<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome数据流验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .flow-diagram {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .order-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
        }
        .form-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Chrome数据流验证测试</h1>
        
        <div class="test-section">
            <h3>🚀 系统初始化验证</h3>
            <button class="test-button" onclick="testSystemInitialization()">测试系统初始化</button>
            <div id="systemInitResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 单订单数据流测试</h3>
            <div class="flow-diagram">
单订单数据流:
用户输入 → 实时分析 → Gemini解析 → 表单填充 → 创建订单 → 成功反馈
  ↓           ↓         ↓          ↓         ↓         ↓
orderInput → debounce → GeminiAI → FormMgr → APISvc → UI反馈
            </div>
            
            <textarea class="order-input" id="singleOrderInput" placeholder="输入单订单测试数据，例如：
王小明 13800138000 
接机服务 KLIA2 到 KL Sentral
明天下午2点 2人 3件行李
航班MH123 价格RM150">王小明 13800138000 
接机服务 KLIA2 到 KL Sentral
明天下午2点 2人 3件行李
航班MH123 价格RM150</textarea>
            
            <div>
                <button class="test-button" onclick="testSingleOrderFlow()">🔍 测试单订单完整流程</button>
                <button class="test-button" onclick="testRealTimeAnalysis()">⚡ 测试实时分析</button>
                <button class="test-button" onclick="testGeminiParsing()">🤖 测试Gemini解析</button>
                <button class="test-button" onclick="testFormFilling()">📋 测试表单填充</button>
            </div>
            
            <div id="singleOrderResults"></div>
        </div>

        <div class="test-section">
            <h3>🔢 多订单数据流测试</h3>
            <div class="flow-diagram">
多订单数据流:
用户输入 → 多订单检测 → 订单分割 → 结构化显示 → 批量创建 → 进度反馈
  ↓           ↓            ↓         ↓            ↓         ↓
orderInput → MultiDetect → GeminiAI → MultiPanel → BatchAPI → Progress
            </div>
            
            <textarea class="order-input" id="multiOrderInput" placeholder="输入多订单测试数据，例如：
订单1: 王小明 13800138000 KLIA2到KL Sentral 明天2点 2人 RM150
订单2: 李小红 13900139000 KL Sentral到KLIA 后天3点 3人 RM200
订单3: 张小刚 13700137000 包车服务 大后天全天 5人 RM500">订单1: 王小明 13800138000 KLIA2到KL Sentral 明天2点 2人 RM150
订单2: 李小红 13900139000 KL Sentral到KLIA 后天3点 3人 RM200
订单3: 张小刚 13700137000 包车服务 大后天全天 5人 RM500</textarea>
            
            <div>
                <button class="test-button" onclick="testMultiOrderFlow()">🔍 测试多订单完整流程</button>
                <button class="test-button" onclick="testMultiOrderDetection()">🔍 测试多订单检测</button>
                <button class="test-button" onclick="testOrderSplitting()">✂️ 测试订单分割</button>
                <button class="test-button" onclick="testBatchCreation()">📦 测试批量创建</button>
            </div>
            
            <div id="multiOrderResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 依赖注入验证</h3>
            <button class="test-button" onclick="testDependencyInjection()">测试依赖注入</button>
            <div id="dependencyResults"></div>
        </div>

        <div class="test-section">
            <h3>🧠 学习引擎验证</h3>
            <button class="test-button" onclick="testLearningEngine()">测试学习引擎</button>
            <div id="learningEngineResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 性能监控验证</h3>
            <button class="test-button" onclick="testPerformanceMonitoring()">测试性能监控</button>
            <div id="performanceResults"></div>
        </div>

        <div class="test-section">
            <h3>🔄 完整集成测试</h3>
            <button class="test-button" onclick="runFullIntegrationTest()">运行完整集成测试</button>
            <div id="integrationResults"></div>
        </div>
    </div>

    <!-- 加载完整的OTA系统 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/paging-service-manager.js"></script>
    <script src="js/multi-select-dropdown.js"></script>
    <script src="js/grid-resizer.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/learning-engine/learning-config.js"></script>
    <script src="js/learning-engine/learning-storage-manager.js"></script>
    <script src="js/learning-engine/user-operation-learner.js"></script>
    <script src="js/learning-engine/error-classification-system.js"></script>
    <script src="js/learning-engine/pattern-matching-engine.js"></script>
    <script src="js/learning-engine/correction-interface.js"></script>
    <script src="js/learning-engine/rule-generation-engine.js"></script>
    <script src="js/learning-engine/system-integration.js"></script>
    <script src="js/learning-engine/ui-correction-manager.js"></script>
    <script src="js/learning-engine/data-persistence-manager.js"></script>
    <script src="js/learning-engine/predictive-corrector.js"></script>
    <script src="js/learning-engine/adaptive-prompt-optimizer.js"></script>
    <script src="js/learning-engine/learning-effectiveness-evaluator.js"></script>
    <script src="js/learning-engine/intelligent-cache-manager.js"></script>
    <script src="js/learning-engine/performance-monitor.js"></script>
    <script src="js/learning-engine/performance-optimizer.js"></script>
    <script src="js/learning-engine/dashboard-manager.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/price-manager.js"></script>
    <script src="js/managers/event-manager.js"></script>
    <script src="js/managers/state-manager.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>
    <script src="js/ui-manager.js"></script>

    <script>
        // 测试工具函数
        function displayResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            element.appendChild(resultDiv);
        }

        function clearResults(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '';
        }

        // 等待系统初始化
        async function waitForSystemInit() {
            if (window.OTA && window.OTA.container) {
                return true;
            }
            return new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    if (window.OTA && window.OTA.container) {
                        clearInterval(checkInterval);
                        resolve(true);
                    }
                }, 100);
            });
        }

        // 测试系统初始化
        async function testSystemInitialization() {
            clearResults('systemInitResults');
            displayResult('systemInitResults', '🚀 开始系统初始化测试...', 'info');
            
            try {
                await waitForSystemInit();
                
                const tests = [
                    { name: '依赖容器', check: () => !!window.OTA.container },
                    { name: '服务定位器', check: () => !!window.OTA.serviceLocator },
                    { name: '应用启动协调器', check: () => !!window.OTA.ApplicationBootstrap },
                    { name: 'getService函数', check: () => typeof window.getService === 'function' },
                    { name: 'Logger服务', check: () => !!getService('logger') },
                    { name: 'AppState服务', check: () => !!getService('appState') },
                    { name: 'GeminiService服务', check: () => !!getService('geminiService') },
                    { name: 'APIService服务', check: () => !!getService('apiService') }
                ];
                
                let passed = 0;
                tests.forEach(test => {
                    try {
                        if (test.check()) {
                            displayResult('systemInitResults', `✅ ${test.name}: 正常`, 'success');
                            passed++;
                        } else {
                            displayResult('systemInitResults', `❌ ${test.name}: 失败`, 'error');
                        }
                    } catch (error) {
                        displayResult('systemInitResults', `❌ ${test.name}: ${error.message}`, 'error');
                    }
                });
                
                displayResult('systemInitResults', `🎯 系统初始化完成: ${passed}/${tests.length} 通过`, 
                    passed === tests.length ? 'success' : 'warning');
                
            } catch (error) {
                displayResult('systemInitResults', `❌ 系统初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试单订单流程
        async function testSingleOrderFlow() {
            clearResults('singleOrderResults');
            displayResult('singleOrderResults', '🔍 开始单订单完整流程测试...', 'info');
            
            try {
                const orderText = document.getElementById('singleOrderInput').value;
                if (!orderText.trim()) {
                    displayResult('singleOrderResults', '❌ 请输入订单文本', 'error');
                    return;
                }
                
                // 步骤1: 实时分析
                displayResult('singleOrderResults', '📝 步骤1: 启动实时分析...', 'info');
                const geminiService = getService('geminiService');
                
                // 步骤2: Gemini解析
                displayResult('singleOrderResults', '🤖 步骤2: Gemini AI解析...', 'info');
                const parseResult = await geminiService.parseOrder(orderText);
                
                if (parseResult.success) {
                    displayResult('singleOrderResults', `✅ 解析成功: 提取到${Object.keys(parseResult.data).length}个字段`, 'success');
                    
                    // 显示解析结果
                    const dataStr = JSON.stringify(parseResult.data, null, 2);
                    displayResult('singleOrderResults', `📊 解析数据:\n${dataStr}`, 'info');
                    
                    // 步骤3: 表单填充（模拟）
                    displayResult('singleOrderResults', '📋 步骤3: 表单填充验证...', 'info');
                    const requiredFields = ['customerName', 'customerContact', 'pickup', 'dropoff'];
                    const foundFields = requiredFields.filter(field => parseResult.data[field]);
                    displayResult('singleOrderResults', `✅ 关键字段检查: ${foundFields.length}/${requiredFields.length} 找到`, 
                        foundFields.length === requiredFields.length ? 'success' : 'warning');
                    
                    // 步骤4: API调用验证（模拟）
                    displayResult('singleOrderResults', '🌐 步骤4: API服务验证...', 'info');
                    const apiService = getService('apiService');
                    if (apiService && typeof apiService.createOrder === 'function') {
                        displayResult('singleOrderResults', '✅ API服务可用', 'success');
                    } else {
                        displayResult('singleOrderResults', '⚠️ API服务未完全配置', 'warning');
                    }
                    
                    displayResult('singleOrderResults', '🎉 单订单流程测试完成', 'success');
                } else {
                    displayResult('singleOrderResults', `❌ 解析失败: ${parseResult.error}`, 'error');
                }
                
            } catch (error) {
                displayResult('singleOrderResults', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试多订单流程
        async function testMultiOrderFlow() {
            clearResults('multiOrderResults');
            displayResult('multiOrderResults', '🔢 开始多订单完整流程测试...', 'info');
            
            try {
                const orderText = document.getElementById('multiOrderInput').value;
                if (!orderText.trim()) {
                    displayResult('multiOrderResults', '❌ 请输入多订单文本', 'error');
                    return;
                }
                
                // 步骤1: 多订单检测
                displayResult('multiOrderResults', '🔍 步骤1: 多订单检测...', 'info');
                const geminiService = getService('geminiService');
                
                const detectionResult = await geminiService.detectAndSplitMultiOrdersWithVerification(orderText);
                
                if (detectionResult.isMultiOrder) {
                    displayResult('multiOrderResults', `✅ 检测到${detectionResult.orderCount}个订单 (置信度: ${detectionResult.confidence})`, 'success');
                    
                    // 步骤2: 订单结构化
                    displayResult('multiOrderResults', '📊 步骤2: 订单结构化分析...', 'info');
                    detectionResult.orders.forEach((order, index) => {
                        const fieldsCount = Object.keys(order).filter(key => order[key] && order[key] !== '').length;
                        displayResult('multiOrderResults', `📋 订单${index + 1}: ${fieldsCount}个字段 - ${order.customerName || '未知客户'}`, 'info');
                    });
                    
                    // 步骤3: 多订单管理器
                    displayResult('multiOrderResults', '🎛️ 步骤3: 多订单管理器验证...', 'info');
                    const multiOrderManager = getService('multiOrderManager');
                    if (multiOrderManager && typeof multiOrderManager.showMultiOrderPanel === 'function') {
                        displayResult('multiOrderResults', '✅ 多订单管理器可用', 'success');
                    } else {
                        displayResult('multiOrderResults', '⚠️ 多订单管理器未完全配置', 'warning');
                    }
                    
                    // 步骤4: 批量创建验证
                    displayResult('multiOrderResults', '📦 步骤4: 批量创建验证...', 'info');
                    displayResult('multiOrderResults', `✅ 准备创建${detectionResult.orderCount}个订单`, 'success');
                    
                    displayResult('multiOrderResults', '🎉 多订单流程测试完成', 'success');
                } else {
                    displayResult('multiOrderResults', '📝 检测结果: 单订单', 'warning');
                }
                
            } catch (error) {
                displayResult('multiOrderResults', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试依赖注入
        async function testDependencyInjection() {
            clearResults('dependencyResults');
            displayResult('dependencyResults', '🎯 开始依赖注入测试...', 'info');
            
            try {
                await waitForSystemInit();
                
                // 测试新的服务定位器模式
                const services = [
                    'logger', 'appState', 'geminiService', 'apiService', 'utils',
                    'multiOrderManager', 'imageUploadManager', 'currencyConverter'
                ];
                
                let passed = 0;
                services.forEach(serviceName => {
                    try {
                        const service = getService(serviceName);
                        if (service) {
                            displayResult('dependencyResults', `✅ ${serviceName}: 可用`, 'success');
                            passed++;
                        } else {
                            displayResult('dependencyResults', `❌ ${serviceName}: 不可用`, 'error');
                        }
                    } catch (error) {
                        displayResult('dependencyResults', `❌ ${serviceName}: ${error.message}`, 'error');
                    }
                });
                
                // 测试容器诊断
                const diagnosis = window.OTA.container.diagnose();
                displayResult('dependencyResults', `🏥 容器健康状态: ${diagnosis.healthy ? '健康' : '有问题'}`, 
                    diagnosis.healthy ? 'success' : 'warning');
                
                displayResult('dependencyResults', `🎯 依赖注入测试完成: ${passed}/${services.length} 通过`, 
                    passed === services.length ? 'success' : 'warning');
                
            } catch (error) {
                displayResult('dependencyResults', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试学习引擎
        async function testLearningEngine() {
            clearResults('learningEngineResults');
            displayResult('learningEngineResults', '🧠 开始学习引擎测试...', 'info');
            
            try {
                const learningServices = [
                    'learningConfig', 'learningStorageManager', 'userOperationLearner',
                    'errorClassificationSystem', 'patternMatchingEngine', 'correctionInterface'
                ];
                
                let passed = 0;
                learningServices.forEach(serviceName => {
                    try {
                        const service = getService(serviceName);
                        if (service) {
                            displayResult('learningEngineResults', `✅ ${serviceName}: 可用`, 'success');
                            passed++;
                        } else {
                            displayResult('learningEngineResults', `❌ ${serviceName}: 不可用`, 'error');
                        }
                    } catch (error) {
                        displayResult('learningEngineResults', `❌ ${serviceName}: ${error.message}`, 'error');
                    }
                });
                
                // 测试学习配置
                const learningConfig = getService('learningConfig');
                if (learningConfig && typeof learningConfig.validate === 'function') {
                    const validation = learningConfig.validate();
                    displayResult('learningEngineResults', `🔧 学习配置验证: ${validation.valid ? '有效' : '无效'}`, 
                        validation.valid ? 'success' : 'warning');
                }
                
                displayResult('learningEngineResults', `🧠 学习引擎测试完成: ${passed}/${learningServices.length} 通过`, 
                    passed === learningServices.length ? 'success' : 'warning');
                
            } catch (error) {
                displayResult('learningEngineResults', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试性能监控
        async function testPerformanceMonitoring() {
            clearResults('performanceResults');
            displayResult('performanceResults', '📊 开始性能监控测试...', 'info');
            
            try {
                // 测试监控系统
                if (window.monitoring) {
                    displayResult('performanceResults', '✅ 全局监控系统可用', 'success');
                    
                    // 测试监控报告
                    if (typeof window.monitoring.report === 'function') {
                        displayResult('performanceResults', '✅ 监控报告功能可用', 'success');
                    }
                    
                    // 测试实时监控
                    if (typeof window.monitoring.setRealTime === 'function') {
                        displayResult('performanceResults', '✅ 实时监控功能可用', 'success');
                    }
                } else {
                    displayResult('performanceResults', '⚠️ 全局监控系统未完全配置', 'warning');
                }
                
                // 测试性能指标
                const logger = getService('logger');
                if (logger && logger.monitoring) {
                    displayResult('performanceResults', '✅ Logger监控系统可用', 'success');
                }
                
                // 测试内存使用
                if (window.performance && window.performance.memory) {
                    const memory = window.performance.memory;
                    const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                    displayResult('performanceResults', `📊 内存使用: ${memoryUsage}MB`, 'info');
                }
                
                displayResult('performanceResults', '📊 性能监控测试完成', 'success');
                
            } catch (error) {
                displayResult('performanceResults', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 运行完整集成测试
        async function runFullIntegrationTest() {
            clearResults('integrationResults');
            displayResult('integrationResults', '🔄 开始完整集成测试...', 'info');
            
            try {
                // 按顺序运行所有测试
                displayResult('integrationResults', '1️⃣ 系统初始化测试...', 'info');
                await testSystemInitialization();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                displayResult('integrationResults', '2️⃣ 依赖注入测试...', 'info');
                await testDependencyInjection();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                displayResult('integrationResults', '3️⃣ 学习引擎测试...', 'info');
                await testLearningEngine();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                displayResult('integrationResults', '4️⃣ 性能监控测试...', 'info');
                await testPerformanceMonitoring();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                displayResult('integrationResults', '5️⃣ 单订单流程测试...', 'info');
                await testSingleOrderFlow();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                displayResult('integrationResults', '6️⃣ 多订单流程测试...', 'info');
                await testMultiOrderFlow();
                
                // 统计测试结果
                const allResults = document.querySelectorAll('.test-result');
                const successCount = document.querySelectorAll('.test-result.success').length;
                const errorCount = document.querySelectorAll('.test-result.error').length;
                const warningCount = document.querySelectorAll('.test-result.warning').length;
                
                displayResult('integrationResults', 
                    `🎯 完整集成测试完成!\n✅ 成功: ${successCount}\n❌ 失败: ${errorCount}\n⚠️ 警告: ${warningCount}`, 
                    errorCount === 0 ? 'success' : 'warning'
                );
                
            } catch (error) {
                displayResult('integrationResults', `❌ 集成测试失败: ${error.message}`, 'error');
            }
        }

        // 简化的测试函数
        function testRealTimeAnalysis() {
            displayResult('singleOrderResults', '⚡ 实时分析测试: 已启动输入监听', 'info');
        }

        function testGeminiParsing() {
            displayResult('singleOrderResults', '🤖 Gemini解析测试: 参见完整流程测试', 'info');
        }

        function testFormFilling() {
            displayResult('singleOrderResults', '📋 表单填充测试: 参见完整流程测试', 'info');
        }

        function testMultiOrderDetection() {
            displayResult('multiOrderResults', '🔍 多订单检测测试: 参见完整流程测试', 'info');
        }

        function testOrderSplitting() {
            displayResult('multiOrderResults', '✂️ 订单分割测试: 参见完整流程测试', 'info');
        }

        function testBatchCreation() {
            displayResult('multiOrderResults', '📦 批量创建测试: 参见完整流程测试', 'info');
        }

        // 页面加载时自动运行系统初始化测试
        window.addEventListener('load', function() {
            setTimeout(testSystemInitialization, 1000);
        });
    </script>
</body>
</html>