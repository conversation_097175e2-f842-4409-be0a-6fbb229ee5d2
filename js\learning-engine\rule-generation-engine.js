/**
 * 智能学习型格式预处理引擎 - 学习规则生成引擎
 * 负责从用户操作中生成学习规则，管理规则冲突和优先级
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getLearningConfig() {
        return getService('learningConfig');
    }

    function getLearningStorageManager() {
        return getService('learningStorageManager');
    }

    function getUserOperationLearner() {
        return getService('userOperationLearner');
    }

    function getPatternMatchingEngine() {
        return getService('patternMatchingEngine');
    }

    function getLogger() {
        return getService('logger');
    }

    /**
     * 学习规则生成引擎类
     * 从用户操作中生成和管理学习规则
     */
    class RuleGenerationEngine {
        constructor() {
            this.config = getLearningConfig();
            this.storageManager = getLearningStorageManager();
            this.operationLearner = getUserOperationLearner();
            this.patternMatcher = getPatternMatchingEngine();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            this.ruleConfig = this.config.get('ruleGeneration');
            
            // 规则缓存
            this.ruleCache = new Map();
            this.conflictCache = new Map();
            
            // 规则统计
            this.ruleStats = {
                totalRules: 0,
                activeRules: 0,
                conflictingRules: 0,
                lastGenerated: null
            };

            this.initialize();
        }

        /**
         * 初始化规则生成引擎
         */
        initialize() {
            try {
                // 加载现有规则
                this.loadExistingRules();
                
                this.logger?.log('学习规则生成引擎初始化完成', 'info', {
                    version: this.version,
                    totalRules: this.ruleStats.totalRules,
                    ruleTypes: Object.keys(this.ruleConfig.ruleTypes)
                });
            } catch (error) {
                this.logger?.logError('学习规则生成引擎初始化失败', error);
            }
        }

        /**
         * 加载现有规则
         */
        loadExistingRules() {
            try {
                const storageKey = this.config.get('storage.keys.learningRules');
                const rulesData = this.storageManager.getData(storageKey);
                
                if (rulesData && rulesData.rules) {
                    rulesData.rules.forEach(rule => {
                        this.ruleCache.set(rule.id, rule);
                    });
                    
                    this.ruleStats.totalRules = rulesData.rules.length;
                    this.ruleStats.activeRules = rulesData.rules.filter(r => r.active !== false).length;
                }
            } catch (error) {
                this.logger?.logError('加载现有规则失败', error);
            }
        }

        /**
         * 生成更正规则
         * @param {Array} operations - 用户操作数组
         * @returns {Object} 规则生成结果
         */
        generateCorrectionRule(operations) {
            try {
                if (!operations || operations.length < this.ruleConfig.minOccurrences) {
                    return this.createEmptyResult('操作数量不足，无法生成规则');
                }

                // 分析操作模式
                const patternAnalysis = this.analyzeOperationPatterns(operations);
                
                // 生成规则候选
                const ruleCandidates = this.generateRuleCandidates(patternAnalysis);
                
                // 验证和筛选规则
                const validRules = this.validateRuleCandidates(ruleCandidates);
                
                // 检测冲突
                const conflictAnalysis = this.detectRuleConflicts(validRules);
                
                // 应用优先级
                const prioritizedRules = this.applyRulePriority(validRules, conflictAnalysis);
                
                // 保存规则
                const savedRules = this.saveGeneratedRules(prioritizedRules);
                
                // 更新统计
                this.updateRuleStats(savedRules);

                return {
                    success: true,
                    generatedRules: savedRules,
                    patternAnalysis: patternAnalysis,
                    conflictAnalysis: conflictAnalysis,
                    timestamp: new Date().toISOString()
                };

            } catch (error) {
                this.logger?.logError('生成更正规则失败', error);
                return this.createEmptyResult(error.message);
            }
        }

        /**
         * 分析操作模式
         */
        analyzeOperationPatterns(operations) {
            const analysis = {
                fieldPatterns: {},
                valuePatterns: {},
                contextPatterns: {},
                frequency: {}
            };

            // 按字段分组分析
            const fieldGroups = this.groupOperationsByField(operations);
            
            Object.entries(fieldGroups).forEach(([field, fieldOps]) => {
                analysis.fieldPatterns[field] = this.analyzeFieldPatterns(fieldOps);
                analysis.valuePatterns[field] = this.analyzeValuePatterns(fieldOps);
                analysis.contextPatterns[field] = this.analyzeContextPatterns(fieldOps);
                analysis.frequency[field] = fieldOps.length;
            });

            return analysis;
        }

        /**
         * 按字段分组操作
         */
        groupOperationsByField(operations) {
            const groups = {};
            
            operations.forEach(op => {
                if (!groups[op.field]) {
                    groups[op.field] = [];
                }
                groups[op.field].push(op);
            });

            return groups;
        }

        /**
         * 分析字段模式
         */
        analyzeFieldPatterns(operations) {
            const patterns = {
                commonTransformations: [],
                errorTypes: {},
                confidenceDistribution: []
            };

            operations.forEach(op => {
                // 分析转换模式
                const transformation = this.identifyTransformation(op.originalValue, op.correctedValue);
                patterns.commonTransformations.push(transformation);

                // 统计错误类型
                const errorType = op.metadata?.errorType || 'unknown';
                patterns.errorTypes[errorType] = (patterns.errorTypes[errorType] || 0) + 1;

                // 收集置信度
                patterns.confidenceDistribution.push(op.confidence || 0.5);
            });

            return patterns;
        }

        /**
         * 识别转换模式
         */
        identifyTransformation(original, corrected) {
            // 格式转换
            if (this.isFormatTransformation(original, corrected)) {
                return {
                    type: 'format_transformation',
                    pattern: this.extractFormatPattern(original, corrected)
                };
            }

            // 值映射
            if (this.isValueMapping(original, corrected)) {
                return {
                    type: 'value_mapping',
                    mapping: { [original]: corrected }
                };
            }

            // 文本清理
            if (this.isTextCleaning(original, corrected)) {
                return {
                    type: 'text_cleaning',
                    operation: this.identifyCleaningOperation(original, corrected)
                };
            }

            return {
                type: 'unknown_transformation',
                original: original,
                corrected: corrected
            };
        }

        /**
         * 检查是否为格式转换
         */
        isFormatTransformation(original, corrected) {
            // 日期格式转换
            const datePatterns = [
                /\d{4}-\d{2}-\d{2}/,
                /\d{2}\/\d{2}\/\d{4}/,
                /\d{2}-\d{2}-\d{4}/
            ];

            const originalIsDate = datePatterns.some(p => p.test(original));
            const correctedIsDate = datePatterns.some(p => p.test(corrected));

            return originalIsDate && correctedIsDate;
        }

        /**
         * 检查是否为值映射
         */
        isValueMapping(original, corrected) {
            // 简单的字符串替换
            return original !== corrected && 
                   original.length < 50 && 
                   corrected.length < 50 &&
                   !this.isFormatTransformation(original, corrected);
        }

        /**
         * 检查是否为文本清理
         */
        isTextCleaning(original, corrected) {
            // 去除空格、特殊字符等
            return original.replace(/\s+/g, '').replace(/[^\w]/g, '') === 
                   corrected.replace(/\s+/g, '').replace(/[^\w]/g, '');
        }

        /**
         * 分析值模式
         */
        analyzeValuePatterns(operations) {
            const patterns = {
                commonValues: {},
                lengthDistribution: [],
                characterPatterns: {}
            };

            operations.forEach(op => {
                // 统计常见值
                patterns.commonValues[op.correctedValue] = 
                    (patterns.commonValues[op.correctedValue] || 0) + 1;

                // 长度分布
                patterns.lengthDistribution.push(op.correctedValue.length);

                // 字符模式
                const charPattern = this.extractCharacterPattern(op.correctedValue);
                patterns.characterPatterns[charPattern] = 
                    (patterns.characterPatterns[charPattern] || 0) + 1;
            });

            return patterns;
        }

        /**
         * 提取字符模式
         */
        extractCharacterPattern(value) {
            return value
                .replace(/\d/g, 'N')
                .replace(/[a-zA-Z]/g, 'A')
                .replace(/\s/g, 'S')
                .replace(/[^\w\s]/g, 'P');
        }

        /**
         * 分析上下文模式
         */
        analyzeContextPatterns(operations) {
            const patterns = {
                commonContexts: {},
                sessionPatterns: {},
                timePatterns: {}
            };

            operations.forEach(op => {
                // 上下文信息
                if (op.context) {
                    const contextKey = JSON.stringify(op.context);
                    patterns.commonContexts[contextKey] = 
                        (patterns.commonContexts[contextKey] || 0) + 1;
                }

                // 会话模式
                if (op.context?.sessionId) {
                    patterns.sessionPatterns[op.context.sessionId] = 
                        (patterns.sessionPatterns[op.context.sessionId] || 0) + 1;
                }

                // 时间模式
                const hour = new Date(op.timestamp).getHours();
                patterns.timePatterns[hour] = (patterns.timePatterns[hour] || 0) + 1;
            });

            return patterns;
        }

        /**
         * 生成规则候选
         */
        generateRuleCandidates(patternAnalysis) {
            const candidates = [];

            Object.entries(patternAnalysis.fieldPatterns).forEach(([field, patterns]) => {
                // 生成格式转换规则
                const formatRules = this.generateFormatRules(field, patterns);
                candidates.push(...formatRules);

                // 生成值映射规则
                const mappingRules = this.generateMappingRules(field, patterns);
                candidates.push(...mappingRules);

                // 生成上下文规则
                const contextRules = this.generateContextRules(field, patternAnalysis.contextPatterns[field]);
                candidates.push(...contextRules);
            });

            return candidates;
        }

        /**
         * 生成格式转换规则
         */
        generateFormatRules(field, patterns) {
            const rules = [];

            patterns.commonTransformations
                .filter(t => t.type === 'format_transformation')
                .forEach(transformation => {
                    rules.push({
                        id: this.generateRuleId(),
                        type: this.ruleConfig.ruleTypes.REGEX_PATTERN,
                        field: field,
                        pattern: transformation.pattern,
                        priority: this.ruleConfig.priority.MEDIUM,
                        confidence: this.calculateTransformationConfidence(transformation),
                        metadata: {
                            transformationType: 'format',
                            generated: new Date().toISOString()
                        }
                    });
                });

            return rules;
        }

        /**
         * 生成值映射规则
         */
        generateMappingRules(field, patterns) {
            const rules = [];

            patterns.commonTransformations
                .filter(t => t.type === 'value_mapping')
                .forEach(transformation => {
                    rules.push({
                        id: this.generateRuleId(),
                        type: this.ruleConfig.ruleTypes.VALUE_MAPPING,
                        field: field,
                        mapping: transformation.mapping,
                        priority: this.ruleConfig.priority.HIGH,
                        confidence: this.calculateTransformationConfidence(transformation),
                        metadata: {
                            transformationType: 'mapping',
                            generated: new Date().toISOString()
                        }
                    });
                });

            return rules;
        }

        /**
         * 生成上下文规则
         */
        generateContextRules(field, contextPatterns) {
            const rules = [];

            if (contextPatterns && Object.keys(contextPatterns.commonContexts).length > 0) {
                rules.push({
                    id: this.generateRuleId(),
                    type: this.ruleConfig.ruleTypes.CONTEXT_RULE,
                    field: field,
                    contextConditions: contextPatterns.commonContexts,
                    priority: this.ruleConfig.priority.LOW,
                    confidence: 0.6,
                    metadata: {
                        transformationType: 'context',
                        generated: new Date().toISOString()
                    }
                });
            }

            return rules;
        }

        /**
         * 生成规则ID
         */
        generateRuleId() {
            return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 计算转换置信度
         */
        calculateTransformationConfidence(transformation) {
            let confidence = 0.5;

            // 基于转换类型调整
            switch (transformation.type) {
                case 'format_transformation':
                    confidence = 0.8;
                    break;
                case 'value_mapping':
                    confidence = 0.9;
                    break;
                case 'text_cleaning':
                    confidence = 0.7;
                    break;
                default:
                    confidence = 0.5;
            }

            return confidence;
        }

        /**
         * 验证规则候选
         */
        validateRuleCandidates(candidates) {
            return candidates.filter(rule => {
                // 基本验证
                if (!rule.id || !rule.type || !rule.field) {
                    return false;
                }

                // 置信度验证
                if (rule.confidence < 0.5) {
                    return false;
                }

                // 类型特定验证
                return this.validateRuleByType(rule);
            });
        }

        /**
         * 按类型验证规则
         */
        validateRuleByType(rule) {
            switch (rule.type) {
                case this.ruleConfig.ruleTypes.REGEX_PATTERN:
                    return rule.pattern && typeof rule.pattern === 'object';
                case this.ruleConfig.ruleTypes.VALUE_MAPPING:
                    return rule.mapping && typeof rule.mapping === 'object';
                case this.ruleConfig.ruleTypes.CONTEXT_RULE:
                    return rule.contextConditions && typeof rule.contextConditions === 'object';
                default:
                    return true;
            }
        }

        /**
         * 检测规则冲突
         */
        detectRuleConflicts(rules) {
            const conflicts = [];
            const fieldGroups = {};

            // 按字段分组
            rules.forEach(rule => {
                if (!fieldGroups[rule.field]) {
                    fieldGroups[rule.field] = [];
                }
                fieldGroups[rule.field].push(rule);
            });

            // 检测每个字段内的冲突
            Object.entries(fieldGroups).forEach(([field, fieldRules]) => {
                const fieldConflicts = this.detectFieldRuleConflicts(fieldRules);
                conflicts.push(...fieldConflicts);
            });

            return {
                hasConflicts: conflicts.length > 0,
                conflicts: conflicts,
                conflictCount: conflicts.length
            };
        }

        /**
         * 检测字段规则冲突
         */
        detectFieldRuleConflicts(rules) {
            const conflicts = [];

            for (let i = 0; i < rules.length; i++) {
                for (let j = i + 1; j < rules.length; j++) {
                    const conflict = this.checkRuleConflict(rules[i], rules[j]);
                    if (conflict) {
                        conflicts.push(conflict);
                    }
                }
            }

            return conflicts;
        }

        /**
         * 检查两个规则是否冲突
         */
        checkRuleConflict(rule1, rule2) {
            // 同类型规则可能冲突
            if (rule1.type === rule2.type && rule1.field === rule2.field) {
                return {
                    type: 'same_type_conflict',
                    rule1: rule1.id,
                    rule2: rule2.id,
                    field: rule1.field,
                    severity: 'medium'
                };
            }

            // 值映射冲突
            if (rule1.type === this.ruleConfig.ruleTypes.VALUE_MAPPING &&
                rule2.type === this.ruleConfig.ruleTypes.VALUE_MAPPING) {
                const overlap = this.checkMappingOverlap(rule1.mapping, rule2.mapping);
                if (overlap) {
                    return {
                        type: 'mapping_conflict',
                        rule1: rule1.id,
                        rule2: rule2.id,
                        field: rule1.field,
                        overlap: overlap,
                        severity: 'high'
                    };
                }
            }

            return null;
        }

        /**
         * 检查映射重叠
         */
        checkMappingOverlap(mapping1, mapping2) {
            const keys1 = Object.keys(mapping1);
            const keys2 = Object.keys(mapping2);
            
            const overlap = keys1.filter(key => keys2.includes(key));
            return overlap.length > 0 ? overlap : null;
        }

        /**
         * 应用规则优先级
         */
        applyRulePriority(rules, conflictAnalysis) {
            if (!conflictAnalysis.hasConflicts) {
                return rules;
            }

            const prioritizedRules = [...rules];

            // 处理冲突
            conflictAnalysis.conflicts.forEach(conflict => {
                const rule1 = prioritizedRules.find(r => r.id === conflict.rule1);
                const rule2 = prioritizedRules.find(r => r.id === conflict.rule2);

                if (rule1 && rule2) {
                    // 根据优先级和置信度决定保留哪个规则
                    const keepRule1 = this.shouldKeepRule(rule1, rule2);
                    const ruleToRemove = keepRule1 ? rule2 : rule1;
                    
                    const index = prioritizedRules.findIndex(r => r.id === ruleToRemove.id);
                    if (index > -1) {
                        prioritizedRules.splice(index, 1);
                    }
                }
            });

            return prioritizedRules;
        }

        /**
         * 判断是否应该保留规则1
         */
        shouldKeepRule(rule1, rule2) {
            // 优先级比较
            if (rule1.priority !== rule2.priority) {
                return rule1.priority > rule2.priority;
            }

            // 置信度比较
            return rule1.confidence >= rule2.confidence;
        }

        /**
         * 保存生成的规则
         */
        saveGeneratedRules(rules) {
            try {
                const storageKey = this.config.get('storage.keys.learningRules');
                const existingData = this.storageManager.getData(storageKey) || {
                    version: '1.0.0',
                    rules: [],
                    metadata: { totalRules: 0 }
                };

                // 添加新规则
                const savedRules = [];
                rules.forEach(rule => {
                    // 设置默认属性
                    rule.created = new Date().toISOString();
                    rule.active = true;
                    rule.usageCount = 0;
                    rule.successRate = 0;

                    existingData.rules.push(rule);
                    this.ruleCache.set(rule.id, rule);
                    savedRules.push(rule);
                });

                // 限制规则数量
                const maxRules = this.ruleConfig.maxRulesPerField * 10; // 假设最多10个字段
                if (existingData.rules.length > maxRules) {
                    existingData.rules = existingData.rules.slice(-maxRules);
                }

                // 更新元数据
                existingData.metadata.totalRules = existingData.rules.length;
                existingData.metadata.lastGenerated = new Date().toISOString();

                // 保存到存储
                this.storageManager.setData(storageKey, existingData);

                return savedRules;

            } catch (error) {
                this.logger?.logError('保存生成的规则失败', error);
                return [];
            }
        }

        /**
         * 更新规则统计
         */
        updateRuleStats(savedRules) {
            this.ruleStats.totalRules += savedRules.length;
            this.ruleStats.activeRules += savedRules.length;
            this.ruleStats.lastGenerated = new Date().toISOString();
        }

        /**
         * 创建空结果
         */
        createEmptyResult(message) {
            return {
                success: false,
                message: message,
                generatedRules: [],
                patternAnalysis: null,
                conflictAnalysis: null,
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 获取规则统计
         */
        getRuleStats() {
            return { ...this.ruleStats };
        }

        /**
         * 获取所有规则
         */
        getAllRules() {
            return Array.from(this.ruleCache.values());
        }

        /**
         * 根据字段获取规则
         */
        getRulesByField(field) {
            return Array.from(this.ruleCache.values()).filter(rule => rule.field === field);
        }

        /**
         * 清理规则缓存
         */
        clearRuleCache() {
            this.ruleCache.clear();
            this.conflictCache.clear();
            this.logger?.log('规则缓存已清理', 'info');
        }
    }

    // 创建全局实例
    const ruleGenerationEngine = new RuleGenerationEngine();

    // 导出到全局命名空间
    window.OTA.ruleGenerationEngine = ruleGenerationEngine;
    window.ruleGenerationEngine = ruleGenerationEngine; // 向后兼容

    // 工厂函数
    window.getRuleGenerationEngine = function() {
        return window.OTA.ruleGenerationEngine || window.ruleGenerationEngine;
    };

    console.log('学习规则生成引擎加载完成', {
        version: ruleGenerationEngine.version,
        totalRules: ruleGenerationEngine.ruleStats.totalRules,
        ruleTypes: Object.keys(ruleGenerationEngine.ruleConfig.ruleTypes)
    });

})();
