/**
 * 智能学习型格式预处理引擎 - 模块加载器
 * 提供动态模块加载、预编译和按需加载功能
 * 优化系统启动时间和内存使用
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-18
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 容器优先的服务访问函数
    function getServiceFromContainer(name) {
        return window.OTA.container ? window.OTA.container.get(name) : getService(name);
    }

    function getLogger() {
        return getServiceFromContainer('logger');
    }

    // 服务定位器函数 - 向后兼容
    function getService(serviceName) {
        const serviceMap = {
            'logger': () => window.OTA.logger || window.logger
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 模块加载器类
     * 提供智能的模块加载和预编译功能
     */
    class ModuleLoader {
        constructor() {
            this.logger = getLogger();
            this.version = '1.0.0';
            
            // 模块配置
            this.moduleConfig = {
                basePath: 'js/learning-engine/',
                preloadStrategy: 'lazy', // 'eager', 'lazy', 'smart'
                cacheStrategy: 'memory', // 'memory', 'session', 'persistent'
                maxModules: 20,
                preloadDelay: 1000, // 预加载延迟(ms)
                cleanupInterval: 30 * 60 * 1000 // 30分钟清理一次
            };
            
            // 模块缓存
            this.loadedModules = new Map();
            this.loadingModules = new Map();
            this.moduleDependencies = new Map();
            this.preloadQueue = [];
            this.moduleStats = {
                loaded: 0,
                failed: 0,
                cached: 0,
                memorySaved: 0
            };
            
            // 模块定义
            this.moduleDefinitions = this.initializeModuleDefinitions();
            
            this.initialize();
        }

        /**
         * 初始化模块加载器
         */
        initialize() {
            try {
                this.setupModuleDependencies();
                this.setupPreloadStrategy();
                this.setupCleanupTimer();
                
                this.logger?.log('模块加载器初始化完成', 'info', {
                    version: this.version,
                    preloadStrategy: this.moduleConfig.preloadStrategy,
                    totalModules: Object.keys(this.moduleDefinitions).length
                });
            } catch (error) {
                this.logger?.logError('模块加载器初始化失败', error);
            }
        }

        /**
         * 初始化模块定义
         */
        initializeModuleDefinitions() {
            return {
                'learning-config': {
                    path: 'learning-config.js',
                    dependencies: [],
                    priority: 1,
                    preload: true,
                    size: 1024
                },
                'learning-storage-manager': {
                    path: 'learning-storage-manager.js',
                    dependencies: ['learning-config'],
                    priority: 2,
                    preload: true,
                    size: 2048
                },
                'user-operation-learner': {
                    path: 'user-operation-learner.js',
                    dependencies: ['learning-config', 'learning-storage-manager'],
                    priority: 3,
                    preload: true,
                    size: 3072
                },
                'error-classification-system': {
                    path: 'error-classification-system.js',
                    dependencies: ['learning-config', 'user-operation-learner'],
                    priority: 4,
                    preload: false,
                    size: 2560
                },
                'pattern-matching-engine': {
                    path: 'pattern-matching-engine.js',
                    dependencies: ['learning-config'],
                    priority: 5,
                    preload: false,
                    size: 4096
                },
                'correction-interface': {
                    path: 'correction-interface.js',
                    dependencies: ['pattern-matching-engine', 'user-operation-learner'],
                    priority: 6,
                    preload: false,
                    size: 2048
                },
                'rule-generation-engine': {
                    path: 'rule-generation-engine.js',
                    dependencies: ['pattern-matching-engine', 'user-operation-learner'],
                    priority: 7,
                    preload: false,
                    size: 3584
                },
                'predictive-corrector': {
                    path: 'predictive-corrector.js',
                    dependencies: ['rule-generation-engine', 'error-classification-system'],
                    priority: 8,
                    preload: false,
                    size: 3072
                },
                'performance-monitor': {
                    path: 'performance-monitor.js',
                    dependencies: ['learning-config'],
                    priority: 9,
                    preload: false,
                    size: 1792
                },
                'intelligent-cache-manager': {
                    path: 'intelligent-cache-manager.js',
                    dependencies: ['learning-config', 'pattern-matching-engine'],
                    priority: 10,
                    preload: false,
                    size: 4608
                },
                'adaptive-prompt-optimizer': {
                    path: 'adaptive-prompt-optimizer.js',
                    dependencies: ['intelligent-cache-manager', 'user-operation-learner'],
                    priority: 11,
                    preload: false,
                    size: 2560
                },
                'learning-effectiveness-evaluator': {
                    path: 'learning-effectiveness-evaluator.js',
                    dependencies: ['performance-monitor', 'user-operation-learner'],
                    priority: 12,
                    preload: false,
                    size: 2048
                },
                'system-integration': {
                    path: 'system-integration.js',
                    dependencies: [
                        'learning-config',
                        'learning-storage-manager',
                        'user-operation-learner',
                        'error-classification-system',
                        'pattern-matching-engine'
                    ],
                    priority: 13,
                    preload: false,
                    size: 3072
                },
                'ui-correction-manager': {
                    path: 'ui-correction-manager.js',
                    dependencies: ['correction-interface', 'system-integration'],
                    priority: 14,
                    preload: false,
                    size: 2048
                },
                'data-persistence-manager': {
                    path: 'data-persistence-manager.js',
                    dependencies: ['learning-storage-manager', 'system-integration'],
                    priority: 15,
                    preload: false,
                    size: 3584
                },
                'performance-optimizer': {
                    path: 'performance-optimizer.js',
                    dependencies: [
                        'intelligent-cache-manager',
                        'performance-monitor',
                        'user-operation-learner',
                        'rule-generation-engine'
                    ],
                    priority: 16,
                    preload: false,
                    size: 4096
                },
                'dashboard-manager': {
                    path: 'dashboard-manager.js',
                    dependencies: [
                        'performance-monitor',
                        'learning-effectiveness-evaluator',
                        'data-persistence-manager'
                    ],
                    priority: 17,
                    preload: false,
                    size: 3072
                }
            };
        }

        /**
         * 设置模块依赖关系
         */
        setupModuleDependencies() {
            Object.entries(this.moduleDefinitions).forEach(([moduleName, config]) => {
                this.moduleDependencies.set(moduleName, config.dependencies);
            });
        }

        /**
         * 设置预加载策略
         */
        setupPreloadStrategy() {
            switch (this.moduleConfig.preloadStrategy) {
                case 'eager':
                    this.preloadAllModules();
                    break;
                case 'lazy':
                    // 懒加载，按需加载
                    break;
                case 'smart':
                    this.setupSmartPreloading();
                    break;
            }
        }

        /**
         * 设置智能预加载
         */
        setupSmartPreloading() {
            // 基于用户行为模式进行智能预加载
            setTimeout(() => {
                this.analyzeUserBehavior();
            }, this.moduleConfig.preloadDelay);
        }

        /**
         * 分析用户行为并预加载相关模块
         */
        analyzeUserBehavior() {
            try {
                // 获取用户操作数据
                const operationLearner = window.OTA?.userOperationLearner;
                if (!operationLearner) return;

                const operations = operationLearner.getRecentOperations(10);
                const commonFields = this.identifyCommonFields(operations);
                
                // 基于常用字段预加载相关模块
                const modulesToPreload = this.selectModulesByFields(commonFields);
                this.preloadModules(modulesToPreload);

            } catch (error) {
                this.logger?.logError('用户行为分析失败', error);
            }
        }

        /**
         * 识别常用字段
         */
        identifyCommonFields(operations) {
            const fieldCount = {};
            
            operations.forEach(op => {
                if (op.field) {
                    fieldCount[op.field] = (fieldCount[op.field] || 0) + 1;
                }
            });
            
            return Object.entries(fieldCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 3)
                .map(entry => entry[0]);
        }

        /**
         * 根据字段选择要预加载的模块
         */
        selectModulesByFields(fields) {
            const moduleMap = {
                'customerName': ['pattern-matching-engine', 'correction-interface'],
                'pickup': ['pattern-matching-engine', 'rule-generation-engine'],
                'dropoff': ['pattern-matching-engine', 'rule-generation-engine'],
                'pickupTime': ['error-classification-system', 'predictive-corrector'],
                'passengerCount': ['error-classification-system'],
                'otaPrice': ['error-classification-system', 'predictive-corrector']
            };

            const selectedModules = new Set();
            fields.forEach(field => {
                if (moduleMap[field]) {
                    moduleMap[field].forEach(module => selectedModules.add(module));
                }
            });

            return Array.from(selectedModules);
        }

        /**
         * 动态加载模块
         */
        async loadModule(moduleName, forceReload = false) {
            try {
                // 检查是否已加载
                if (this.loadedModules.has(moduleName) && !forceReload) {
                    return this.loadedModules.get(moduleName);
                }

                // 检查是否正在加载
                if (this.loadingModules.has(moduleName)) {
                    return this.loadingModules.get(moduleName);
                }

                const moduleDef = this.moduleDefinitions[moduleName];
                if (!moduleDef) {
                    throw new Error(`模块未定义: ${moduleName}`);
                }

                // 创建加载Promise
                const loadPromise = this.performModuleLoad(moduleName, moduleDef);
                this.loadingModules.set(moduleName, loadPromise);

                const result = await loadPromise;
                this.loadedModules.set(moduleName, result);
                this.loadingModules.delete(moduleName);

                this.moduleStats.loaded++;
                this.logger?.log('模块加载完成', 'info', {
                    module: moduleName,
                    size: moduleDef.size,
                    dependencies: moduleDef.dependencies
                });

                return result;

            } catch (error) {
                this.moduleStats.failed++;
                this.loadingModules.delete(moduleName);
                this.logger?.logError('模块加载失败', { module: moduleName, error: error.message });
                throw error;
            }
        }

        /**
         * 执行模块加载
         */
        async performModuleLoad(moduleName, moduleDef) {
            // 先加载依赖模块
            if (moduleDef.dependencies.length > 0) {
                const dependencyPromises = moduleDef.dependencies.map(dep => 
                    this.loadModule(dep)
                );
                await Promise.all(dependencyPromises);
            }

            // 使用动态脚本加载
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = this.moduleConfig.basePath + moduleDef.path;
                script.async = true;
                
                script.onload = () => {
                    // 等待全局变量设置完成
                    setTimeout(() => {
                        const moduleInstance = window.OTA[moduleName] || 
                                            window[moduleName] || 
                                            window[`get${this.capitalizeFirst(moduleName)}`]?.();
                        resolve(moduleInstance);
                    }, 10);
                };
                
                script.onerror = () => reject(new Error(`Failed to load module: ${moduleName}`));
                
                document.head.appendChild(script);
            });
        }

        /**
         * 预加载多个模块
         */
        async preloadModules(moduleNames) {
            const promises = moduleNames.map(name => this.loadModule(name));
            return Promise.allSettled(promises);
        }

        /**
         * 预加载所有模块
         */
        async preloadAllModules() {
            const moduleNames = Object.keys(this.moduleDefinitions);
            return this.preloadModules(moduleNames);
        }

        /**
         * 卸载模块
         */
        unloadModule(moduleName) {
            if (this.loadedModules.has(moduleName)) {
                this.loadedModules.delete(moduleName);
                this.moduleStats.memorySaved += this.moduleDefinitions[moduleName]?.size || 0;
                
                this.logger?.log('模块已卸载', 'debug', { module: moduleName });
                return true;
            }
            return false;
        }

        /**
         * 获取模块状态
         */
        getModuleStatus(moduleName) {
            if (this.loadedModules.has(moduleName)) {
                return 'loaded';
            } else if (this.loadingModules.has(moduleName)) {
                return 'loading';
            } else {
                return 'not_loaded';
            }
        }

        /**
         * 获取加载统计
         */
        getLoadingStats() {
            return {
                ...this.moduleStats,
                loadedModules: Array.from(this.loadedModules.keys()),
                loadingModules: Array.from(this.loadingModules.keys()),
                totalModules: Object.keys(this.moduleDefinitions).length,
                memoryUsage: this.calculateMemoryUsage()
            };
        }

        /**
         * 计算内存使用量
         */
        calculateMemoryUsage() {
            let totalSize = 0;
            this.loadedModules.forEach((_, moduleName) => {
                totalSize += this.moduleDefinitions[moduleName]?.size || 0;
            });
            return totalSize;
        }

        /**
         * 设置清理定时器
         */
        setupCleanupTimer() {
            setInterval(() => {
                this.cleanupUnusedModules();
            }, this.moduleConfig.cleanupInterval);
        }

        /**
         * 清理未使用的模块
         */
        cleanupUnusedModules() {
            const threshold = 30 * 60 * 1000; // 30分钟未使用
            const now = Date.now();
            
            // 这里简化实现，实际应该跟踪模块最后使用时间
            if (this.loadedModules.size > this.moduleConfig.maxModules) {
                const toUnload = Array.from(this.loadedModules.keys())
                    .slice(0, this.loadedModules.size - this.moduleConfig.maxModules);
                
                toUnload.forEach(moduleName => this.unloadModule(moduleName));
            }
        }

        /**
         * 工具函数：首字母大写
         */
        capitalizeFirst(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        /**
         * 检查模块是否可用
         */
        isModuleAvailable(moduleName) {
            return !!this.moduleDefinitions[moduleName];
        }

        /**
         * 获取模块依赖关系图
         */
        getDependencyGraph() {
            const graph = {};
            Object.entries(this.moduleDefinitions).forEach(([name, config]) => {
                graph[name] = config.dependencies;
            });
            return graph;
        }
    }

    // 创建全局实例
    const moduleLoader = new ModuleLoader();

    // 导出到全局命名空间
    window.OTA.moduleLoader = moduleLoader;
    window.moduleLoader = moduleLoader; // 向后兼容

    // 工厂函数
    window.getModuleLoader = function() {
        return window.OTA.moduleLoader || window.moduleLoader;
    };

    // 增强require函数，支持模块懒加载
    window.requireModule = function(moduleName) {
        return moduleLoader.loadModule(moduleName);
    };

    // 预加载核心模块
    setTimeout(() => {
        const coreModules = ['learning-config', 'learning-storage-manager', 'user-operation-learner'];
        moduleLoader.preloadModules(coreModules);
    }, 1000);

    console.log('模块加载器加载完成', {
        version: moduleLoader.version,
        preloadStrategy: moduleLoader.moduleConfig.preloadStrategy,
        totalModules: Object.keys(moduleLoader.moduleDefinitions).length
    });

})();