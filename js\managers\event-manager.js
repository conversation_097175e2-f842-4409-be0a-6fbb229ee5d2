/**
 * 事件管理器模块
 * 负责处理用户交互事件、键盘快捷键和各种UI事件
 * 协调不同组件之间的事件通信
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getAppState() {
        return getService('appState');
    }

    function getLogger() {
        return getService('logger');
    }

    function getApiService() {
        return getService('apiService');
    }

    function getGeminiService() {
        return getService('geminiService');
    }

    /**
     * 事件管理器类
     * 负责事件处理和用户交互
     */
    class EventManager {
        constructor(elements, uiManager) {
            this.elements = elements;
            this.uiManager = uiManager;
            this.currentModal = null;
            
            // 绑定方法上下文
            this.handleLogin = this.handleLogin.bind(this);
            this.handleLogout = this.handleLogout.bind(this);
            this.handleParseOrder = this.handleParseOrder.bind(this);
            this.handleCreateOrder = this.handleCreateOrder.bind(this);
            this.handleThemeToggle = this.handleThemeToggle.bind(this);
            this.handleKeyboardShortcuts = this.handleKeyboardShortcuts.bind(this);
            this.handleLanguageChange = this.handleLanguageChange.bind(this); // **新增**
            this.handleShowHistory = this.handleShowHistory.bind(this);       // **新增**
            this.handleReturnToMultiOrder = this.handleReturnToMultiOrder.bind(this); // **新增**
        }

        /**
         * 初始化事件管理器
         */
        init() {
            this.bindEvents();
            this.setupKeyboardShortcuts();
            getLogger().log('事件管理器初始化完成', 'success');
        }

        /**
         * 绑定事件监听器 - 完全重写版本
         * 确保所有事件监听器都能正确绑定和工作
         */
        bindEvents() {
            getLogger().log('开始绑定事件监听器...', 'info');

            // 登录表单
            this.bindLoginEvents();

            // **重构**: 核心按钮事件绑定 - 使用更可靠的方式
            this.bindCoreButtonEvents();

            // 其他功能按钮
            this.bindFunctionalButtons();

            // 模态框事件
            this.bindModalEvents();

            // 预览模态框事件
            this.bindPreviewModalEvents();

            // 字段编辑事件
            this.bindFieldEditEvents();

            getLogger().log('所有事件监听器绑定完成', 'success');
        }

        /**
         * 绑定登录相关事件
         */
        bindLoginEvents() {
            // 登录表单提交
            if (this.elements.loginForm) {
                this.elements.loginForm.addEventListener('submit', this.handleLogin.bind(this));
                getLogger().log('登录表单事件已绑定', 'info');
            }

            // 清除保存账号按钮
            if (this.elements.clearSavedBtn) {
                this.elements.clearSavedBtn.addEventListener('click', () => this.handleClearSaved());
                getLogger().log('清除保存账号按钮事件已绑定', 'info');
            }
        }

        /**
         * 绑定核心按钮事件 - 重构版本
         * 专门处理历史订单和退出登录按钮
         */
        bindCoreButtonEvents() {
            // **重构**: 历史订单按钮 - 使用更可靠的绑定方式
            const historyBtn = document.getElementById('historyBtn');
            if (historyBtn) {
                // 移除可能存在的旧监听器
                historyBtn.removeEventListener('click', this.handleShowHistory);

                // 绑定新的监听器
                historyBtn.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    getLogger().log('历史订单按钮被点击', 'info');
                    this.handleShowHistory();
                });

                getLogger().log('历史订单按钮事件已重新绑定', 'success');
            } else {
                getLogger().log('历史订单按钮元素未找到', 'error');
            }

            // **重构**: 退出登录按钮 - 使用更可靠的绑定方式
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                // 移除可能存在的旧监听器
                logoutBtn.removeEventListener('click', this.handleLogout);

                // 绑定新的监听器
                logoutBtn.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    getLogger().log('退出登录按钮被点击', 'info');
                    this.handleLogout();
                });

                getLogger().log('退出登录按钮事件已重新绑定', 'success');
            } else {
                getLogger().log('退出登录按钮元素未找到', 'error');
            }

            // **新增**: 返回多订单模式按钮
            const returnToMultiOrderBtn = document.getElementById('returnToMultiOrder');
            if (returnToMultiOrderBtn) {
                // 移除可能存在的旧监听器
                returnToMultiOrderBtn.removeEventListener('click', this.handleReturnToMultiOrder);

                // 绑定新的监听器
                returnToMultiOrderBtn.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    getLogger().log('返回多订单模式按钮被点击', 'info');
                    this.handleReturnToMultiOrder();
                });

                getLogger().log('返回多订单模式按钮事件已绑定', 'success');
            } else {
                getLogger().log('返回多订单模式按钮元素未找到', 'info');
            }
        }

        /**
         * 绑定功能按钮事件
         */
        bindFunctionalButtons() {
            // 语言切换
            const languageSelect = document.getElementById('languageSelect');
            if (languageSelect) {
                languageSelect.addEventListener('change', this.handleLanguageChange.bind(this));
            }

            // 订单解析按钮
            this.elements.parseBtn?.addEventListener('click', this.handleParseOrder.bind(this));

            // 订单创建按钮
            this.elements.createBtn?.addEventListener('click', this.handleCreateOrder.bind(this));
            
            // 订单验证按钮
            this.elements.validateBtn?.addEventListener('click', () => this.handleValidateOrder());

            // 数据异常检查按钮
            this.elements.validateDataBtn?.addEventListener('click', () => this.handleValidateData());

            // 重置按钮
            this.elements.resetBtn?.addEventListener('click', () => this.handleResetOrder());

            // 主题切换按钮
            this.elements.themeToggle?.addEventListener('click', this.handleThemeToggle.bind(this));

            // 清除日志按钮
            this.elements.clearLogsBtn?.addEventListener('click', () => this.handleClearLogs());

            // 导出日志按钮
            this.elements.exportLogsBtn?.addEventListener('click', () => this.handleExportLogs());

            // 调试模式切换
            this.elements.debugMode?.addEventListener('change', () => this.handleDebugModeChange());

            // 生成示例订单按钮
            const generateSampleBtn = document.getElementById('generateSampleBtn');
            if (generateSampleBtn) {
                generateSampleBtn.addEventListener('click', () => {
                    if (this.elements.orderInput) {
                        this.elements.orderInput.value = getGeminiService().generateSampleOrder();
                    }
                });
            }

            // 清空输入按钮 - 修复ID匹配问题
            const clearInputBtn = document.getElementById('clearInput');
            if (clearInputBtn) {
                clearInputBtn.addEventListener('click', () => this.handleClearInput());
                getLogger().log('清空输入按钮事件已绑定', 'info');
            }

            // 手动多订单检测按钮
            const manualMultiOrderBtn = document.getElementById('manualMultiOrderDetection');
            if (manualMultiOrderBtn) {
                manualMultiOrderBtn.addEventListener('click', () => this.handleManualMultiOrderDetection());
                getLogger().log('手动多订单检测按钮事件已绑定', 'info');
            }
        }

        /**
         * 绑定模态框事件
         */
        bindModalEvents() {
            // 模态框关闭事件
            this.elements.modalCancel?.addEventListener('click', () => this.hideModal());
            this.elements.modalClose?.addEventListener('click', () => this.hideModal());
            
            // 点击模态框背景关闭
            this.elements.modal?.addEventListener('click', (e) => {
                if (e.target === this.elements.modal) {
                    this.hideModal();
                }
            });
        }

        /**
         * 绑定预览模态框事件
         */
        bindPreviewModalEvents() {
            const closePreviewModal = document.getElementById('closePreviewModal');
            if (closePreviewModal) {
                closePreviewModal.addEventListener('click', () => this.hidePreviewModal());
            }

            const previewModalOverlay = document.getElementById('previewSection');
            if (previewModalOverlay) {
                previewModalOverlay.addEventListener('click', (e) => {
                    if (e.target === previewModalOverlay) {
                        this.hidePreviewModal();
                    }
                });
            }
        }

        /**
         * 绑定字段编辑事件
         */
        bindFieldEditEvents() {
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('edit-field-btn')) {
                    this.handleFieldEdit(e.target);
                }
            });
        }

        /**
         * 设置键盘快捷键
         */
        setupKeyboardShortcuts() {
            document.addEventListener('keydown', this.handleKeyboardShortcuts);
        }

        /**
         * 处理键盘快捷键
         * @param {KeyboardEvent} e - 键盘事件
         */
        handleKeyboardShortcuts(e) {
            // Ctrl/Cmd + Enter: 手动解析订单
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                if (document.activeElement === this.elements.orderInput) {
                    e.preventDefault();
                    this.handleParseOrder();
                }
            }

            // Escape: 关闭模态框
            if (e.key === 'Escape' && this.currentModal) {
                this.hideModal();
            }

            // Ctrl/Cmd + Shift + R: 重置表单
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                this.handleResetOrder();
            }
        }

        /**
         * 处理登录
         * @param {Event} e - 表单提交事件
         */
        async handleLogin(e) {
            e.preventDefault();
            
            // **修复**: 从 UIManager 获取唯一的 FormManager 实例
            const formManager = this.uiManager.getManager('form');
            const stateManager = this.uiManager.getManager('state');

            const email = this.elements.emailInput.value.trim();
            const password = this.elements.passwordInput.value.trim();
            const rememberMe = this.elements.rememberMe?.checked || false;

            if (!email || !password) {
                this.uiManager.showAlert('请输入邮箱和密码', 'warning');
                return;
            }

            this.uiManager.setButtonLoading(this.elements.loginBtn, true);

            try {
                // **修复**: 直接调用 ApiService 登录
                const result = await getApiService().login(email, password, rememberMe);
                
                if (result.success) {
                    // **修复**: AppState 会自动更新，状态监听器会处理UI变化
                    getLogger().log('登录成功', 'success', { email });
                    this.uiManager.showAlert('登录成功！', 'success');
                } else {
                    getLogger().log('登录失败', 'error', { email, error: result.message });
                    this.uiManager.showAlert(result.message || '登录失败', 'error');
                }
            } catch (error) {
                getLogger().log('登录异常', 'error', { email, error: error.message });
                this.uiManager.showAlert('登录过程中发生错误', 'error');
            } finally {
                this.uiManager.setButtonLoading(this.elements.loginBtn, false);
            }
        }

        /**
         * **完全重写**: 处理登出
         * 确保完整的登出流程和UI状态切换
         */
        handleLogout() {
            getLogger().log('开始处理登出请求', 'info');

            // 显示确认对话框
            this.uiManager.showConfirm(
                '确认登出',
                '您确定要登出吗？这将清除当前的登录状态并返回登录界面。',
                () => {
                    this.performLogout();
                },
                () => {
                    getLogger().log('用户取消登出操作', 'info');
                }
            );
        }

        /**
         * 执行实际的登出操作
         * 包含完整的状态清理和UI切换
         */
        performLogout() {
            getLogger().log('开始执行登出操作', 'info');

            try {
                // 第一步：清除应用状态
                const appState = getAppState();
                if (appState) {
                    getLogger().log('清除应用认证状态', 'info');
                    appState.clearAuth();
                } else {
                    getLogger().log('AppState不可用，跳过状态清理', 'warning');
                }

                // 第二步：调用API服务的登出方法（如果可用）
                const apiService = getApiService();
                if (apiService && typeof apiService.clearAuthData === 'function') {
                    getLogger().log('调用API服务清除认证数据', 'info');
                    apiService.clearAuthData();
                } else {
                    getLogger().log('API服务清除认证数据方法不可用', 'warning');
                }

                // 第三步：手动执行UI状态切换
                this.switchToLoginUI();

                // 第四步：清除可能的缓存数据
                this.clearUserData();

                // 第五步：重新初始化关键组件
                this.reinitializeAfterLogout();

                getLogger().log('登出操作完成', 'success');
                this.uiManager.showAlert('已成功登出', 'success', 2000);

            } catch (error) {
                getLogger().log(`登出操作失败: ${error.message}`, 'error');
                this.uiManager.showAlert(`登出失败: ${error.message}`, 'error');
            }
        }

        /**
         * 登出后的重新初始化
         */
        reinitializeAfterLogout() {
            try {
                getLogger().log('开始登出后重新初始化', 'info');

                // 重置表单
                if (this.elements.orderForm) {
                    this.elements.orderForm.reset();
                }

                // 清空输入框
                if (this.elements.orderInput) {
                    this.elements.orderInput.value = '';
                }

                // 重新绑定登录表单事件（防止事件丢失）
                this.bindLoginEvents();

                getLogger().log('登出后重新初始化完成', 'success');

            } catch (error) {
                getLogger().log(`登出后重新初始化失败: ${error.message}`, 'error');
            }
        }

        /**
         * 切换到登录界面
         */
        switchToLoginUI() {
            getLogger().log('切换UI到登录界面', 'info');

            // 获取主要容器元素
            const loginPanel = document.getElementById('loginPanel');
            const workspace = document.getElementById('workspace');
            const userInfo = document.querySelector('.user-info');
            const historyBtn = document.getElementById('historyBtn');
            const logoutBtn = document.getElementById('logoutBtn');

            // 显示登录面板
            if (loginPanel) {
                loginPanel.style.display = 'block';
                getLogger().log('登录面板已显示', 'info');
            } else {
                getLogger().log('登录面板元素未找到', 'error');
            }

            // 隐藏工作区
            if (workspace) {
                workspace.style.display = 'none';
                getLogger().log('工作区已隐藏', 'info');
            } else {
                getLogger().log('工作区元素未找到', 'error');
            }

            // 隐藏用户信息区域
            if (userInfo) {
                userInfo.style.display = 'none';
                getLogger().log('用户信息区域已隐藏', 'info');
            }

            // 隐藏历史订单和登出按钮
            if (historyBtn) {
                historyBtn.style.display = 'none';
            }
            if (logoutBtn) {
                logoutBtn.style.display = 'none';
            }

            // 清除用户显示信息
            const currentUser = document.getElementById('currentUser');
            if (currentUser) {
                currentUser.textContent = '';
            }
        }

        /**
         * 清除用户相关数据
         */
        clearUserData() {
            getLogger().log('清除用户相关数据', 'info');

            // 清除表单数据（如果需要）
            const formManager = this.uiManager.getManager('form');
            if (formManager && typeof formManager.resetForm === 'function') {
                formManager.resetForm();
            }

            // 清除可能的临时数据
            // 注意：不清除localStorage中的记住我设置和保存的邮箱
        }

        /**
         * **新增**: 处理语言切换
         * @param {Event} e - 变化事件
         */
        handleLanguageChange(e) {
            const selectedLanguage = e.target.value;
            getLogger().log(`语言切换请求: ${selectedLanguage}`, 'info');
            
            // 通过全局函数调用I18nManager
            if (window.getI18nManager) {
                try {
                    const i18nManager = window.getI18nManager();
                    if (!i18nManager) {
                        getLogger().error('i18nManager未初始化', 'error');
                        return;
                    }
                    
                    i18nManager.setLanguage(selectedLanguage);
                    getLogger().log(`语言已切换至: ${selectedLanguage}`, 'info');
                    
                    // 显示语言切换成功提示，并自动关闭
                    const languageName = i18nManager.t(`header.language${selectedLanguage.charAt(0).toUpperCase() + selectedLanguage.slice(1)}`);
                    this.uiManager.showAlert(`语言已切换至: ${languageName}`, 'success', 2000); // 2秒后自动关闭
                } catch (error) {
                    getLogger().error(`语言切换失败: ${error.message}`, 'error');
                }
            } else {
                getLogger().error('i18nManager未定义', 'error');
            }
        }

        /**
         * **完全重写**: 处理显示历史订单
         * 确保历史订单面板能够正确显示
         */
        handleShowHistory() {
            getLogger().log('开始处理历史订单显示请求', 'info');

            try {
                // 第一步：多种方式获取OrderHistoryManager
                let historyManager = null;
                
                if (window.getOrderHistoryManager) {
                    historyManager = window.getOrderHistoryManager();
                } else if (window.OTA && window.OTA.orderHistoryManager) {
                    historyManager = window.OTA.orderHistoryManager;
                } else if (window.OrderHistoryManager) {
                    // 如果全局函数不存在，尝试直接创建实例
                    historyManager = new window.OrderHistoryManager();
                }

                if (!historyManager) {
                    throw new Error('无法获取OrderHistoryManager实例');
                }

                // 第二步：验证showHistoryPanel方法是否存在
                if (typeof historyManager.showHistoryPanel !== 'function') {
                    // 如果方法不存在，尝试直接操作DOM
                    getLogger().log('showHistoryPanel方法不存在，尝试直接显示面板', 'warning');
                    this.directShowHistoryPanel();
                    return;
                }

                // 第三步：验证历史面板DOM元素是否存在
                const historyPanel = document.getElementById('historyPanel');
                if (!historyPanel) {
                    throw new Error('历史订单面板DOM元素(#historyPanel)不存在');
                }

                getLogger().log('所有前置条件验证通过，开始显示历史面板', 'info');

                // 第四步：调用显示方法
                historyManager.showHistoryPanel();

                // 第五步：验证面板是否成功显示
                setTimeout(() => {
                    const isVisible = historyPanel && 
                        historyPanel.style.display !== 'none' && 
                        !historyPanel.classList.contains('hidden');
                    if (isVisible) {
                        getLogger().log('历史订单面板显示成功', 'success');
                    } else {
                        getLogger().log('历史订单面板显示失败 - 面板仍然隐藏', 'error');
                        this.directShowHistoryPanel();
                    }
                }, 100);

            } catch (error) {
                getLogger().log(`历史订单功能错误: ${error.message}`, 'error');
                
                // 备用方案：直接操作DOM显示面板
                getLogger().log('尝试备用方案：直接显示历史面板', 'info');
                this.directShowHistoryPanel();
            }
        }

        /**
         * 直接操作DOM显示历史面板（备用方案）
         */
        directShowHistoryPanel() {
            try {
                const historyPanel = document.getElementById('historyPanel');
                if (historyPanel) {
                    historyPanel.style.display = 'block';
                    historyPanel.classList.remove('hidden');
                    getLogger().log('直接显示历史面板成功', 'success');
                } else {
                    throw new Error('历史面板元素不存在');
                }
            } catch (error) {
                getLogger().log(`直接显示历史面板失败: ${error.message}`, 'error');
                this.uiManager.showAlert('历史订单功能暂时不可用，请刷新页面重试', 'error');
            }
        }

        /**
         * 处理返回多订单模式
         * 重新显示之前的多订单面板
         */
        handleReturnToMultiOrder() {
            getLogger().log('开始处理返回多订单模式请求', 'info');

            try {
                // 获取多订单管理器
                const multiOrderManager = window.OTA?.multiOrderManager || window.multiOrderManager || window.getMultiOrderManager?.();
                
                if (!multiOrderManager) {
                    throw new Error('无法获取MultiOrderManager实例');
                }

                // 检查是否有已解析的订单数据
                const parsedOrders = multiOrderManager.state?.parsedOrders;
                if (!parsedOrders || parsedOrders.length === 0) {
                    getLogger().log('没有找到已解析的多订单数据', 'warning');
                    this.uiManager.showAlert('没有可返回的多订单数据，请重新解析订单', 'warning');
                    return;
                }

                // 重新显示多订单面板
                multiOrderManager.showMultiOrderPanel(parsedOrders);
                
                // 隐藏返回按钮
                const returnBtn = document.getElementById('returnToMultiOrder');
                if (returnBtn) {
                    returnBtn.classList.add('hidden');
                }

                getLogger().log(`成功返回多订单模式，显示${parsedOrders.length}个订单`, 'success');

            } catch (error) {
                getLogger().log(`返回多订单模式失败: ${error.message}`, 'error');
                this.uiManager.showAlert('返回多订单模式失败，请重新解析订单', 'error');
            }
        }

        /**
         * 处理手动订单解析（保留作为备用功能）
         */
        async handleParseOrder() {
            const orderText = this.elements.orderInput.value.trim();
            
            if (!orderText) {
                this.uiManager.showAlert('请输入订单描述', 'warning');
                return;
            }

            if (!getGeminiService().isAvailable()) {
                this.uiManager.showApiKeyPrompt();
                return;
            }

            this.uiManager.setButtonLoading(this.elements.parseBtn, true);

            try {
                const result = await getGeminiService().parseOrder(orderText);
                
                if (result && Array.isArray(result) && result.length > 0) {
                    // parseOrder 返回的是数组，取第一个订单数据
                    const orderData = result[0];
                    
                    // 更新应用状态
                    getAppState().setCurrentOrder({
                        rawText: orderText,
                        parsedData: orderData,
                        confidence: this.calculateDataConfidence(orderData),
                        timestamp: Date.now()
                    });

                    getLogger().log('订单解析成功', 'success', { orderData });
                    this.uiManager.showAlert('订单解析成功！', 'success');
                } else {
                    getLogger().log('订单解析失败：无有效数据', 'error');
                    this.uiManager.showAlert('订单解析失败，请检查输入内容', 'error');
                }
            } catch (error) {
                getLogger().log('订单解析异常', 'error', { error: error.message });
                this.uiManager.showAlert('订单解析过程中发生错误', 'error');
            } finally {
                this.uiManager.setButtonLoading(this.elements.parseBtn, false);
            }
        }

        /**
         * 处理订单创建
         * @param {Event} e - 表单提交事件
         */
        async handleCreateOrder(e) {
            if (e) e.preventDefault();

            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            const orderData = formManager.collectFormData();

            // **新增**: 强制确保负责人字段存在
            if (!orderData.incharge_by_backend_user_id) {
                const defaultBackendUserId = getApiService().getDefaultBackendUserId();
                if (defaultBackendUserId) {
                    orderData.incharge_by_backend_user_id = defaultBackendUserId;
                    getLogger().log(`handleCreateOrder: 补充设置负责人ID: ${defaultBackendUserId}`, 'info');
                } else {
                    // 如果还是无法获取，使用紧急默认值
                    orderData.incharge_by_backend_user_id = 1;
                    getLogger().log('handleCreateOrder: 使用紧急默认负责人ID: 1', 'warning');
                }
            }

            // 验证数据
            const validation = getApiService().validateOrderData(orderData);
            if (!validation.isValid) {
                formManager.showValidationErrors(validation.errors);
                getLogger().log('订单验证失败', 'error', { 
                    errors: validation.errors,
                    orderData: orderData 
                });
                return;
            }

            if (validation.warnings.length > 0) {
                this.uiManager.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
            }

            this.uiManager.setButtonLoading(this.elements.createBtn, true);

            try {
                const result = await getApiService().createOrder(orderData);
                
                if (result.success) {
                    // 提取订单ID
                    const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';
                    
                    getLogger().log('订单创建成功', 'success', { 
                        orderId,
                        orderData,
                        response: result 
                    });

                    // **新增**: 记录成功的订单到历史
                    try {
                        const historyManager = this.getOrderHistoryManager();
                        if (historyManager) {
                            historyManager.addOrder(orderData, orderId, result);
                            getLogger().log('订单已记录到历史', 'info', { orderId });
                        }
                    } catch (historyError) {
                        getLogger().logError('记录订单历史失败', historyError);
                    }

                    // 显示简洁的成功提示
                    this.uiManager.showSimpleSuccessToast(orderId);

                    // 自动重置表单（无确认对话框）
                    formManager.resetForm();
                    
                    // 重新填充选项（确保下拉框数据完整）
                    setTimeout(() => {
                        formManager.populateFormOptions();
                    }, 100);

                } else {
                    const errorMessage = result.message || result.error || '订单创建失败';
                    getLogger().log('订单创建失败', 'error', {
                        error: errorMessage,
                        orderData,
                        response: result
                    });

                    // **新增**: 记录失败的订单到历史
                    try {
                        const historyManager = this.getOrderHistoryManager();
                        if (historyManager) {
                            // 记录失败的订单，使用特殊的订单ID标识
                            const failedOrderId = `FAILED_${Date.now()}`;
                            historyManager.addOrder(orderData, failedOrderId, {
                                success: false,
                                message: errorMessage,
                                error: result,
                                timestamp: new Date().toISOString()
                            });
                            getLogger().log('失败订单已记录到历史', 'info', { failedOrderId });
                        }
                    } catch (historyError) {
                        getLogger().logError('记录失败订单历史失败', historyError);
                    }

                    // 创建错误对象并显示详细错误弹窗
                    const errorObj = new Error(errorMessage);
                    errorObj.details = result;
                    errorObj.requestData = orderData;

                    // 如果API返回了验证错误，添加到错误对象中
                    if (result.data && result.data.validation_error) {
                        errorObj.validationErrors = result.data.validation_error;
                    }

                    this.uiManager.showErrorModal(errorObj, {
                        title: 'Order Creation Failed'
                    });
                }
            } catch (error) {
                getLogger().log('订单创建异常', 'error', {
                    error: error.message,
                    originalError: error.originalError?.message,
                    errorDetails: error.details,
                    orderData
                });

                // **新增**: 记录异常的订单到历史
                try {
                    const historyManager = this.getOrderHistoryManager();
                    if (historyManager) {
                        const exceptionOrderId = `EXCEPTION_${Date.now()}`;
                        historyManager.addOrder(orderData, exceptionOrderId, {
                            success: false,
                            message: `异常: ${error.message}`,
                            error: {
                                name: error.name,
                                message: error.message,
                                stack: error.stack
                            },
                            timestamp: new Date().toISOString()
                        });
                        getLogger().log('异常订单已记录到历史', 'info', { exceptionOrderId });
                    }
                } catch (historyError) {
                    getLogger().logError('记录异常订单历史失败', historyError);
                }

                // 确保错误弹窗总是显示
                try {
                    // 检查是否是重复订单错误
                    if (error.isDuplicateOrder) {
                        this.uiManager.showErrorModal(error, {
                            title: '⚠️ Duplicate Order Detected'
                        });
                    } else {
                        this.uiManager.showErrorModal(error, {
                            title: 'Order Creation Failed'
                        });
                    }
                } catch (uiError) {
                    getLogger().logError('显示错误弹窗失败', uiError);
                    // 如果弹窗显示失败，至少显示简单的alert
                    alert(`Order Creation Failed: ${error.message}`);
                }
            } finally {
                this.uiManager.setButtonLoading(this.elements.createBtn, false);
            }
        }

        /**
         * 处理订单验证
         */
        handleValidateOrder() {
            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            const orderData = formManager.collectFormData();
            const validation = getApiService().validateOrderData(orderData);

            if (validation.isValid) {
                this.uiManager.showAlert('数据验证通过！', 'success');
            } else {
                formManager.showValidationErrors(validation.errors);
            }

            if (validation.warnings.length > 0) {
                this.uiManager.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
            }
        }

        /**
         * 处理数据异常提示
         */
        handleValidateData() {
            try {
                // **修复**: 从UIManager获取FormManager
                const formManager = this.uiManager.getManager('form');
                if (!formManager) return;

                const data = formManager.collectFormData();
                const issues = this.detectDataIssues(data);

                if (issues.length === 0) {
                    this.uiManager.showQuickToast('✅ 数据检查正常', 'success');
                } else {
                    const issueText = issues.slice(0, 3).join('、');
                    const moreText = issues.length > 3 ? `等${issues.length}个问题` : '';
                    this.uiManager.showQuickToast(`⚠️ 发现问题：${issueText}${moreText}`, 'warning');
                }
            } catch (error) {
                getLogger().log('数据验证失败', 'error', { error: error.message });
                this.uiManager.showQuickToast('❌ 数据验证失败', 'error');
            }
        }

        /**
         * 检测数据异常
         * @param {Object} data - 订单数据
         * @returns {Array} 问题列表
         */
        detectDataIssues(data) {
            const issues = [];

            // 基本信息检查
            if (!data.customer_name) {
                issues.push('客户姓名为空');
            }

            if (!data.customer_contact && !data.customer_email) {
                issues.push('客户联系方式不完整（电话和邮箱都为空）');
            }

            if (!data.pickup) {
                issues.push('上车地点为空');
            }

            if (!data.dropoff) {
                issues.push('目的地为空');
            }

            if (!data.pickup_date) {
                issues.push('接送日期为空');
            }

            if (!data.pickup_time) {
                issues.push('接送时间为空');
            }

            // 数值范围检查
            if (data.passenger_count && data.passenger_count > 20) {
                issues.push('乘客人数过多（超过20人）');
            }

            if (data.luggage_count && data.luggage_count > 50) {
                issues.push('行李件数过多（超过50件）');
            }

            // 日期有效性检查
            if (data.pickup_date) {
                const pickupDate = new Date(data.pickup_date);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (pickupDate < today) {
                    issues.push('接送日期已过期');
                }

                const oneYearLater = new Date();
                oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

                if (pickupDate > oneYearLater) {
                    issues.push('接送日期过于遥远（超过一年）');
                }
            }

            // 价格检查
            if (data.ota_price && data.ota_price < 0) {
                issues.push('价格不能为负数');
            }

            if (data.ota_price && data.ota_price > 10000) {
                issues.push('价格过高（超过10000）');
            }

            return issues;
        }

        /**
         * 处理订单重置
         */
        handleResetOrder() {
            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            this.uiManager.showConfirm(
                '重置表单',
                '您确定要重置表单吗？这将清除所有已填写的内容。',
                () => {
                    formManager.resetForm();
                }
            );
        }

        /**
         * 处理主题切换
         */
        handleThemeToggle() {
            const currentTheme = getAppState().get('config.theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            // 保存到应用状态
            getAppState().set('config.theme', newTheme);
            
            // 持久化保存到localStorage
            localStorage.setItem('ota_theme_preference', newTheme);
            
            getLogger().log(`主题已切换至: ${newTheme}`, 'info');
            
            // 显示切换成功提示，自动关闭
            const i18nManager = window.getI18nManager();
            const themeText = newTheme === 'light' ? '亮色' : '暗色';
            const message = i18nManager ? 
                i18nManager.t('messages.themeChanged', { theme: themeText }) : 
                `主题已切换至${themeText}模式`;
            this.uiManager.showAlert(message, 'success', 2000);
        }

        /**
         * 处理清空日志
         */
        handleClearLogs() {
            getLogger().clear();
        }

        /**
         * 处理导出日志
         */
        handleExportLogs() {
            const logs = getLogger().export();
            const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `ota-logs-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.uiManager.showAlert('日志已导出', 'success');
        }

        /**
         * 处理调试模式变化
         */
        handleDebugModeChange() {
            const enabled = this.elements.debugMode.checked;
            getAppState().setDebugMode(enabled);
            getLogger().log(`调试模式${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 处理字段编辑
         * @param {HTMLElement} button - 编辑按钮
         */
        handleFieldEdit(button) {
            const fieldName = button.getAttribute('data-field');
            const fieldGroup = button.closest('.editable-field');
            const field = fieldGroup.querySelector('input, textarea, select');

            if (!field) return;

            if (fieldGroup.classList.contains('editing')) {
                // 保存编辑
                this.saveFieldEdit(fieldGroup, field);
            } else {
                // 开始编辑
                this.startFieldEdit(fieldGroup, field);
            }
        }

        /**
         * 开始字段编辑
         * @param {HTMLElement} fieldGroup - 字段组容器
         * @param {HTMLElement} field - 字段元素
         */
        startFieldEdit(fieldGroup, field) {
            fieldGroup.classList.add('editing');
            field.focus();

            // 如果是输入框，选中所有文本
            if (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA') {
                field.select();
            }
        }

        /**
         * 保存字段编辑
         * @param {HTMLElement} fieldGroup - 字段组容器
         * @param {HTMLElement} field - 字段元素
         */
        saveFieldEdit(fieldGroup, field) {
            fieldGroup.classList.remove('editing');

            // 触发change事件以更新应用状态
            const event = new Event('change', { bubbles: true });
            field.dispatchEvent(event);

            getLogger().log('字段编辑已保存', 'info', {
                fieldName: field.name || field.id,
                value: field.value
            });
        }

        /**
         * 处理清空输入
         */
        handleClearInput() {
            this.uiManager.showConfirm(
                '清空输入',
                '您确定要清空当前输入的内容吗？',
                () => {
                    this.performComprehensiveClear();
                }
            );
        }

        /**
         * 执行全面清理操作
         * 清理所有相关数据但保护关键信息
         */
        performComprehensiveClear() {
            const logger = getLogger();
            logger.log('🧹 开始执行全面清理操作...', 'info');

            try {
                // 1. 清理表单输入字段
                if (this.elements.orderInput) {
                    this.elements.orderInput.value = '';
                }

                // 2. 清理表单管理器状态
                const formManager = this.uiManager.getManager('form');
                if (formManager && typeof formManager.resetForm === 'function') {
                    formManager.resetForm();
                }

                // 3. 清理实时分析状态
                const realtimeManager = this.uiManager.getManager('realtimeAnalysis');
                if (realtimeManager && typeof realtimeManager.clearRealtimeAnalysis === 'function') {
                    realtimeManager.clearRealtimeAnalysis();
                }

                // 4. 清理多订单管理器状态
                if (window.OTA?.multiOrderManager) {
                    window.OTA.multiOrderManager.hideMultiOrderPanel();
                }

                // 5. 清理当前订单状态（但保护历史记录）
                const appState = getAppState();
                if (appState) {
                    appState.set('currentOrder', null);
                    appState.set('currentAnalysis', null);
                }

                // 6. 清理UI显示状态
                this.clearUIDisplayState();

                // 7. 重置Gemini状态显示
                this.resetGeminiStatusDisplay();

                logger.log('✅ 全面清理操作完成', 'success');

            } catch (error) {
                logger.logError('全面清理操作失败', error);
            }
        }

        /**
         * 清理UI显示状态
         */
        clearUIDisplayState() {
            try {
                // 清理进度指示器
                const progressIndicators = document.querySelectorAll('.progress-indicator, .loading-spinner');
                progressIndicators.forEach(indicator => {
                    indicator.style.display = 'none';
                });

                // 清理错误提示
                const errorMessages = document.querySelectorAll('.error-message, .validation-message');
                errorMessages.forEach(message => {
                    message.remove();
                });

                // 清理成功提示
                const successMessages = document.querySelectorAll('.success-message, .field-success-message');
                successMessages.forEach(message => {
                    message.remove();
                });

                getLogger().log('UI显示状态已清理', 'info');
            } catch (error) {
                getLogger().logError('清理UI显示状态失败', error);
            }
        }

        /**
         * 重置Gemini状态显示
         */
        resetGeminiStatusDisplay() {
            try {
                const geminiStatus = document.getElementById('geminiStatus');
                if (geminiStatus) {
                    geminiStatus.textContent = '请输入订单描述';
                    geminiStatus.className = 'gemini-status';
                }

                const geminiIndicator = document.querySelector('.gemini-indicator');
                if (geminiIndicator) {
                    geminiIndicator.style.display = 'none';
                }

                getLogger().log('Gemini状态显示已重置', 'info');
            } catch (error) {
                getLogger().logError('重置Gemini状态显示失败', error);
            }
        }

        /**
         * 处理手动多订单检测
         */
        handleManualMultiOrderDetection() {
            const logger = getLogger();

            if (!this.elements.orderInput || !this.elements.orderInput.value.trim()) {
                this.uiManager.showAlert('提示', '请先输入订单内容再进行多订单检测。');
                return;
            }

            const orderText = this.elements.orderInput.value.trim();

            // 检查最小长度要求
            if (orderText.length < 50) {
                this.uiManager.showAlert('提示', '订单内容过短，建议至少输入50个字符以获得更好的检测效果。');
                return;
            }

            // 显示确认对话框（如果启用）
            const multiOrderManager = window.OTA?.multiOrderManager;
            if (!multiOrderManager) {
                this.uiManager.showAlert('错误', '多订单管理器不可用，请刷新页面重试。');
                return;
            }

            const config = multiOrderManager.config?.manualDetection;
            if (config?.confirmBeforeDetection) {
                this.uiManager.showConfirm(
                    '手动多订单检测',
                    '确定要对当前输入内容进行多订单检测吗？这将使用AI分析文本并可能显示多订单面板。',
                    () => {
                        this.performManualMultiOrderDetection(orderText);
                    }
                );
            } else {
                this.performManualMultiOrderDetection(orderText);
            }
        }

        /**
         * 执行手动多订单检测
         * @param {string} orderText - 订单文本
         */
        async performManualMultiOrderDetection(orderText) {
            const logger = getLogger();

            try {
                logger.log('🔍 开始手动多订单检测...', 'info');

                // 显示加载状态
                const manualBtn = document.getElementById('manualMultiOrderDetection');
                if (manualBtn) {
                    manualBtn.disabled = true;
                    manualBtn.innerHTML = '🔄 检测中...';
                }

                // 调用多订单管理器进行检测
                const multiOrderManager = window.OTA?.multiOrderManager;
                if (multiOrderManager) {
                    // 强制触发多订单检测，忽略自动触发条件
                    await multiOrderManager.analyzeInputForMultiOrder(orderText, {
                        forceDetection: true,
                        source: 'manual'
                    });

                    logger.log('✅ 手动多订单检测完成', 'success');
                } else {
                    throw new Error('多订单管理器不可用');
                }

            } catch (error) {
                logger.logError('手动多订单检测失败', error);
                this.uiManager.showAlert('检测失败', `多订单检测过程中发生错误：${error.message}`);
            } finally {
                // 恢复按钮状态
                const manualBtn = document.getElementById('manualMultiOrderDetection');
                if (manualBtn) {
                    manualBtn.disabled = false;
                    manualBtn.innerHTML = '🔍 多订单检测';
                }
            }
        }

        /**
         * 处理清除保存账号
         */
        handleClearSaved() {
            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            this.uiManager.showConfirm(
                '清除保存的账号',
                '您确定要清除保存的账号信息吗？',
                () => {
                    formManager.clearSavedAccountInfo();
                }
            );
        }

        /**
         * 显示模态框
         * @param {string} title - 标题
         * @param {string} content - 内容
         * @param {Object} options - 选项
         */
        showModal(title, content, options = {}) {
            this.currentModal = this.elements.modal;
            this.uiManager.showModal(title, content, options);
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            this.currentModal = null;
            this.uiManager.hideModal();
        }

        /**
         * 显示预览模态框
         */
        showPreviewModal() {
            this.uiManager.showPreviewModal();
        }

        /**
         * 隐藏预览模态框
         */
        hidePreviewModal() {
            this.uiManager.hidePreviewModal();
        }

        /**
         * 计算数据置信度
         * @param {object} orderData - 订单数据
         * @returns {number} 置信度百分比
         */
        calculateDataConfidence(orderData) {
            if (!orderData || typeof orderData !== 'object') {
                return 0;
            }

            // 重要字段权重
            const importantFields = {
                'customer_name': 15,
                'customer_contact': 10,
                'pickup_location': 20,
                'dropoff_location': 20,
                'pickup_date': 15,
                'pickup_time': 10,
                'ota_price': 5,
                'ota_reference_number': 5
            };

            let filledWeight = 0;
            let totalWeight = 0;

            for (const [field, weight] of Object.entries(importantFields)) {
                totalWeight += weight;
                const value = orderData[field];
                if (value !== null && value !== undefined && value !== '' && value !== 0) {
                    filledWeight += weight;
                }
            }

            return Math.round((filledWeight / totalWeight) * 100);
        }

        /**
         * 安全地获取OrderHistoryManager实例
         * @returns {OrderHistoryManager|null} 历史管理器实例或null
         */
        getOrderHistoryManager() {
            try {
                // 尝试多种方式获取OrderHistoryManager
                if (window.getOrderHistoryManager) {
                    return window.getOrderHistoryManager();
                } else if (window.OTA && window.OTA.orderHistoryManager) {
                    return window.OTA.orderHistoryManager;
                } else if (window.OrderHistoryManager) {
                    return new window.OrderHistoryManager();
                }
                
                getLogger().log('无法获取OrderHistoryManager实例', 'warning');
                return null;
            } catch (error) {
                getLogger().logError('获取OrderHistoryManager失败', error);
                return null;
            }
        }

    }

    // 导出到全局命名空间
    window.OTA.managers.EventManager = EventManager;

})();
