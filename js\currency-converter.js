/**
 * 货币转换管理器
 * 负责价格识别、货币转换和汇率管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块 - 使用统一的服务定位器
function getLogger() {
    return getService('logger');
}

class CurrencyConverter {
    constructor() {
        this.storageKey = 'ota_exchange_rates';
        this.baseCurrency = 'MYR'; // 基础货币为马来西亚令吉
        
        // 默认汇率（可由用户自定义）
        this.defaultRates = {
            'CNY': 0.615,  // 人民币转MYR：乘以0.615
            'USD': 4.3,    // 美元转MYR：乘以4.3
            'SGD': 3.4,    // 新币转MYR：乘以3.4
            'MYR': 1.0     // MYR保持不变
        };
        
        this.init();
    }

    /**
     * 初始化货币转换器
     */
    init() {
        this.loadExchangeRates();
        const logger = getLogger();
        if (logger) {
            logger.log('货币转换管理器已初始化', 'info');
        }
    }

    /**
     * 加载汇率设置
     */
    loadExchangeRates() {
        try {
            const savedRates = localStorage.getItem(this.storageKey);
            if (savedRates) {
                this.exchangeRates = { ...this.defaultRates, ...JSON.parse(savedRates) };
            } else {
                this.exchangeRates = { ...this.defaultRates };
            }
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`加载汇率设置失败: ${error.message}`, 'error');
            }
            this.exchangeRates = { ...this.defaultRates };
        }
    }

    /**
     * 保存汇率设置
     */
    saveExchangeRates() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.exchangeRates));
            const logger = getLogger();
            if (logger) {
                logger.log('汇率设置已保存', 'info');
            }
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`保存汇率设置失败: ${error.message}`, 'error');
            }
        }
    }

    /**
     * 识别价格和货币类型
     * @param {string} text - 包含价格的文本
     * @returns {Object|null} 价格信息对象
     */
    parsePrice(text) {
        if (!text || typeof text !== 'string') {
            return null;
        }

        // 价格识别正则表达式
        const pricePatterns = [
            // MYR格式
            { regex: /(?:RM|MYR)\s*(\d+(?:\.\d{2})?)/i, currency: 'MYR' },
            { regex: /(\d+(?:\.\d{2})?)\s*(?:RM|MYR|马币|令吉)/i, currency: 'MYR' },

            // USD格式
            { regex: /(?:\$|USD)\s*(\d+(?:\.\d{2})?)/i, currency: 'USD' },
            { regex: /(\d+(?:\.\d{2})?)\s*(?:USD|美元|美金)/i, currency: 'USD' },

            // SGD格式
            { regex: /(?:S\$|SGD\$|SGD)\s*(\d+(?:\.\d{2})?)/i, currency: 'SGD' },
            { regex: /(\d+(?:\.\d{2})?)\s*(?:SGD|S\$|SGD\$|新币)/i, currency: 'SGD' },

            // CNY格式
            { regex: /(?:￥|CNY|RMB)\s*(\d+(?:\.\d{2})?)/i, currency: 'CNY' },
            { regex: /(\d+(?:\.\d{2})?)\s*(?:CNY|RMB|人民币|元)/i, currency: 'CNY' },

            // 通用数字格式（默认为MYR）
            { regex: /(\d+(?:\.\d{2})?)/i, currency: 'MYR' }
        ];

        for (const pattern of pricePatterns) {
            const match = text.match(pattern.regex);
            if (match) {
                const amount = parseFloat(match[1]);
                if (!isNaN(amount) && amount > 0) {
                    return {
                        originalAmount: amount,
                        originalCurrency: pattern.currency,
                        originalText: match[0]
                    };
                }
            }
        }

        return null;
    }

    /**
     * 转换货币到基础货币（MYR）
     * @param {number} amount - 金额
     * @param {string} fromCurrency - 源货币
     * @returns {Object} 转换结果
     */
    convertToMYR(amount, fromCurrency) {
        if (!amount || !fromCurrency) {
            return {
                originalAmount: amount,
                originalCurrency: fromCurrency,
                convertedAmount: amount,
                convertedCurrency: 'MYR',
                exchangeRate: 1,
                needsConversion: false
            };
        }

        const rate = this.exchangeRates[fromCurrency.toUpperCase()];
        if (!rate) {
            const logger = getLogger();
            if (logger) {
                logger.log(`不支持的货币类型: ${fromCurrency}`, 'warning');
            }
            return {
                originalAmount: amount,
                originalCurrency: fromCurrency,
                convertedAmount: amount,
                convertedCurrency: 'MYR',
                exchangeRate: 1,
                needsConversion: false,
                error: `不支持的货币类型: ${fromCurrency}`
            };
        }

        const convertedAmount = Math.round(amount * rate * 100) / 100; // 保留两位小数
        const needsConversion = fromCurrency.toUpperCase() !== 'MYR';

        return {
            originalAmount: amount,
            originalCurrency: fromCurrency.toUpperCase(),
            convertedAmount: convertedAmount,
            convertedCurrency: 'MYR',
            exchangeRate: rate,
            needsConversion: needsConversion
        };
    }

    /**
     * 处理订单数据中的价格转换
     * @param {Object} orderData - 订单数据
     * @returns {Object} 处理后的订单数据
     */
    processOrderPrice(orderData) {
        if (!orderData || !orderData.price || !orderData.currency) {
            return orderData;
        }

        const conversionResult = this.convertToMYR(orderData.price, orderData.currency);
        
        // 更新订单数据
        const processedData = {
            ...orderData,
            // 保留原始价格信息
            originalPrice: conversionResult.originalAmount,
            originalCurrency: conversionResult.originalCurrency,
            // 更新为转换后的价格
            price: conversionResult.convertedAmount,
            currency: conversionResult.convertedCurrency,
            // 添加转换信息
            priceConversion: {
                needsConversion: conversionResult.needsConversion,
                exchangeRate: conversionResult.exchangeRate,
                conversionNote: conversionResult.needsConversion 
                    ? `原价 ${conversionResult.originalAmount} ${conversionResult.originalCurrency}，按汇率 ${conversionResult.exchangeRate} 转换`
                    : null
            }
        };

        if (conversionResult.needsConversion) {
            getLogger().log(`价格已转换: ${conversionResult.originalAmount} ${conversionResult.originalCurrency} → ${conversionResult.convertedAmount} MYR`, 'info');
        }

        return processedData;
    }

    /**
     * 批量处理多个订单的价格转换
     * @param {Array} ordersData - 订单数据数组
     * @returns {Array} 处理后的订单数据数组
     */
    processMultipleOrdersPrices(ordersData) {
        if (!Array.isArray(ordersData)) {
            return ordersData;
        }

        return ordersData.map(orderData => this.processOrderPrice(orderData));
    }

    /**
     * 更新汇率
     * @param {string} currency - 货币代码
     * @param {number} rate - 汇率
     */
    updateExchangeRate(currency, rate) {
        if (!currency || !rate || rate <= 0) {
            throw new Error('无效的货币代码或汇率');
        }

        this.exchangeRates[currency.toUpperCase()] = rate;
        this.saveExchangeRates();
        
        getLogger().log(`汇率已更新: ${currency.toUpperCase()} = ${rate}`, 'info');
    }

    /**
     * 获取当前汇率设置
     * @returns {Object} 汇率对象
     */
    getExchangeRates() {
        return { ...this.exchangeRates };
    }

    /**
     * 重置汇率为默认值
     */
    resetToDefaultRates() {
        this.exchangeRates = { ...this.defaultRates };
        this.saveExchangeRates();
        getLogger().log('汇率已重置为默认值', 'info');
    }

    /**
     * 获取支持的货币列表
     * @returns {Array} 货币列表
     */
    getSupportedCurrencies() {
        return Object.keys(this.exchangeRates).map(code => ({
            code: code,
            rate: this.exchangeRates[code],
            name: this.getCurrencyName(code)
        }));
    }

    /**
     * 获取货币名称
     * @param {string} code - 货币代码
     * @returns {string} 货币名称
     */
    getCurrencyName(code) {
        const names = {
            'MYR': '马来西亚令吉',
            'CNY': '人民币',
            'USD': '美元',
            'SGD': '新加坡元'
        };
        return names[code] || code;
    }

    /**
     * 格式化价格显示
     * @param {number} amount - 金额
     * @param {string} currency - 货币代码
     * @returns {string} 格式化后的价格字符串
     */
    formatPrice(amount, currency = 'MYR') {
        if (!amount || isNaN(amount)) {
            return '0.00';
        }

        const symbols = {
            'MYR': 'RM',
            'CNY': '￥',
            'USD': '$',
            'SGD': 'S$'
        };

        const symbol = symbols[currency] || currency;
        return `${symbol} ${amount.toFixed(2)}`;
    }

    /**
     * 创建价格转换摘要
     * @param {Object} conversionResult - 转换结果
     * @returns {string} 转换摘要文本
     */
    createConversionSummary(conversionResult) {
        if (!conversionResult.needsConversion) {
            return `价格: ${this.formatPrice(conversionResult.convertedAmount, conversionResult.convertedCurrency)}`;
        }

        return `价格: ${this.formatPrice(conversionResult.originalAmount, conversionResult.originalCurrency)} → ${this.formatPrice(conversionResult.convertedAmount, conversionResult.convertedCurrency)} (汇率: ${conversionResult.exchangeRate})`;
    }
}

// 创建全局实例
let currencyConverterInstance = null;

/**
 * 获取货币转换器实例
 * @returns {CurrencyConverter} 转换器实例
 */
function getCurrencyConverter() {
    if (!currencyConverterInstance) {
        currencyConverterInstance = new CurrencyConverter();
    }
    return currencyConverterInstance;
}

// 导出到全局作用域
window.CurrencyConverter = CurrencyConverter;
window.getCurrencyConverter = getCurrencyConverter;
