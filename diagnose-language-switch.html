<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言切换诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>语言切换诊断工具</h1>
    
    <div class="section">
        <h2>1. 依赖检查</h2>
        <button onclick="checkDependencies()">检查依赖</button>
        <div id="dependencyResults"></div>
    </div>

    <div class="section">
        <h2>2. 初始化检查</h2>
        <button onclick="checkInitialization()">检查初始化</button>
        <div id="initResults"></div>
    </div>

    <div class="section">
        <h2>3. 语言切换测试</h2>
        <select id="testLanguage">
            <option value="zh">中文</option>
            <option value="en">English</option>
        </select>
        <button onclick="testLanguageSwitch()">测试切换</button>
        <div id="switchResults"></div>
    </div>

    <div class="section">
        <h2>4. 事件系统测试</h2>
        <button onclick="testEventSystem()">测试事件系统</button>
        <div id="eventResults"></div>
    </div>

    <!-- 加载核心脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/i18n.js"></script>

    <script>
        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
        }

        function clear(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function checkDependencies() {
            clear('dependencyResults');
            
            const checks = [
                { name: 'window.OTA', value: window.OTA, expected: 'object' },
                { name: 'window.getI18nManager', value: window.getI18nManager, expected: 'function' },
                { name: 'window.OTA.getI18nManager', value: window.OTA?.getI18nManager, expected: 'function' },
                { name: 'window.OTA.i18nManager', value: window.OTA?.i18nManager, expected: 'object' },
                { name: 'window.OTA.appState', value: window.OTA?.appState, expected: 'object' },
                { name: 'window.getAppState', value: window.getAppState, expected: 'function' },
                { name: 'window.getLogger', value: window.getLogger, expected: 'function' }
            ];

            checks.forEach(check => {
                const actualType = typeof check.value;
                const exists = check.value !== undefined && check.value !== null;
                const correct = actualType === check.expected;
                
                if (exists && correct) {
                    log('dependencyResults', `✅ ${check.name}: ${actualType}`, 'success');
                } else if (exists) {
                    log('dependencyResults', `⚠️ ${check.name}: ${actualType} (期望: ${check.expected})`, 'warning');
                } else {
                    log('dependencyResults', `❌ ${check.name}: 不存在`, 'error');
                }
            });
        }

        function checkInitialization() {
            clear('initResults');
            
            try {
                // 检查国际化管理器实例
                const i18nManager = window.getI18nManager();
                if (!i18nManager) {
                    log('initResults', '❌ 无法获取国际化管理器实例', 'error');
                    return;
                }
                log('initResults', '✅ 成功获取国际化管理器实例', 'success');

                // 检查当前语言
                const currentLang = i18nManager.getCurrentLanguage();
                log('initResults', `当前语言: ${currentLang}`, 'info');

                // 检查翻译资源
                const hasTranslations = i18nManager.translations && Object.keys(i18nManager.translations).length > 0;
                log('initResults', `翻译资源: ${hasTranslations ? '已加载' : '未加载'}`, hasTranslations ? 'success' : 'error');

                if (hasTranslations) {
                    const languages = Object.keys(i18nManager.translations);
                    log('initResults', `可用语言: ${languages.join(', ')}`, 'info');
                }

                // 检查应用状态
                if (window.getAppState) {
                    const appState = window.getAppState();
                    if (appState) {
                        const configLang = appState.get('config.language');
                        log('initResults', `应用状态中的语言: ${configLang}`, 'info');
                        
                        const localStorageLang = localStorage.getItem('ota_language_preference');
                        log('initResults', `localStorage中的语言: ${localStorageLang}`, 'info');
                    }
                }

                // 尝试初始化
                if (typeof i18nManager.init === 'function') {
                    i18nManager.init();
                    log('initResults', '✅ 初始化完成', 'success');
                    
                    const newCurrentLang = i18nManager.getCurrentLanguage();
                    log('initResults', `初始化后语言: ${newCurrentLang}`, 'info');
                }

            } catch (error) {
                log('initResults', `❌ 初始化检查出错: ${error.message}`, 'error');
            }
        }

        function testLanguageSwitch() {
            clear('switchResults');
            
            const targetLanguage = document.getElementById('testLanguage').value;
            
            try {
                const i18nManager = window.getI18nManager();
                if (!i18nManager) {
                    log('switchResults', '❌ 无法获取国际化管理器', 'error');
                    return;
                }

                const beforeLang = i18nManager.getCurrentLanguage();
                log('switchResults', `切换前语言: ${beforeLang}`, 'info');

                // 执行切换
                i18nManager.setLanguage(targetLanguage);
                
                const afterLang = i18nManager.getCurrentLanguage();
                log('switchResults', `切换后语言: ${afterLang}`, 'info');

                if (afterLang === targetLanguage) {
                    log('switchResults', '✅ 语言切换成功', 'success');
                } else {
                    log('switchResults', '❌ 语言切换失败', 'error');
                }

                // 检查存储
                const storedLang = localStorage.getItem('ota_language_preference');
                log('switchResults', `localStorage更新: ${storedLang}`, storedLang === targetLanguage ? 'success' : 'warning');

                // 测试翻译
                const testKey = 'header.title';
                const translation = i18nManager.t(testKey);
                log('switchResults', `翻译测试 (${testKey}): "${translation}"`, 'info');

            } catch (error) {
                log('switchResults', `❌ 语言切换测试出错: ${error.message}`, 'error');
            }
        }

        function testEventSystem() {
            clear('eventResults');
            
            // 检查语言选择器元素
            const languageSelect = document.getElementById('languageSelect');
            if (languageSelect) {
                log('eventResults', '✅ 找到语言选择器元素', 'success');
                
                // 检查事件监听器
                const hasEventListener = languageSelect.onchange || 
                    (languageSelect._listeners && languageSelect._listeners.change);
                log('eventResults', `事件监听器: ${hasEventListener ? '已绑定' : '未绑定'}`, 
                    hasEventListener ? 'success' : 'warning');
                    
            } else {
                log('eventResults', '❌ 未找到语言选择器元素 (id="languageSelect")', 'error');
            }

            // 模拟事件触发
            try {
                const event = new Event('change', { bubbles: true });
                const testSelect = document.getElementById('testLanguage');
                testSelect.value = 'en';
                testSelect.dispatchEvent(event);
                log('eventResults', '✅ 事件触发测试完成', 'success');
            } catch (error) {
                log('eventResults', `❌ 事件触发测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkDependencies();
                setTimeout(checkInitialization, 500);
            }, 100);
        });
    </script>
</body>
</html>
