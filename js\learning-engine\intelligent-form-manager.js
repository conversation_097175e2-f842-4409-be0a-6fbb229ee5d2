/**
 * 智能学习型格式预处理引擎 - 智能表单管理器
 * 提供基于历史数据的智能表单自动填充功能
 * 支持相似订单识别和字段级智能建议
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-18
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 容器优先的服务访问函数
    function getServiceFromContainer(name) {
        return window.OTA.container ? window.OTA.container.get(name) : getService(name);
    }

    function getLearningConfig() {
        return getServiceFromContainer('learningConfig');
    }

    function getUserOperationLearner() {
        return getServiceFromContainer('userOperationLearner');
    }

    function getPatternMatchingEngine() {
        return getServiceFromContainer('patternMatchingEngine');
    }

    function getIntelligentCacheManager() {
        return getServiceFromContainer('intelligentCacheManager');
    }

    function getLogger() {
        return getServiceFromContainer('logger');
    }

    // 服务定位器函数 - 向后兼容
    function getService(serviceName) {
        const serviceMap = {
            'learningConfig': () => window.OTA.learningConfig || window.learningConfig,
            'userOperationLearner': () => window.OTA.userOperationLearner || window.userOperationLearner,
            'patternMatchingEngine': () => window.OTA.patternMatchingEngine || window.patternMatchingEngine,
            'intelligentCacheManager': () => window.OTA.intelligentCacheManager || window.intelligentCacheManager,
            'logger': () => window.OTA.logger || window.logger
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 智能表单管理器类
     * 提供基于历史数据的智能表单自动填充和字段建议功能
     */
    class IntelligentFormManager {
        constructor() {
            this.config = getLearningConfig();
            this.operationLearner = getUserOperationLearner();
            this.patternMatcher = getPatternMatchingEngine();
            this.cacheManager = getIntelligentCacheManager();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 配置参数
            this.formConfig = {
                similarityThreshold: 0.7,
                maxSuggestions: 5,
                autoFillConfidence: 0.9,
                cacheTimeout: 30 * 60 * 1000, // 30分钟
                fieldWeights: {
                    customerName: 0.15,
                    pickup: 0.2,
                    dropoff: 0.2,
                    pickupTime: 0.15,
                    passengerCount: 0.1,
                    carTypeId: 0.1,
                    otaPrice: 0.1
                }
            };
            
            // 缓存系统
            this.formCache = new Map();
            this.patternCache = new Map();
            this.suggestionCache = new Map();
            
            // 状态管理
            this.currentFormData = {};
            this.activeSuggestions = new Map();
            this.lastSimilarOrders = [];
            
            this.initialize();
        }

        /**
         * 初始化智能表单管理器
         */
        initialize() {
            try {
                this.setupEventListeners();
                this.initializeFormCache();
                this.setupAutoFillEngine();
                
                this.logger?.log('智能表单管理器初始化完成', 'info', {
                    version: this.version,
                    similarityThreshold: this.formConfig.similarityThreshold,
                    maxSuggestions: this.formConfig.maxSuggestions
                });
            } catch (error) {
                this.logger?.logError('智能表单管理器初始化失败', error);
            }
        }

        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 监听表单字段变化
            document.addEventListener('input', (e) => {
                if (e.target.matches('[data-field-name]')) {
                    this.handleFieldChange(e.target);
                }
            });

            // 监听表单聚焦事件
            document.addEventListener('focus', (e) => {
                if (e.target.matches('[data-field-name]')) {
                    this.showFieldSuggestions(e.target);
                }
            }, true);

            // 监听表单提交事件
            document.addEventListener('submit', (e) => {
                if (e.target.matches('#orderForm')) {
                    this.handleFormSubmission(e.target);
                }
            });
        }

        /**
         * 初始化表单缓存
         */
        initializeFormCache() {
            // 预加载最近的订单数据
            this.loadRecentOrders();
        }

        /**
         * 设置自动填充引擎
         */
        setupAutoFillEngine() {
            // 设置定期缓存清理
            setInterval(() => {
                this.cleanupCache();
            }, this.formConfig.cacheTimeout);
        }

        /**
         * 处理字段变化
         */
        async handleFieldChange(inputElement) {
            const fieldName = inputElement.dataset.fieldName;
            const currentValue = inputElement.value;
            
            if (!currentValue || currentValue.length < 2) {
                this.hideSuggestions(fieldName);
                return;
            }

            // 防抖处理
            clearTimeout(this.fieldChangeTimeout);
            this.fieldChangeTimeout = setTimeout(() => {
                this.generateFieldSuggestions(fieldName, currentValue);
            }, 300);
        }

        /**
         * 生成字段建议
         */
        async generateFieldSuggestions(fieldName, currentValue) {
            try {
                const cacheKey = `${fieldName}_${currentValue}`;
                
                // 检查缓存
                if (this.suggestionCache.has(cacheKey)) {
                    const cached = this.suggestionCache.get(cacheKey);
                    if (Date.now() - cached.timestamp < this.formConfig.cacheTimeout) {
                        this.displaySuggestions(fieldName, cached.suggestions);
                        return;
                    }
                }

                // 获取相似订单
                const similarOrders = await this.findSimilarOrders(fieldName, currentValue);
                
                // 提取字段建议
                const suggestions = this.extractFieldSuggestions(similarOrders, fieldName, currentValue);
                
                // 缓存结果
                this.suggestionCache.set(cacheKey, {
                    suggestions: suggestions,
                    timestamp: Date.now()
                });

                this.displaySuggestions(fieldName, suggestions);

            } catch (error) {
                this.logger?.logError('生成字段建议失败', error);
            }
        }

        /**
         * 查找相似订单
         */
        async findSimilarOrders(fieldName, currentValue) {
            try {
                // 获取历史订单
                const operations = this.operationLearner.getUserOperations();
                const orders = operations.map(op => op.context?.aiAnalysisResult).filter(Boolean);
                
                // 计算相似度
                const similarOrders = orders.map(order => ({
                    order,
                    similarity: this.calculateOrderSimilarity(order, fieldName, currentValue)
                }));
                
                // 按相似度排序
                similarOrders.sort((a, b) => b.similarity - a.similarity);
                
                return similarOrders.slice(0, this.formConfig.maxSuggestions);

            } catch (error) {
                this.logger?.logError('查找相似订单失败', error);
                return [];
            }
        }

        /**
         * 计算订单相似度
         */
        calculateOrderSimilarity(order, fieldName, currentValue) {
            try {
                let totalSimilarity = 0;
                let weightSum = 0;

                // 计算字段相似度
                if (order[fieldName]) {
                    const fieldSimilarity = this.patternMatcher.calculateSimilarity(
                        currentValue.toLowerCase(),
                        order[fieldName].toString().toLowerCase()
                    );
                    
                    const weight = this.formConfig.fieldWeights[fieldName] || 0.1;
                    totalSimilarity += fieldSimilarity * weight;
                    weightSum += weight;
                }

                // 计算上下文相似度
                Object.keys(this.formConfig.fieldWeights).forEach(key => {
                    if (key !== fieldName && order[key]) {
                        const contextSimilarity = this.patternMatcher.calculateSimilarity(
                            this.currentFormData[key]?.toString().toLowerCase() || '',
                            order[key].toString().toLowerCase()
                        );
                        
                        const weight = this.formConfig.fieldWeights[key] * 0.5; // 降低权重
                        totalSimilarity += contextSimilarity * weight;
                        weightSum += weight;
                    }
                });

                return weightSum > 0 ? totalSimilarity / weightSum : 0;

            } catch (error) {
                this.logger?.logError('计算订单相似度失败', error);
                return 0;
            }
        }

        /**
         * 提取字段建议
         */
        extractFieldSuggestions(similarOrders, fieldName, currentValue) {
            const suggestions = [];
            const seenValues = new Set();

            similarOrders.forEach(({ order, similarity }) => {
                if (similarity >= this.formConfig.similarityThreshold) {
                    const value = order[fieldName];
                    if (value && !seenValues.has(value) && value !== currentValue) {
                        suggestions.push({
                            value: value,
                            similarity: similarity,
                            confidence: Math.min(similarity * 1.2, 1.0),
                            source: 'historical'
                        });
                        seenValues.add(value);
                    }
                }
            });

            return suggestions.slice(0, this.formConfig.maxSuggestions);
        }

        /**
         * 显示字段建议
         */
        displaySuggestions(fieldName, suggestions) {
            const inputElement = document.querySelector(`[data-field-name="${fieldName}"]`);
            if (!inputElement) return;

            // 移除旧的建议容器
            const oldContainer = document.querySelector(`#suggestions-${fieldName}`);
            if (oldContainer) {
                oldContainer.remove();
            }

            if (suggestions.length === 0) return;

            // 创建建议容器
            const container = document.createElement('div');
            container.id = `suggestions-${fieldName}`;
            container.className = 'intelligent-suggestions';
            container.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                z-index: 1000;
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                max-height: 200px;
                overflow-y: auto;
                width: 100%;
            `;

            // 添加建议项
            suggestions.forEach((suggestion, index) => {
                const item = document.createElement('div');
                item.className = 'suggestion-item';
                item.style.cssText = `
                    padding: 8px 12px;
                    cursor: pointer;
                    border-bottom: 1px solid #eee;
                    font-size: 14px;
                `;
                item.textContent = suggestion.value;
                item.title = `相似度: ${(suggestion.similarity * 100).toFixed(1)}%`;

                item.addEventListener('click', () => {
                    inputElement.value = suggestion.value;
                    this.hideSuggestions(fieldName);
                    this.triggerFieldValidation(inputElement);
                });

                item.addEventListener('mouseenter', () => {
                    item.style.backgroundColor = '#f0f0f0';
                });

                item.addEventListener('mouseleave', () => {
                    item.style.backgroundColor = 'transparent';
                });

                container.appendChild(item);
            });

            // 插入到输入框下方
            inputElement.parentNode.style.position = 'relative';
            inputElement.parentNode.appendChild(container);

            // 点击外部关闭
            const closeSuggestions = (e) => {
                if (!container.contains(e.target) && e.target !== inputElement) {
                    this.hideSuggestions(fieldName);
                    document.removeEventListener('click', closeSuggestions);
                }
            };
            setTimeout(() => {
                document.addEventListener('click', closeSuggestions);
            }, 100);
        }

        /**
         * 隐藏建议
         */
        hideSuggestions(fieldName) {
            const container = document.querySelector(`#suggestions-${fieldName}`);
            if (container) {
                container.remove();
            }
        }

        /**
         * 显示字段建议
         */
        showFieldSuggestions(inputElement) {
            const fieldName = inputElement.dataset.fieldName;
            if (!fieldName) return;

            // 如果已有建议，直接显示
            const cacheKey = `${fieldName}_${inputElement.value}`;
            if (this.suggestionCache.has(cacheKey)) {
                const cached = this.suggestionCache.get(cacheKey);
                this.displaySuggestions(fieldName, cached.suggestions);
            }
        }

        /**
         * 智能自动填充
         */
        async smartAutoFill(currentFormData) {
            try {
                this.currentFormData = currentFormData;
                
                // 查找最相似的订单
                const similarOrder = await this.findMostSimilarOrder(currentFormData);
                
                if (similarOrder && similarOrder.similarity >= this.formConfig.autoFillConfidence) {
                    return this.generateAutoFillData(similarOrder.order);
                }

                return {};

            } catch (error) {
                this.logger?.logError('智能自动填充失败', error);
                return {};
            }
        }

        /**
         * 查找最相似的订单
         */
        async findMostSimilarOrder(currentFormData) {
            try {
                const operations = this.operationLearner.getUserOperations();
                const orders = operations.map(op => op.context?.aiAnalysisResult).filter(Boolean);
                
                const similarOrders = orders.map(order => ({
                    order,
                    similarity: this.calculateOverallSimilarity(currentFormData, order)
                }));
                
                similarOrders.sort((a, b) => b.similarity - a.similarity);
                
                return similarOrders[0] || null;

            } catch (error) {
                this.logger?.logError('查找最相似订单失败', error);
                return null;
            }
        }

        /**
         * 计算整体相似度
         */
        calculateOverallSimilarity(currentData, historicalOrder) {
            try {
                let totalSimilarity = 0;
                let weightSum = 0;

                Object.keys(this.formConfig.fieldWeights).forEach(fieldName => {
                    const currentValue = currentData[fieldName];
                    const historicalValue = historicalOrder[fieldName];
                    
                    if (currentValue && historicalValue) {
                        const similarity = this.patternMatcher.calculateSimilarity(
                            currentValue.toString().toLowerCase(),
                            historicalValue.toString().toLowerCase()
                        );
                        
                        const weight = this.formConfig.fieldWeights[fieldName];
                        totalSimilarity += similarity * weight;
                        weightSum += weight;
                    }
                });

                return weightSum > 0 ? totalSimilarity / weightSum : 0;

            } catch (error) {
                this.logger?.logError('计算整体相似度失败', error);
                return 0;
            }
        }

        /**
         * 生成自动填充数据
         */
        generateAutoFillData(similarOrder) {
            const autoFillData = {};
            
            Object.keys(this.formConfig.fieldWeights).forEach(fieldName => {
                if (similarOrder[fieldName] && !this.currentFormData[fieldName]) {
                    autoFillData[fieldName] = similarOrder[fieldName];
                }
            });

            return autoFillData;
        }

        /**
         * 加载最近订单
         */
        loadRecentOrders() {
            try {
                const operations = this.operationLearner.getUserOperations();
                const recentOrders = operations.slice(-50).map(op => ({
                    data: op.context?.aiAnalysisResult || {},
                    timestamp: op.timestamp
                }));
                
                this.lastSimilarOrders = recentOrders;
                
                // 缓存到内存
                this.cacheManager.set('recent_orders', recentOrders, {
                    ttl: 15 * 60 * 1000 // 15分钟
                });

            } catch (error) {
                this.logger?.logError('加载最近订单失败', error);
            }
        }

        /**
         * 处理表单提交
         */
        handleFormSubmission(formElement) {
            try {
                // 清理缓存
                this.cleanupCache();
                
                // 更新最近订单缓存
                this.loadRecentOrders();
                
                this.logger?.log('表单提交处理完成', 'info');
            } catch (error) {
                this.logger?.logError('处理表单提交失败', error);
            }
        }

        /**
         * 触发字段验证
         */
        triggerFieldValidation(inputElement) {
            // 触发自定义事件供其他模块监听
            const event = new CustomEvent('fieldValidated', {
                detail: {
                    field: inputElement.dataset.fieldName,
                    value: inputElement.value
                }
            });
            inputElement.dispatchEvent(event);
        }

        /**
         * 清理缓存
         */
        cleanupCache() {
            const now = Date.now();
            
            // 清理过期的建议缓存
            for (const [key, value] of this.suggestionCache.entries()) {
                if (now - value.timestamp > this.formConfig.cacheTimeout) {
                    this.suggestionCache.delete(key);
                }
            }
        }

        /**
         * 获取表单智能建议统计
         */
        getFormStats() {
            return {
                cacheSize: this.suggestionCache.size,
                lastSimilarOrders: this.lastSimilarOrders.length,
                activeSuggestions: this.activeSuggestions.size
            };
        }
    }

    // 创建全局实例
    const intelligentFormManager = new IntelligentFormManager();

    // 导出到全局命名空间
    window.OTA.intelligentFormManager = intelligentFormManager;
    window.intelligentFormManager = intelligentFormManager; // 向后兼容

    // 工厂函数
    window.getIntelligentFormManager = function() {
        return window.OTA.intelligentFormManager || window.intelligentFormManager;
    };

    console.log('智能表单管理器加载完成', {
        version: intelligentFormManager.version,
        similarityThreshold: intelligentFormManager.formConfig.similarityThreshold
    });

})();