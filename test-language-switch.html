<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言切换功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .demo-text {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>语言切换功能测试</h1>
    
    <div class="test-container">
        <h2>1. 国际化管理器可用性检查</h2>
        <button onclick="checkI18nAvailability()">检查国际化管理器</button>
        <div id="i18nAvailabilityResults"></div>
    </div>

    <div class="test-container">
        <h2>2. 语言切换测试</h2>
        <select id="testLanguageSelect">
            <option value="zh">中文</option>
            <option value="en">English</option>
        </select>
        <button onclick="testLanguageSwitch()">切换语言</button>
        <div id="languageSwitchResults"></div>
        
        <div class="demo-text">
            <h3 data-i18n="header.title">OTA订单处理系统</h3>
            <p data-i18n="login.title">用户登录</p>
            <p data-i18n="common.loading">加载中...</p>
        </div>
    </div>

    <div class="test-container">
        <h2>3. 翻译功能测试</h2>
        <button onclick="testTranslation()">测试翻译</button>
        <div id="translationResults"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/i18n.js"></script>

    <script>
        // 测试函数
        function displayResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function checkI18nAvailability() {
            clearResults('i18nAvailabilityResults');
            
            // 检查全局函数
            if (typeof window.getI18nManager === 'function') {
                displayResult('i18nAvailabilityResults', '✅ window.getI18nManager 函数存在', 'success');
            } else {
                displayResult('i18nAvailabilityResults', '❌ window.getI18nManager 函数不存在', 'error');
            }

            // 检查OTA命名空间
            if (window.OTA && window.OTA.getI18nManager) {
                displayResult('i18nAvailabilityResults', '✅ window.OTA.getI18nManager 函数存在', 'success');
            } else {
                displayResult('i18nAvailabilityResults', '❌ window.OTA.getI18nManager 函数不存在', 'error');
            }

            // 检查实例
            if (window.OTA && window.OTA.i18nManager) {
                displayResult('i18nAvailabilityResults', '✅ window.OTA.i18nManager 实例存在', 'success');
            } else {
                displayResult('i18nAvailabilityResults', '❌ window.OTA.i18nManager 实例不存在', 'error');
            }

            // 尝试获取实例
            try {
                const i18nManager = window.getI18nManager();
                if (i18nManager) {
                    displayResult('i18nAvailabilityResults', '✅ 成功获取国际化管理器实例', 'success');
                    displayResult('i18nAvailabilityResults', `当前语言: ${i18nManager.getCurrentLanguage()}`, 'info');
                } else {
                    displayResult('i18nAvailabilityResults', '❌ 获取国际化管理器实例失败', 'error');
                }
            } catch (error) {
                displayResult('i18nAvailabilityResults', `❌ 获取实例时出错: ${error.message}`, 'error');
            }
        }

        function testLanguageSwitch() {
            clearResults('languageSwitchResults');
            
            const selectedLanguage = document.getElementById('testLanguageSelect').value;
            
            try {
                const i18nManager = window.getI18nManager();
                if (!i18nManager) {
                    displayResult('languageSwitchResults', '❌ 无法获取国际化管理器', 'error');
                    return;
                }

                // 初始化管理器（如果还没有初始化）
                if (typeof i18nManager.init === 'function') {
                    i18nManager.init();
                }

                const oldLanguage = i18nManager.getCurrentLanguage();
                displayResult('languageSwitchResults', `当前语言: ${oldLanguage}`, 'info');
                
                // 切换语言
                i18nManager.setLanguage(selectedLanguage);
                
                const newLanguage = i18nManager.getCurrentLanguage();
                displayResult('languageSwitchResults', `切换后语言: ${newLanguage}`, 'info');
                
                if (newLanguage === selectedLanguage) {
                    displayResult('languageSwitchResults', '✅ 语言切换成功', 'success');
                } else {
                    displayResult('languageSwitchResults', '❌ 语言切换失败', 'error');
                }
                
            } catch (error) {
                displayResult('languageSwitchResults', `❌ 语言切换出错: ${error.message}`, 'error');
            }
        }

        function testTranslation() {
            clearResults('translationResults');
            
            try {
                const i18nManager = window.getI18nManager();
                if (!i18nManager) {
                    displayResult('translationResults', '❌ 无法获取国际化管理器', 'error');
                    return;
                }

                // 初始化管理器
                if (typeof i18nManager.init === 'function') {
                    i18nManager.init();
                }

                // 测试翻译
                const testKeys = [
                    'header.title',
                    'login.title', 
                    'common.loading',
                    'header.languageZh',
                    'header.languageEn'
                ];

                testKeys.forEach(key => {
                    const translation = i18nManager.t(key);
                    displayResult('translationResults', `${key}: "${translation}"`, 'info');
                });

                // 测试全局t函数
                if (typeof window.t === 'function') {
                    const globalTranslation = window.t('header.title');
                    displayResult('translationResults', `全局t函数测试: "${globalTranslation}"`, 'success');
                } else {
                    displayResult('translationResults', '❌ 全局t函数不存在', 'error');
                }

            } catch (error) {
                displayResult('translationResults', `❌ 翻译测试出错: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkI18nAvailability();
            }, 500);
        });
    </script>
</body>
</html>
