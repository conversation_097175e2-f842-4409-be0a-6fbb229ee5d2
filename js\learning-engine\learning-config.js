/**
 * 智能学习型格式预处理引擎 - 配置文件
 * 定义学习系统的核心配置参数和常量
 * 集成到现有OTA系统架构中
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 学习引擎配置类
     * 管理所有学习系统相关的配置参数
     */
    class LearningConfig {
        constructor() {
            // 系统基础配置
            this.system = {
                version: '1.0.0',
                enabled: true,                    // 学习系统总开关
                debugMode: false,                 // 调试模式
                logLevel: 'info',                // 日志级别: debug, info, warn, error
                maxMemoryUsage: 50 * 1024 * 1024 // 最大内存使用量 (50MB)
            };

            // 数据存储配置
            this.storage = {
                keyPrefix: 'ota-learning-',       // localStorage键前缀
                keys: {
                    userOperations: 'ota-learning-operations',    // 用户操作记录
                    learningRules: 'ota-learning-rules',          // 学习规则
                    systemStats: 'ota-learning-stats',           // 系统统计
                    userPreferences: 'ota-learning-preferences'  // 用户偏好
                },
                maxStorageSize: 10 * 1024 * 1024, // 最大存储大小 (10MB)
                compressionEnabled: true,          // 启用数据压缩
                backupEnabled: true,              // 启用数据备份
                retentionDays: 30                 // 数据保留天数
            };

            // 用户操作学习配置
            this.userOperationLearning = {
                enabled: true,                    // 用户操作学习开关
                recordThreshold: 3,               // 记录阈值（操作次数）
                maxRecordsPerField: 100,          // 每个字段最大记录数
                confidenceThreshold: 0.7,         // 置信度阈值
                learningRate: 0.1,                // 学习率
                decayFactor: 0.95                 // 衰减因子
            };

            // 错误分类配置
            this.errorClassification = {
                enabled: true,                    // 错误分类开关
                categories: {
                    TIME_FORMAT_ERROR: 'time_format_error',
                    NAME_EXTRACTION_ERROR: 'name_extraction_error',
                    LOCATION_PARSING_ERROR: 'location_parsing_error',
                    CONTACT_INFO_ERROR: 'contact_info_error',
                    PRICE_CONVERSION_ERROR: 'price_conversion_error',
                    DATE_FORMAT_ERROR: 'date_format_error',
                    PASSENGER_COUNT_ERROR: 'passenger_count_error',
                    FLIGHT_INFO_ERROR: 'flight_info_error',
                    UNKNOWN_ERROR: 'unknown_error'
                },
                minSimilarityScore: 0.6,          // 最小相似度分数
                maxCategoryDepth: 3               // 最大分类深度
            };

            // 模式匹配配置
            this.patternMatching = {
                enabled: true,                    // 模式匹配开关
                algorithms: {
                    regex: true,                  // 正则表达式匹配
                    fuzzy: true,                  // 模糊匹配
                    semantic: false               // 语义匹配（暂未实现）
                },
                similarity: {
                    threshold: 0.8,               // 相似度阈值
                    algorithm: 'levenshtein',     // 相似度算法
                    caseSensitive: false          // 大小写敏感
                },
                cache: {
                    enabled: true,                // 缓存开关
                    maxSize: 1000,                // 最大缓存条目数
                    ttl: 3600000                  // 缓存TTL (1小时)
                }
            };

            // 学习规则生成配置
            this.ruleGeneration = {
                enabled: true,                    // 规则生成开关
                minOccurrences: 3,                // 最小出现次数
                maxRulesPerField: 50,             // 每个字段最大规则数
                ruleTypes: {
                    REGEX_PATTERN: 'regex_pattern',
                    VALUE_MAPPING: 'value_mapping',
                    CONTEXT_RULE: 'context_rule',
                    TRANSFORMATION: 'transformation'
                },
                priority: {
                    HIGH: 3,
                    MEDIUM: 2,
                    LOW: 1
                },
                conflictResolution: 'latest_wins' // 冲突解决策略
            };

            // 预测性校正配置
            this.predictiveCorrection = {
                enabled: false,                   // 预测校正开关（Phase 2功能）
                confidenceThreshold: 0.9,         // 预测置信度阈值
                maxPredictions: 5,                // 最大预测数量
                autoApplyThreshold: 0.95          // 自动应用阈值
            };

            // 性能监控配置
            this.performance = {
                enabled: true,                    // 性能监控开关
                metricsCollection: {
                    responseTime: true,           // 响应时间
                    memoryUsage: true,            // 内存使用
                    cacheHitRate: true,           // 缓存命中率
                    learningAccuracy: true        // 学习准确率
                },
                alertThresholds: {
                    responseTime: 2000,           // 响应时间阈值 (2秒)
                    memoryUsage: 0.8,             // 内存使用阈值 (80%)
                    errorRate: 0.1                // 错误率阈值 (10%)
                }
            };

            // UI集成配置
            this.ui = {
                enabled: true,                    // UI功能开关
                correctionModal: {
                    enabled: true,                // 更正模态框
                    autoShow: false,              // 自动显示
                    position: 'center'            // 显示位置
                },
                historyPanel: {
                    enabled: true,                // 历史面板
                    maxDisplayItems: 20           // 最大显示项目数
                },
                notifications: {
                    enabled: true,                // 通知功能
                    duration: 3000                // 通知持续时间 (3秒)
                }
            };

            // API集成配置
            this.api = {
                integration: {
                    geminiService: true,          // 集成Gemini服务
                    multiOrderManager: true,      // 集成多订单管理器
                    realtimeAnalysis: true        // 集成实时分析
                },
                hooks: {
                    beforeAnalysis: true,         // 分析前钩子
                    afterAnalysis: true,          // 分析后钩子
                    onUserCorrection: true        // 用户更正钩子
                }
            };
        }

        /**
         * 获取配置值
         * @param {string} path - 配置路径，使用点号分隔
         * @returns {*} 配置值
         */
        get(path) {
            const keys = path.split('.');
            let value = this;
            
            for (const key of keys) {
                if (value && typeof value === 'object' && key in value) {
                    value = value[key];
                } else {
                    return undefined;
                }
            }
            
            return value;
        }

        /**
         * 设置配置值
         * @param {string} path - 配置路径
         * @param {*} value - 配置值
         */
        set(path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            let target = this;
            
            for (const key of keys) {
                if (!target[key] || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                target = target[key];
            }
            
            target[lastKey] = value;
        }

        /**
         * 验证配置完整性
         * @returns {boolean} 验证结果
         */
        validate() {
            try {
                // 检查必需的配置项
                const requiredPaths = [
                    'system.version',
                    'storage.keys.userOperations',
                    'userOperationLearning.confidenceThreshold',
                    'errorClassification.categories',
                    'patternMatching.similarity.threshold'
                ];

                for (const path of requiredPaths) {
                    if (this.get(path) === undefined) {
                        console.error(`学习引擎配置验证失败: 缺少必需配置 ${path}`);
                        return false;
                    }
                }

                return true;
            } catch (error) {
                console.error('学习引擎配置验证异常:', error);
                return false;
            }
        }

        /**
         * 重置为默认配置
         */
        reset() {
            const defaultConfig = new LearningConfig();
            Object.assign(this, defaultConfig);
        }

        /**
         * 导出配置为JSON
         * @returns {string} JSON字符串
         */
        toJSON() {
            return JSON.stringify(this, null, 2);
        }

        /**
         * 从JSON导入配置
         * @param {string} jsonString - JSON字符串
         */
        fromJSON(jsonString) {
            try {
                const config = JSON.parse(jsonString);
                Object.assign(this, config);
            } catch (error) {
                console.error('学习引擎配置导入失败:', error);
            }
        }
    }

    // 创建全局配置实例
    let learningConfigInstance = null;

    // 延迟初始化，确保容器已准备好
    function initializeLearningConfig() {
        if (!learningConfigInstance) {
            learningConfigInstance = new LearningConfig();
            
            // 验证配置
            if (!learningConfigInstance.validate()) {
                console.error('学习引擎配置验证失败，使用默认配置');
            }
            
            // 注册到容器
            if (window.OTA && window.OTA.container) {
                window.OTA.container.register('learningConfig', () => learningConfigInstance, {
                    singleton: true
                });
            }
        }
        return learningConfigInstance;
    }

    // 初始化实例
    learningConfigInstance = initializeLearningConfig();

    // 导出到全局命名空间
    window.OTA.learningConfig = learningConfigInstance;
    window.learningConfig = learningConfigInstance; // 向后兼容

    // 工厂函数 - 容器优先
    window.getLearningConfig = function() {
        try {
            if (window.OTA?.container?.has('learningConfig')) {
                return window.OTA.container.get('learningConfig');
            }
            return window.OTA.learningConfig || window.learningConfig;
        } catch (error) {
            console.warn('获取学习配置失败，使用回退:', error.message);
            return learningConfigInstance;
        }
    };

    // 确保在容器准备好后再注册
    if (window.OTA && window.OTA.container) {
        window.OTA.container.register('learningConfig', () => learningConfigInstance, {
            singleton: true
        });
    }

    console.log('学习引擎配置模块加载完成', {
        version: learningConfig.system.version,
        enabled: learningConfig.system.enabled
    });

})();
