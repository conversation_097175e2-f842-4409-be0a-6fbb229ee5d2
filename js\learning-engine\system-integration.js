/**
 * 智能学习型格式预处理引擎 - 系统集成模块
 * 负责将学习系统集成到现有的多订单检测系统中
 * 提供向后兼容性和开关控制机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getServiceFromContainer(name) {
        return window.OTA.container ? window.OTA.container.get(name) : getService(name);
    }

    function getLearningConfig() {
        return getServiceFromContainer('learningConfig');
    }

    function getUserOperationLearner() {
        return getServiceFromContainer('userOperationLearner');
    }

    function getErrorClassificationSystem() {
        return getServiceFromContainer('errorClassificationSystem');
    }

    function getRuleGenerationEngine() {
        return getServiceFromContainer('ruleGenerationEngine');
    }

    function getCorrectionInterface() {
        return getServiceFromContainer('correctionInterface');
    }

    function getLogger() {
        return getServiceFromContainer('logger');
    }

    function getAppState() {
        return getServiceFromContainer('appState');
    }

    /**
     * 学习系统集成器类
     * 管理学习系统与现有系统的集成
     */
    class LearningSystemIntegration {
        constructor() {
            this.config = getLearningConfig();
            this.logger = getLogger();
            this.appState = getAppState();
            
            this.version = '1.0.0';
            
            // 集成状态
            this.integrationStatus = {
                enabled: false,
                initialized: false,
                components: {
                    operationLearner: false,
                    errorClassifier: false,
                    ruleGenerator: false,
                    correctionInterface: false
                }
            };

            // 原始方法备份
            this.originalMethods = {};
            
            this.initialize();
        }

        /**
         * 初始化系统集成
         */
        initialize() {
            try {
                // 检查学习系统配置
                const learningEnabled = this.config.get('system.enabled');
                
                if (learningEnabled) {
                    this.enableLearningSystem();
                } else {
                    this.logger?.log('学习系统已禁用', 'info');
                }

                this.integrationStatus.initialized = true;
                
                this.logger?.log('学习系统集成初始化完成', 'info', {
                    version: this.version,
                    enabled: this.integrationStatus.enabled,
                    components: this.integrationStatus.components
                });

            } catch (error) {
                this.logger?.logError('学习系统集成初始化失败', error);
            }
        }

        /**
         * 启用学习系统
         */
        enableLearningSystem() {
            try {
                // 检查组件可用性
                this.checkComponentAvailability();
                
                // 集成到现有系统
                this.integrateWithExistingSystems();
                
                this.integrationStatus.enabled = true;
                
                this.logger?.log('学习系统已启用', 'success', {
                    components: this.integrationStatus.components
                });

            } catch (error) {
                this.logger?.logError('启用学习系统失败', error);
                this.integrationStatus.enabled = false;
            }
        }

        /**
         * 禁用学习系统
         */
        disableLearningSystem() {
            try {
                // 恢复原始方法
                this.restoreOriginalMethods();
                
                this.integrationStatus.enabled = false;
                
                this.logger?.log('学习系统已禁用', 'info');

            } catch (error) {
                this.logger?.logError('禁用学习系统失败', error);
            }
        }

        /**
         * 检查组件可用性
         */
        checkComponentAvailability() {
            const components = this.integrationStatus.components;
            
            // 检查用户操作学习器
            try {
                const operationLearner = getUserOperationLearner();
                components.operationLearner = !!operationLearner;
            } catch (error) {
                components.operationLearner = false;
            }

            // 检查错误分类系统
            try {
                const errorClassifier = getErrorClassificationSystem();
                components.errorClassifier = !!errorClassifier;
            } catch (error) {
                components.errorClassifier = false;
            }

            // 检查规则生成引擎
            try {
                const ruleGenerator = getRuleGenerationEngine();
                components.ruleGenerator = !!ruleGenerator;
            } catch (error) {
                components.ruleGenerator = false;
            }

            // 检查更正接口
            try {
                const correctionInterface = getCorrectionInterface();
                components.correctionInterface = !!correctionInterface;
            } catch (error) {
                components.correctionInterface = false;
            }
        }

        /**
         * 集成到现有系统
         */
        integrateWithExistingSystems() {
            // 集成到Gemini服务
            this.integrateWithGeminiService();
            
            // 集成到多订单管理器
            this.integrateWithMultiOrderManager();
            
            // 集成到实时分析管理器
            this.integrateWithRealtimeAnalysis();
        }

        /**
         * 集成到Gemini服务
         */
        integrateWithGeminiService() {
            try {
                const geminiService = window.OTA?.geminiService || window.geminiService;
                if (!geminiService) {
                    this.logger?.log('Gemini服务不可用，跳过集成', 'warn');
                    return;
                }

                // 备份原始方法
                if (!this.originalMethods.detectAndSplitMultiOrders) {
                    this.originalMethods.detectAndSplitMultiOrders = geminiService.detectAndSplitMultiOrders.bind(geminiService);
                }

                // 增强detectAndSplitMultiOrders方法
                const self = this;
                geminiService.detectAndSplitMultiOrders = async function(orderText) {
                    // 调用原始方法
                    const originalResult = await self.originalMethods.detectAndSplitMultiOrders(orderText);
                    
                    // 如果学习系统启用，进行学习分析
                    if (self.integrationStatus.enabled) {
                        try {
                            const enhancedResult = await self.enhanceMultiOrderDetection(orderText, originalResult);
                            return enhancedResult;
                        } catch (error) {
                            self.logger?.logError('学习系统增强失败，返回原始结果', error);
                            return originalResult;
                        }
                    }
                    
                    return originalResult;
                };

                this.logger?.log('已集成到Gemini服务', 'info');

            } catch (error) {
                this.logger?.logError('集成到Gemini服务失败', error);
            }
        }

        /**
         * 增强多订单检测
         */
        async enhanceMultiOrderDetection(orderText, originalResult) {
            try {
                // 记录分析上下文
                const analysisContext = {
                    orderText: orderText,
                    originalResult: originalResult,
                    timestamp: new Date().toISOString(),
                    sessionId: getUserOperationLearner()?.sessionId
                };

                // 应用学习规则（如果有）
                const enhancedResult = await this.applyLearningRules(originalResult, analysisContext);

                // 记录分析结果用于后续学习
                this.recordAnalysisForLearning(analysisContext, enhancedResult);

                return enhancedResult;

            } catch (error) {
                this.logger?.logError('增强多订单检测失败', error);
                return originalResult;
            }
        }

        /**
         * 应用学习规则
         */
        async applyLearningRules(originalResult, context) {
            try {
                if (!this.integrationStatus.components.ruleGenerator) {
                    return originalResult;
                }

                const ruleGenerator = getRuleGenerationEngine();
                const applicableRules = this.findApplicableRules(originalResult, context);

                if (applicableRules.length === 0) {
                    return originalResult;
                }

                // 应用规则增强结果
                const enhancedResult = this.applyRulesToResult(originalResult, applicableRules);
                
                this.logger?.log('应用学习规则完成', 'info', {
                    rulesApplied: applicableRules.length,
                    originalOrderCount: originalResult.orderCount,
                    enhancedOrderCount: enhancedResult.orderCount
                });

                return enhancedResult;

            } catch (error) {
                this.logger?.logError('应用学习规则失败', error);
                return originalResult;
            }
        }

        /**
         * 查找适用的规则
         */
        findApplicableRules(result, context) {
            try {
                const ruleGenerator = getRuleGenerationEngine();
                const allRules = ruleGenerator.getAllRules();
                
                // 筛选适用的规则
                const applicableRules = allRules.filter(rule => {
                    return rule.active && this.isRuleApplicable(rule, result, context);
                });

                return applicableRules;

            } catch (error) {
                this.logger?.logError('查找适用规则失败', error);
                return [];
            }
        }

        /**
         * 检查规则是否适用
         */
        isRuleApplicable(rule, result, context) {
            // 基于规则类型和上下文判断是否适用
            if (rule.type === 'context_rule') {
                return this.checkContextRuleApplicability(rule, context);
            }
            
            if (rule.type === 'value_mapping') {
                return this.checkValueMappingApplicability(rule, result);
            }
            
            return false;
        }

        /**
         * 检查上下文规则适用性
         */
        checkContextRuleApplicability(rule, context) {
            // 简单的上下文匹配
            return context.orderText && context.orderText.length > 0;
        }

        /**
         * 检查值映射规则适用性
         */
        checkValueMappingApplicability(rule, result) {
            // 检查结果中是否有可以应用映射的字段
            return result.orders && result.orders.length > 0;
        }

        /**
         * 将规则应用到结果
         */
        applyRulesToResult(originalResult, rules) {
            let enhancedResult = JSON.parse(JSON.stringify(originalResult));
            
            rules.forEach(rule => {
                try {
                    enhancedResult = this.applyIndividualRule(enhancedResult, rule);
                } catch (error) {
                    this.logger?.logError(`应用规则 ${rule.id} 失败`, error);
                }
            });

            return enhancedResult;
        }

        /**
         * 应用单个规则
         */
        applyIndividualRule(result, rule) {
            // 根据规则类型应用不同的逻辑
            switch (rule.type) {
                case 'value_mapping':
                    return this.applyValueMappingRule(result, rule);
                case 'context_rule':
                    return this.applyContextRule(result, rule);
                default:
                    return result;
            }
        }

        /**
         * 应用值映射规则
         */
        applyValueMappingRule(result, rule) {
            if (!rule.mapping || !rule.field) {
                return result;
            }

            result.orders.forEach(order => {
                if (order[rule.field] && rule.mapping[order[rule.field]]) {
                    order[rule.field] = rule.mapping[order[rule.field]];
                }
            });

            return result;
        }

        /**
         * 应用上下文规则
         */
        applyContextRule(result, rule) {
            // 上下文规则的应用逻辑
            return result;
        }

        /**
         * 记录分析结果用于学习
         */
        recordAnalysisForLearning(context, result) {
            try {
                if (!this.integrationStatus.components.operationLearner) {
                    return;
                }

                const operationLearner = getUserOperationLearner();
                
                // 记录分析操作
                operationLearner.recordOperation({
                    type: 'analysis',
                    field: 'multi_order_detection',
                    originalValue: context.orderText,
                    correctedValue: JSON.stringify(result),
                    context: {
                        analysisType: 'multi_order_detection',
                        originalResult: context.originalResult,
                        sessionId: context.sessionId
                    },
                    confidence: result.confidence || 0.5,
                    metadata: {
                        source: 'learning_system_integration',
                        timestamp: context.timestamp
                    }
                });

            } catch (error) {
                this.logger?.logError('记录分析结果失败', error);
            }
        }

        /**
         * 集成到多订单管理器
         */
        integrateWithMultiOrderManager() {
            try {
                const multiOrderManager = window.OTA?.multiOrderManager || window.multiOrderManager;
                if (!multiOrderManager) {
                    this.logger?.log('多订单管理器不可用，跳过集成', 'warn');
                    return;
                }

                // 添加学习系统相关的方法
                multiOrderManager.enableLearningMode = () => {
                    this.enableLearningSystem();
                };

                multiOrderManager.disableLearningMode = () => {
                    this.disableLearningSystem();
                };

                multiOrderManager.getLearningStats = () => {
                    return this.getLearningStats();
                };

                this.logger?.log('已集成到多订单管理器', 'info');

            } catch (error) {
                this.logger?.logError('集成到多订单管理器失败', error);
            }
        }

        /**
         * 集成到实时分析管理器
         */
        integrateWithRealtimeAnalysis() {
            try {
                const realtimeManager = window.OTA?.managers?.RealtimeAnalysisManager || 
                                      window.realtimeAnalysisManager;
                
                if (!realtimeManager) {
                    this.logger?.log('实时分析管理器不可用，跳过集成', 'warn');
                    return;
                }

                // 添加学习系统钩子
                const originalAnalyze = realtimeManager.analyze;
                if (originalAnalyze) {
                    const self = this;
                    realtimeManager.analyze = function(inputText) {
                        const result = originalAnalyze.call(this, inputText);
                        
                        // 如果学习系统启用，记录实时分析
                        if (self.integrationStatus.enabled) {
                            self.recordRealtimeAnalysis(inputText, result);
                        }
                        
                        return result;
                    };
                }

                this.logger?.log('已集成到实时分析管理器', 'info');

            } catch (error) {
                this.logger?.logError('集成到实时分析管理器失败', error);
            }
        }

        /**
         * 记录实时分析
         */
        recordRealtimeAnalysis(inputText, result) {
            try {
                if (!this.integrationStatus.components.operationLearner) {
                    return;
                }

                const operationLearner = getUserOperationLearner();
                
                operationLearner.recordOperation({
                    type: 'analysis',
                    field: 'realtime_analysis',
                    originalValue: inputText,
                    correctedValue: JSON.stringify(result),
                    context: {
                        analysisType: 'realtime',
                        timestamp: new Date().toISOString()
                    },
                    confidence: 0.7,
                    metadata: {
                        source: 'realtime_analysis_integration'
                    }
                });

            } catch (error) {
                this.logger?.logError('记录实时分析失败', error);
            }
        }

        /**
         * 恢复原始方法
         */
        restoreOriginalMethods() {
            try {
                // 恢复Gemini服务方法
                const geminiService = window.OTA?.geminiService || window.geminiService;
                if (geminiService && this.originalMethods.detectAndSplitMultiOrders) {
                    geminiService.detectAndSplitMultiOrders = this.originalMethods.detectAndSplitMultiOrders;
                }

                this.logger?.log('原始方法已恢复', 'info');

            } catch (error) {
                this.logger?.logError('恢复原始方法失败', error);
            }
        }

        /**
         * 获取学习统计信息
         */
        getLearningStats() {
            const stats = {
                integrationStatus: this.integrationStatus,
                components: {}
            };

            try {
                if (this.integrationStatus.components.operationLearner) {
                    const operationLearner = getUserOperationLearner();
                    stats.components.operationLearner = operationLearner.getStats();
                }

                if (this.integrationStatus.components.ruleGenerator) {
                    const ruleGenerator = getRuleGenerationEngine();
                    stats.components.ruleGenerator = ruleGenerator.getRuleStats();
                }

                if (this.integrationStatus.components.correctionInterface) {
                    const correctionInterface = getCorrectionInterface();
                    stats.components.correctionInterface = correctionInterface.getStats();
                }

            } catch (error) {
                this.logger?.logError('获取学习统计信息失败', error);
            }

            return stats;
        }

        /**
         * 切换学习系统状态
         */
        toggleLearningSystem() {
            if (this.integrationStatus.enabled) {
                this.disableLearningSystem();
            } else {
                this.enableLearningSystem();
            }
        }

        /**
         * 获取集成状态
         */
        getIntegrationStatus() {
            return { ...this.integrationStatus };
        }
    }

    // 创建全局实例
    const learningSystemIntegration = new LearningSystemIntegration();

    // 导出到全局命名空间
    window.OTA.learningSystemIntegration = learningSystemIntegration;
    window.learningSystemIntegration = learningSystemIntegration; // 向后兼容

    // 工厂函数
    window.getLearningSystemIntegration = function() {
        return window.OTA.learningSystemIntegration || window.learningSystemIntegration;
    };

    console.log('学习系统集成模块加载完成', {
        version: learningSystemIntegration.version,
        enabled: learningSystemIntegration.integrationStatus.enabled,
        components: learningSystemIntegration.integrationStatus.components
    });

})();
