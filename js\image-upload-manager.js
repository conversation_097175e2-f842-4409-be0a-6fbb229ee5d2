/**
 * 图片上传管理器
 * 负责图片上传、预览、删除和Gemini Vision API集成
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块 - 使用统一的服务定位器
function getLogger() {
    return getService('logger');
}

class ImageUploadManager {
    constructor() {
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        this.uploadedImages = [];
        this.isProcessing = false;
        this.init();
    }

    /**
     * 初始化图片上传管理器
     */
    init() {
        this.bindEvents();
        const logger = getLogger();
        if (logger) {
            logger.log('图片上传管理器已初始化', 'info');
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 文件输入变化事件
        const fileInput = document.getElementById('imageFileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // **修复**: 绑定正确的图片上传按钮
        const imageUploadButton = document.getElementById('imageUploadButton');
        if (imageUploadButton) {
            imageUploadButton.addEventListener('click', () => this.triggerFileSelect());
            const logger = getLogger();
            if (logger) {
                logger.log('图片上传按钮事件已绑定', 'success');
            }
        } else {
            const logger = getLogger();
            if (logger) {
                logger.log('图片上传按钮元素未找到', 'error');
            }
        }

        // 保留原有的上传区域拖拽支持（如果存在）
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => this.triggerFileSelect());
            uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        }
    }

    /**
     * 触发文件选择
     */
    triggerFileSelect() {
        const fileInput = document.getElementById('imageFileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    /**
     * 处理文件选择
     * @param {Event} event - 文件选择事件
     */
    handleFileSelect(event) {
        const files = event.target.files;
        if (files && files.length > 0) {
            this.processFiles(Array.from(files));
        }
    }

    /**
     * 处理拖拽悬停
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.classList.add('drag-over');
        }
    }

    /**
     * 处理拖拽离开
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.classList.remove('drag-over');
        }
    }

    /**
     * 处理文件拖放
     * @param {DragEvent} event - 拖放事件
     */
    handleDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.classList.remove('drag-over');
        }

        const files = event.dataTransfer.files;
        if (files && files.length > 0) {
            this.processFiles(Array.from(files));
        }
    }

    /**
     * 处理文件列表
     * @param {File[]} files - 文件列表
     */
    async processFiles(files) {
        for (const file of files) {
            if (this.validateFile(file)) {
                await this.uploadImage(file);
            }
        }
    }

    /**
     * 验证文件
     * @param {File} file - 文件对象
     * @returns {boolean} 是否有效
     */
    validateFile(file) {
        // 检查文件类型
        if (!this.allowedTypes.includes(file.type)) {
            const logger = getLogger();
            if (logger) {
                logger.log(`不支持的文件类型: ${file.type}`, 'error');
            }
            window.uiManager?.showAlert(`不支持的文件类型: ${file.type}`, 'error');
            return false;
        }

        // 检查文件大小
        if (file.size > this.maxFileSize) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
            const logger = getLogger();
            if (logger) {
                logger.log(`文件过大: ${sizeMB}MB，最大支持5MB`, 'error');
            }
            window.uiManager?.showAlert(`文件过大: ${sizeMB}MB，最大支持5MB`, 'error');
            return false;
        }

        return true;
    }

    /**
     * 上传图片
     * @param {File} file - 图片文件
     */
    async uploadImage(file) {
        try {
            this.isProcessing = true;
            this.updateUploadStatus('正在处理图片...', 'info');

            // 创建图片对象
            const imageData = {
                id: this.generateImageId(),
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                uploadTime: new Date().toISOString(),
                base64: null,
                extractedText: null,
                status: 'uploading'
            };

            // 转换为base64
            imageData.base64 = await this.fileToBase64(file);
            imageData.status = 'uploaded';

            // 添加到上传列表
            this.uploadedImages.push(imageData);

            // 更新UI
            this.renderImagePreview(imageData);
            this.updateUploadStatus('图片上传成功', 'success');

            getLogger().log(`图片上传成功: ${file.name}`, 'success');

            // 自动开始图片分析
            await this.analyzeImage(imageData);

        } catch (error) {
            getLogger().log(`图片上传失败: ${error.message}`, 'error');
            window.uiManager?.showAlert(`图片上传失败: ${error.message}`, 'error');
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 文件转base64
     * @param {File} file - 文件对象
     * @returns {Promise<string>} base64字符串
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 分析图片内容
     * @param {Object} imageData - 图片数据
     */
    async analyzeImage(imageData) {
        try {
            imageData.status = 'analyzing';
            this.updateImageStatus(imageData.id, 'analyzing');
            this.updateUploadStatus('正在分析图片内容...', 'info');

            // 调用Gemini Vision API
            const geminiService = window.OTA?.geminiService || window.getGeminiService?.();
            if (!geminiService) {
                throw new Error('Gemini服务未初始化');
            }

            // 调用增强版图片分析方法，返回标准化的订单数据
            const analysisResult = await geminiService.analyzeImage(imageData.base64);
            
            // 数据验证和质量检查
            const validatedResult = this.validateAnalysisResult(analysisResult);
            
            if (validatedResult && validatedResult.orderCount > 0) {
                imageData.analysisResult = validatedResult;
                imageData.status = 'analyzed';
                
                // 如果识别到多个订单，直接触发多订单流程
                if (validatedResult.isMultiOrder) {
                    this.handleMultiOrderFromImage(validatedResult);
                    this.updateUploadStatus(`识别到${validatedResult.orderCount}个订单，已启动多订单模式`, 'success');
                } else {
                    // 单个订单，转换为文本格式并填入
                    const orderText = this.convertOrderToText(validatedResult.orders[0]);
                    this.fillOrderDescription(orderText);
                    this.updateUploadStatus('单个订单识别完成', 'success');
                }
                
                this.updateImageStatus(imageData.id, 'analyzed');
                getLogger().log(`图片分析成功: ${imageData.name}`, 'success', {
                    orderCount: validatedResult.orderCount,
                    confidence: validatedResult.confidence,
                    validationPassed: true
                });
                
            } else {
                // 未识别到完整订单信息
                imageData.status = 'no-order';
                this.updateImageStatus(imageData.id, 'no-order');
                
                if (analysisResult && analysisResult.analysis) {
                    // 有部分信息或分析结果
                    this.fillOrderDescription(analysisResult.analysis);
                    this.updateUploadStatus('识别到部分信息', 'warning');
                } else {
                    this.updateUploadStatus('未检测到订单信息', 'warning');
                }
            }

        } catch (error) {
            imageData.status = 'error';
            this.updateImageStatus(imageData.id, 'error');
            this.updateUploadStatus(`分析失败: ${error.message}`, 'error');
            getLogger().log(`图片分析失败: ${error.message}`, 'error');
        }
    }

    /**
     * 验证图片分析结果的准确性和完整性
     * @param {Object} result - 原始分析结果
     * @returns {Object|null} 验证后的结果
     */
    validateAnalysisResult(result) {
        if (!result || typeof result !== 'object') {
            getLogger().log('图片分析结果格式无效', 'warning');
            return null;
        }

        // 基础结构验证
        const validatedResult = {
            isMultiOrder: Boolean(result.isMultiOrder),
            orderCount: Math.max(0, parseInt(result.orderCount) || 0),
            confidence: Math.max(0, Math.min(1, parseFloat(result.confidence) || 0)),
            analysis: result.analysis || '图片分析完成',
            orders: []
        };

        // 验证订单数组
        if (result.orders && Array.isArray(result.orders)) {
            validatedResult.orders = result.orders
                .map(order => this.validateOrderData(order))
                .filter(order => order !== null);
            
            // 更新实际订单数量
            validatedResult.orderCount = validatedResult.orders.length;
            validatedResult.isMultiOrder = validatedResult.orderCount > 1;
        }

        // 如果没有有效订单，返回null
        if (validatedResult.orderCount === 0) {
            getLogger().log('图片分析未找到有效订单', 'warning');
            return null;
        }

        getLogger().log('图片分析结果验证完成', 'success', {
            orderCount: validatedResult.orderCount,
            confidence: validatedResult.confidence
        });

        return validatedResult;
    }

    /**
     * 验证单个订单数据的完整性
     * @param {Object} order - 原始订单数据
     * @returns {Object|null} 验证后的订单数据
     */
    validateOrderData(order) {
        if (!order || typeof order !== 'object') {
            return null;
        }

        // 必需字段验证
        const hasRequiredFields = order.customerName || order.pickup || order.dropoff || order.pickupDate;
        if (!hasRequiredFields) {
            getLogger().log('订单缺少必需字段，跳过', 'warning');
            return null;
        }

        // 数据清理和标准化
        const validatedOrder = {
            rawText: order.rawText || '',
            customerName: this.cleanStringField(order.customerName),
            customerContact: this.cleanPhoneField(order.customerContact),
            customerEmail: this.validateEmail(order.customerEmail),
            pickup: this.cleanStringField(order.pickup),
            dropoff: this.cleanStringField(order.dropoff),
            pickupDate: this.validateDate(order.pickupDate),
            pickupTime: this.validateTime(order.pickupTime),
            passengerCount: Math.max(1, parseInt(order.passengerCount) || 1),
            luggageCount: Math.max(0, parseInt(order.luggageCount) || 1),
            flightInfo: this.cleanStringField(order.flightInfo),
            otaReferenceNumber: this.cleanStringField(order.otaReferenceNumber),
            otaPrice: this.validatePrice(order.otaPrice),
            currency: this.validateCurrency(order.currency),
            carTypeId: this.validateCarTypeId(order.carTypeId, order.passengerCount),
            subCategoryId: this.validateSubCategoryId(order.subCategoryId),
            drivingRegionId: this.validateDrivingRegionId(order.drivingRegionId),
            languagesIdArray: this.validateLanguagesArray(order.languagesIdArray),
            extraRequirement: this.cleanStringField(order.extraRequirement),
            babyChair: Boolean(order.babyChair),
            tourGuide: Boolean(order.tourGuide),
            meetAndGreet: Boolean(order.meetAndGreet)
        };

        return validatedOrder;
    }

    /**
     * 清理字符串字段
     * @param {any} value - 原始值
     * @returns {string|null} 清理后的字符串
     */
    cleanStringField(value) {
        if (!value || typeof value !== 'string') return null;
        const cleaned = value.trim().replace(/\s+/g, ' ');
        return cleaned.length > 0 ? cleaned : null;
    }

    /**
     * 清理电话号码字段
     * @param {any} phone - 原始电话号码
     * @returns {string|null} 清理后的电话号码
     */
    cleanPhoneField(phone) {
        if (!phone) return null;
        const cleaned = phone.toString().replace(/[^\d+\-\s]/g, '').trim();
        return cleaned.length >= 8 ? cleaned : null;
    }

    /**
     * 验证邮箱地址
     * @param {any} email - 原始邮箱
     * @returns {string|null} 验证后的邮箱
     */
    validateEmail(email) {
        if (!email || typeof email !== 'string') return null;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim()) ? email.trim() : null;
    }

    /**
     * 验证日期格式
     * @param {any} date - 原始日期
     * @returns {string|null} YYYY-MM-DD格式日期
     */
    validateDate(date) {
        if (!date) return null;
        const dateStr = String(date).trim();
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(dateStr)) {
            const dateObj = new Date(dateStr);
            if (!isNaN(dateObj.getTime())) {
                return dateStr;
            }
        }
        return null;
    }

    /**
     * 验证时间格式
     * @param {any} time - 原始时间
     * @returns {string|null} HH:MM格式时间
     */
    validateTime(time) {
        if (!time) return null;
        const timeStr = String(time).trim();
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (timeRegex.test(timeStr)) {
            const [hour, minute] = timeStr.split(':');
            return `${hour.padStart(2, '0')}:${minute}`;
        }
        return null;
    }

    /**
     * 验证价格
     * @param {any} price - 原始价格
     * @returns {number|null} 验证后的价格
     */
    validatePrice(price) {
        const numPrice = parseFloat(price);
        return (!isNaN(numPrice) && numPrice > 0) ? numPrice : null;
    }

    /**
     * 验证货币类型
     * @param {any} currency - 原始货币
     * @returns {string} 验证后的货币代码
     */
    validateCurrency(currency) {
        const validCurrencies = ['MYR', 'USD', 'SGD', 'CNY'];
        return validCurrencies.includes(currency) ? currency : 'MYR';
    }

    /**
     * 验证车型ID
     * @param {any} carTypeId - 原始车型ID
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 验证后的车型ID
     */
    validateCarTypeId(carTypeId, passengerCount) {
        const id = parseInt(carTypeId);
        if (!isNaN(id) && id > 0) return id;
        
        // 根据乘客人数推荐车型
        const passengers = parseInt(passengerCount) || 1;
        if (passengers <= 3) return 5; // 5 Seater
        if (passengers <= 4) return 37; // Extended 5
        if (passengers <= 5) return 15; // 7 Seater MPV
        if (passengers <= 6) return 32; // Velfire/Alphard
        if (passengers <= 7) return 20; // 10 Seater MPV
        return 23; // 14 Seater Van
    }

    /**
     * 验证服务类型ID
     * @param {any} subCategoryId - 原始服务类型ID
     * @returns {number} 验证后的服务类型ID
     */
    validateSubCategoryId(subCategoryId) {
        const id = parseInt(subCategoryId);
        const validIds = [2, 3, 4]; // 接机、送机、包车
        return validIds.includes(id) ? id : 2; // 默认接机
    }

    /**
     * 验证行驶区域ID
     * @param {any} drivingRegionId - 原始区域ID
     * @returns {number} 验证后的区域ID
     */
    validateDrivingRegionId(drivingRegionId) {
        const id = parseInt(drivingRegionId);
        return (!isNaN(id) && id > 0) ? id : 1; // 默认吉隆坡
    }

    /**
     * 验证语言数组
     * @param {any} languages - 原始语言数组
     * @returns {Array} 验证后的语言ID数组
     */
    validateLanguagesArray(languages) {
        if (!Array.isArray(languages)) return [2, 4]; // 默认英文+中文
        const validIds = languages.filter(id => {
            const numId = parseInt(id);
            return !isNaN(numId) && numId > 0;
        });
        return validIds.length > 0 ? validIds : [2, 4];
    }

    /**
     * 处理从图片识别到的多订单
     * @param {Object} analysisResult - 图片分析结果
     */
    handleMultiOrderFromImage(analysisResult) {
        try {
            // 获取多订单管理器
            const multiOrderManager = window.OTA?.multiOrderManager || window.getMultiOrderManager?.();
            if (!multiOrderManager) {
                getLogger().log('多订单管理器未初始化', 'error');
                return;
            }

            // 直接显示多订单面板，传入分析结果
            multiOrderManager.showMultiOrderPanel(analysisResult);
            
            getLogger().log('图片多订单处理完成', 'success', {
                orderCount: analysisResult.orderCount,
                source: 'image_analysis'
            });

        } catch (error) {
            getLogger().logError('图片多订单处理失败', error);
            // 回退到文本模式
            const combinedText = analysisResult.orders.map(order => order.rawText || this.convertOrderToText(order)).join('\n\n');
            this.fillOrderDescription(combinedText);
        }
    }

    /**
     * 将订单对象转换为可读文本格式
     * @param {Object} order - 订单对象
     * @returns {string} 格式化的订单文本
     */
    convertOrderToText(order) {
        const parts = [];
        
        if (order.customerName) parts.push(`客户: ${order.customerName}`);
        if (order.customerContact) parts.push(`电话: ${order.customerContact}`);
        if (order.customerEmail) parts.push(`邮箱: ${order.customerEmail}`);
        if (order.flightInfo) parts.push(`航班: ${order.flightInfo}`);
        if (order.pickupDate) parts.push(`日期: ${order.pickupDate}`);
        if (order.pickupTime) parts.push(`时间: ${order.pickupTime}`);
        if (order.pickup) parts.push(`上车: ${order.pickup}`);
        if (order.dropoff) parts.push(`目的地: ${order.dropoff}`);
        if (order.passengerCount) parts.push(`乘客: ${order.passengerCount}人`);
        if (order.luggageCount) parts.push(`行李: ${order.luggageCount}件`);
        if (order.otaPrice && order.currency) parts.push(`价格: ${order.otaPrice} ${order.currency}`);
        if (order.extraRequirement) parts.push(`要求: ${order.extraRequirement}`);
        
        return parts.join('\n');
    }

    /**
     * 填入订单描述（增强版）
     * @param {string} text - 提取的文本
     * @param {Object} options - 可选配置参数
     */
    fillOrderDescription(text, options = {}) {
        const orderInput = document.getElementById('orderInput');
        if (!orderInput) {
            getLogger().log('订单输入框未找到', 'error');
            return;
        }

        const {
            append = true,           // 是否追加到现有内容
            triggerAnalysis = true,  // 是否触发实时分析
            autoDetection = true,    // 是否自动检测多订单
            separator = '\n\n'       // 内容分隔符
        } = options;

        try {
            const currentText = orderInput.value.trim();
            let newText;
            
            if (append && currentText) {
                // 追加模式：在现有内容后添加
                newText = `${currentText}${separator}${text}`;
            } else {
                // 替换模式：直接使用新文本
                newText = text;
            }

            // 数据质量检查
            const qualityScore = this.assessTextQuality(newText);
            if (qualityScore < 0.3) {
                getLogger().log('文本质量较低，可能包含错误信息', 'warning', {
                    qualityScore: qualityScore
                });
            }

            orderInput.value = newText;
            
            // 触发输入事件以启动实时分析（🔧 修复：添加标记防止递归）
            if (triggerAnalysis) {
                const event = new Event('input', { bubbles: true });
                event._programmaticTrigger = true;
                orderInput.dispatchEvent(event);
            }
            
            // 智能多订单检测
            if (autoDetection && this.shouldTriggerMultiOrderDetection(newText)) {
                // 使用定时器确保输入事件处理完成后再触发多订单检测
                setTimeout(() => {
                    this.triggerMultiOrderDetection(newText);
                }, 500);
            }
            
            // 记录操作结果
            getLogger().log('订单内容已更新', 'success', {
                contentLength: newText.length,
                qualityScore: qualityScore,
                autoDetection: autoDetection && this.shouldTriggerMultiOrderDetection(newText),
                mode: append ? 'append' : 'replace'
            });

            // 更新用户界面状态
            this.updateContentStatus(newText, qualityScore);

        } catch (error) {
            getLogger().logError('更新订单内容失败', error);
        }
    }

    /**
     * 评估文本质量
     * @param {string} text - 要评估的文本
     * @returns {number} 质量分数 (0-1)
     */
    assessTextQuality(text) {
        if (!text || typeof text !== 'string') return 0;

        let score = 0.5; // 基础分数
        const length = text.trim().length;

        // 长度评分
        if (length > 50) score += 0.1;
        if (length > 200) score += 0.1;

        // 关键字段检测
        const keyFields = [
            /客户|姓名|customer|name/i,
            /电话|手机|phone|contact/i,
            /日期|时间|date|time/i,
            /地点|地址|location|address/i,
            /乘客|人数|passenger/i
        ];

        const fieldMatches = keyFields.filter(regex => regex.test(text)).length;
        score += (fieldMatches / keyFields.length) * 0.3;

        // 结构化内容检测
        if (text.includes(':') || text.includes('：')) score += 0.1;
        if (/\d{4}-\d{2}-\d{2}/.test(text)) score += 0.1; // 日期格式
        if (/\d{1,2}:\d{2}/.test(text)) score += 0.1; // 时间格式

        return Math.min(score, 1.0);
    }

    /**
     * 判断是否应该触发多订单检测
     * @param {string} text - 文本内容
     * @returns {boolean} 是否触发检测
     */
    shouldTriggerMultiOrderDetection(text) {
        if (!text || text.length < 100) return false;

        // 多订单指示器
        const multiOrderIndicators = [
            /订单\s*[1-9]/i,
            /order\s*[1-9]/i,
            /---+/,
            /===+/,
            /第[一二三四五六七八九十\d]+个?/,
            /^\d+[\.、]/m,
            /(接机|送机|包车).*?(接机|送机|包车)/i
        ];

        return multiOrderIndicators.some(regex => regex.test(text));
    }

    /**
     * 更新内容状态显示
     * @param {string} text - 文本内容
     * @param {number} qualityScore - 质量分数
     */
    updateContentStatus(text, qualityScore) {
        const statusElement = document.getElementById('contentQualityStatus');
        if (statusElement) {
            let statusText = `内容长度: ${text.length} 字符`;
            let statusClass = 'info';

            if (qualityScore >= 0.7) {
                statusText += ' | 质量: 良好';
                statusClass = 'success';
            } else if (qualityScore >= 0.5) {
                statusText += ' | 质量: 一般';
                statusClass = 'warning';
            } else {
                statusText += ' | 质量: 需要改进';
                statusClass = 'error';
            }

            statusElement.textContent = statusText;
            statusElement.className = `content-status ${statusClass}`;
        }
    }

    /**
     * 触发多订单检测（增强版）
     * @param {string} text - 订单文本
     * @param {Object} options - 检测选项
     */
    async triggerMultiOrderDetection(text, options = {}) {
        const {
            forceDetection = false,  // 强制检测，忽略条件判断
            showProgress = true,     // 显示检测进度
            retryCount = 2          // 重试次数
        } = options;

        try {
            // 预检查
            if (!forceDetection && !this.shouldTriggerMultiOrderDetection(text)) {
                getLogger().log('文本不符合多订单检测条件', 'info');
                return false;
            }

            const geminiService = window.OTA?.geminiService || window.getGeminiService?.();
            if (!geminiService) {
                getLogger().log('Gemini服务未初始化，跳过多订单检测', 'warning');
                return false;
            }

            if (showProgress) {
                this.updateUploadStatus('正在检测多订单...', 'info');
            }

            let detectionResult = null;
            let lastError = null;

            // 重试机制
            for (let attempt = 0; attempt <= retryCount; attempt++) {
                try {
                    getLogger().log(`多订单检测尝试 ${attempt + 1}/${retryCount + 1}`, 'info');
                    
                    detectionResult = await geminiService.detectAndSplitMultiOrders(text);
                    
                    // 验证检测结果
                    if (this.validateDetectionResult(detectionResult)) {
                        break; // 成功，跳出重试循环
                    } else {
                        throw new Error('检测结果验证失败');
                    }

                } catch (error) {
                    lastError = error;
                    getLogger().log(`检测尝试 ${attempt + 1} 失败: ${error.message}`, 'warning');
                    
                    if (attempt < retryCount) {
                        // 等待一段时间后重试
                        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
                    }
                }
            }

            // 处理检测结果
            if (detectionResult && detectionResult.isMultiOrder && detectionResult.orderCount > 1) {
                // 检测到多订单，显示多订单面板
                const multiOrderManager = window.OTA?.multiOrderManager || window.getMultiOrderManager?.();
                if (multiOrderManager) {
                    multiOrderManager.showMultiOrderPanel(detectionResult);
                    
                    if (showProgress) {
                        this.updateUploadStatus(`自动检测到${detectionResult.orderCount}个订单`, 'success');
                    }
                    
                    getLogger().log('多订单检测成功', 'success', {
                        orderCount: detectionResult.orderCount,
                        confidence: detectionResult.confidence,
                        source: 'auto_detection'
                    });
                    
                    return true;
                } else {
                    getLogger().log('多订单管理器未初始化', 'error');
                    return false;
                }
            } else {
                // 未检测到多订单
                if (showProgress && detectionResult) {
                    this.updateUploadStatus('未检测到多订单', 'info');
                }
                
                getLogger().log('未检测到多订单', 'info', {
                    detectionResult: detectionResult,
                    textLength: text.length
                });
                
                return false;
            }

        } catch (error) {
            if (showProgress) {
                this.updateUploadStatus(`多订单检测失败: ${error.message}`, 'error');
            }
            
            getLogger().logError('多订单检测失败', error);
            return false;
        }
    }

    /**
     * 验证多订单检测结果
     * @param {Object} result - 检测结果
     * @returns {boolean} 是否有效
     */
    validateDetectionResult(result) {
        if (!result || typeof result !== 'object') {
            return false;
        }

        // 基础字段验证
        const hasBasicFields = typeof result.isMultiOrder === 'boolean' &&
                              typeof result.orderCount === 'number' &&
                              typeof result.confidence === 'number';

        if (!hasBasicFields) {
            getLogger().log('检测结果缺少基础字段', 'warning');
            return false;
        }

        // 订单数据验证
        if (result.isMultiOrder && result.orderCount > 1) {
            if (!Array.isArray(result.orders) || result.orders.length !== result.orderCount) {
                getLogger().log('多订单数据不一致', 'warning');
                return false;
            }

            // 验证每个订单的最低要求
            const validOrders = result.orders.filter(order => 
                order && typeof order === 'object' && 
                (order.customerName || order.pickup || order.dropoff)
            );

            if (validOrders.length < result.orderCount * 0.8) { // 至少80%的订单有效
                getLogger().log('有效订单比例过低', 'warning');
                return false;
            }
        }

        return true;
    }

    /**
     * 获取图片分析性能统计
     * @returns {Object} 性能统计数据
     */
    getAnalysisStats() {
        const totalImages = this.uploadedImages.length;
        const analyzedImages = this.uploadedImages.filter(img => img.status === 'analyzed').length;
        const failedImages = this.uploadedImages.filter(img => img.status === 'error').length;
        const noOrderImages = this.uploadedImages.filter(img => img.status === 'no-order').length;

        return {
            total: totalImages,
            analyzed: analyzedImages,
            failed: failedImages,
            noOrder: noOrderImages,
            successRate: totalImages > 0 ? (analyzedImages / totalImages * 100).toFixed(1) : 0,
            averageConfidence: this.calculateAverageConfidence()
        };
    }

    /**
     * 计算平均置信度
     * @returns {number} 平均置信度
     */
    calculateAverageConfidence() {
        const analyzedImages = this.uploadedImages.filter(img => 
            img.status === 'analyzed' && img.analysisResult && img.analysisResult.confidence
        );

        if (analyzedImages.length === 0) return 0;

        const totalConfidence = analyzedImages.reduce((sum, img) => 
            sum + img.analysisResult.confidence, 0
        );

        return (totalConfidence / analyzedImages.length * 100).toFixed(1);
    }

    /**
     * 渲染图片预览
     * @param {Object} imageData - 图片数据
     */
    renderImagePreview(imageData) {
        const container = document.getElementById('imagePreviewContainer');
        if (!container) return;

        const previewElement = document.createElement('div');
        previewElement.className = 'image-preview-item';
        previewElement.setAttribute('data-image-id', imageData.id);
        
        previewElement.innerHTML = `
            <div class="image-preview-wrapper">
                <img src="${imageData.base64}" alt="${imageData.name}" class="preview-image">
                <div class="image-overlay">
                    <div class="image-status" data-status="${imageData.status}">
                        <span class="status-icon">${this.getStatusIcon(imageData.status)}</span>
                        <span class="status-text">${this.getStatusText(imageData.status)}</span>
                    </div>
                    <div class="image-actions">
                        <button type="button" class="btn btn-icon btn-sm delete-image-btn" data-image-id="${imageData.id}" title="删除图片">
                            🗑️
                        </button>
                    </div>
                </div>
            </div>
            <div class="image-info">
                <div class="image-name">${imageData.name}</div>
                <div class="image-size">${this.formatFileSize(imageData.size)}</div>
            </div>
        `;

        container.appendChild(previewElement);

        // 绑定删除事件
        const deleteBtn = previewElement.querySelector('.delete-image-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => this.deleteImage(imageData.id));
        }
    }

    /**
     * 更新图片状态
     * @param {string} imageId - 图片ID
     * @param {string} status - 状态
     */
    updateImageStatus(imageId, status) {
        const previewItem = document.querySelector(`[data-image-id="${imageId}"]`);
        if (previewItem) {
            const statusElement = previewItem.querySelector('.image-status');
            if (statusElement) {
                statusElement.setAttribute('data-status', status);
                statusElement.querySelector('.status-icon').textContent = this.getStatusIcon(status);
                statusElement.querySelector('.status-text').textContent = this.getStatusText(status);
            }
        }
    }

    /**
     * 更新上传状态
     * @param {string} message - 状态消息
     * @param {string} type - 消息类型
     */
    updateUploadStatus(message, type = 'info') {
        const statusElement = document.getElementById('imageUploadStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `upload-status ${type}`;
        }
    }

    /**
     * 删除图片
     * @param {string} imageId - 图片ID
     */
    deleteImage(imageId) {
        // 从数组中移除
        this.uploadedImages = this.uploadedImages.filter(img => img.id !== imageId);
        
        // 从DOM中移除
        const previewItem = document.querySelector(`[data-image-id="${imageId}"]`);
        if (previewItem) {
            previewItem.remove();
        }
        
        getLogger().log(`图片已删除: ${imageId}`, 'info');
    }

    /**
     * 获取状态图标
     * @param {string} status - 状态
     * @returns {string} 图标
     */
    getStatusIcon(status) {
        const icons = {
            uploading: '⏳',
            uploaded: '✅',
            analyzing: '🔍',
            analyzed: '📋',
            'no-order': '❓',
            'no-text': '❔',
            error: '❌'
        };
        return icons[status] || '❓';
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const texts = {
            uploading: '上传中',
            uploaded: '已上传',
            analyzing: '分析中',
            analyzed: '已解析',
            'no-order': '无订单',
            'no-text': '无文本',
            error: '错误'
        };
        return texts[status] || '未知';
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 生成图片ID
     * @returns {string} 唯一ID
     */
    generateImageId() {
        return 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 清空所有图片
     */
    clearAllImages() {
        this.uploadedImages = [];
        const container = document.getElementById('imagePreviewContainer');
        if (container) {
            container.innerHTML = '';
        }
        this.updateUploadStatus('', 'info');
    }

    /**
     * 获取上传的图片列表
     * @returns {Array} 图片列表
     */
    getUploadedImages() {
        return this.uploadedImages;
    }

    /**
     * 获取提取的文本内容
     * @returns {string} 合并的文本内容
     */
    getExtractedText() {
        return this.uploadedImages
            .filter(img => img.extractedText)
            .map(img => img.extractedText)
            .join('\n\n');
    }
}

// 创建全局实例
let imageUploadManagerInstance = null;

/**
 * 获取图片上传管理器实例
 * @returns {ImageUploadManager} 管理器实例
 */
function getImageUploadManager() {
    if (!imageUploadManagerInstance) {
        imageUploadManagerInstance = new ImageUploadManager();
    }
    return imageUploadManagerInstance;
}

// 导出到全局作用域
window.ImageUploadManager = ImageUploadManager;
window.getImageUploadManager = getImageUploadManager;
