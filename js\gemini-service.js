/**
 * Gemini AI服务模块
 * 负责与Google Gemini API的交互，提供订单内容智能解析功能
 * 支持实时分析和批量处理
 * 重构为传统script标签加载方式
 *
 * 配置信息：
 * - 模型版本：Gemini 2.0 Flash (gemini-2.5-flash-lite-preview-06-17)
 * - API密钥：内嵌配置（个人自用项目）
 * - 智能ID填充：基于api return id list.md数据映射
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getAppState() {
        return getService('appState');
    }

    function getLogger() {
        return getService('logger');
    }

class GeminiService {
    constructor() {
        // 内嵌API密钥配置（个人自用项目，忽略安全警告）
        this.apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';

        // 更新为Gemini 2.0 Flash模型
        this.modelVersion = 'gemini-2.5-flash-lite-preview-06-17';
        this.baseURL = `https://generativelanguage.googleapis.com/v1beta/models/${this.modelVersion}:generateContent`;
        this.timeout = 30000;
        
        // 实时分析配置
        this.realtimeConfig = {
            enabled: true,
            debounceDelay: 1500, // 1.5秒防抖延迟
            minInputLength: 20, // 最小输入长度才触发分析
            maxRetries: 2, // 最大重试次数
            confidenceThreshold: 0.3 // 最低置信度阈值
        };
        
        // 分析状态管理
        this.analysisState = {
            isAnalyzing: false,
            lastAnalyzedText: '',
            currentRequest: null,
            analysisHistory: []
        };
        
        // 🔧 新增：提示词缓存和验证机制
        this.promptCache = new Map();
        this.verificationConfig = {
            enabled: true,
            attempts: 3,
            consistencyThreshold: 0.8,
            timeout: 30000,
            requireConsistency: true
        };
        this.validationStats = {
            totalValidations: 0,
            successfulValidations: 0,
            consistentResults: 0,
            averageAttempts: 0,
            cacheHits: 0,
            cacheMisses: 0
        };

        // OTA参考号识别规则配置
        this.otaReferenceConfig = {
            // 排除规则 - 这些内容不应被识别为参考号
            excludePatterns: {
                customerName: /^[A-Za-z\s\u4e00-\u9fff]{2,50}$/,
                phoneNumber: /^[\+]?[\d\s\-\(\)]{8,20}$/,
                flightNumber: /^[A-Z]{2}\d{3,4}$/,
                dateTime: /^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{1,2}:\d{2}/,
                address: /^[\u4e00-\u9fff\w\s,\.\-#]{10,}$/,
                email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                price: /^[A-Z]{3}?\s*\d+(\.\d{2})?$/,
                serviceType: /^(接机|送机|包车|charter|pickup|dropoff)$/i,
                luggageCount: /^\d+\s*(件|pcs|luggage)$/i,
                passengerCount: /^\d+\s*(人|pax|passenger)$/i
            },

            // 目标识别规则 - 这些格式可能是参考号
            targetPatterns: {
                chongDealer: /^CD[A-Z0-9]{6,12}$/,
                generic: /^[A-Z]{2,4}[0-9]{6,10}$/,
                withSeparator: /^[A-Z0-9]{2,6}[-_][A-Z0-9]{3,8}$/,
                alphanumeric: /^[A-Z0-9]{8,15}$/,
                withPrefix: /^(GMH|OTA|REF|ORDER|BOOK)-[A-Z0-9]{4,10}$/i,
                teamNumber: /团号[:：]\s*([A-Z0-9\-]{6,20})/i,
                confirmationCode: /确认号[:：]\s*([A-Z0-9\-]{6,20})/i
            },

            // 平台特定规则
            platformRules: {
                'Chong Dealer': {
                    patterns: [/^CD[A-Z0-9]{6,12}$/, /^CHONG[A-Z0-9]{4,8}$/i],
                    priority: 10
                },
                'Klook': {
                    patterns: [/^KL[A-Z0-9]{8,12}$/, /^KLOOK[A-Z0-9]{4,8}$/i],
                    priority: 9
                },
                'KKday': {
                    patterns: [/^KK[A-Z0-9]{8,12}$/, /^KKDAY[A-Z0-9]{4,8}$/i],
                    priority: 9
                },
                'Generic': {
                    patterns: [/^[A-Z]{2,4}[0-9]{6,10}$/, /^[A-Z0-9]{8,15}$/],
                    priority: 5
                }
            }
        };

        // 通用化提示词模板系统
        this.promptTemplates = {
            // 基础系统角色定义
            systemRole: {
                base: `你是一个高度智能的、专为马来西亚和新加坡设计的用车服务订单处理引擎。`,
                task: `你的 **唯一任务** 是精确地、无逻辑遗漏地解析非结构化订单文本，并根据下方定义的规则，直接计算并输出一个 **JSON数组**。`,
                format: `即使只有一个订单，也必须以数组形式 \\\`[ { ... } ]\\\` 返回。`
            },

            // JSON Schema定义
            jsonSchema: {
                header: `### **输出格式契约 (JSON Schema)**\n\n你必须严格按照以下JSON结构输出。**所有未在文本中找到对应信息的字段，其值必须为 \\\`null\\\`**，不能省略字段。`,
                structure: `\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null",
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null",
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null",
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\``
            },

            // 容错和重试配置
            errorHandling: {
                retryStrategies: [
                    'simplify_prompt',
                    'add_examples',
                    'reduce_complexity',
                    'fallback_parsing'
                ],
                fallbackResponses: {
                    parseError: `解析失败，请检查输入格式`,
                    timeoutError: `请求超时，请稍后重试`,
                    formatError: `输出格式不正确，请重新生成`
                }
            }
        };

        // 智能ID填充映射表（基于api return id list.md）
        this.idMappings = {
            // 后台用户映射：邮箱 -> ID
            backendUsers: {
                '<EMAIL>': 37,
                '<EMAIL>': 89,
                '<EMAIL>': 310,
                '<EMAIL>': 311,
                '<EMAIL>': 312,
                'SMW <EMAIL>': 342,
                'SMW <EMAIL>': 343,
                '<EMAIL>': 420,
                '<EMAIL>': 421,
                '空空@gomyhire.com': 777,
                '<EMAIL>': 1047,
                '<EMAIL>': 1181,
                '<EMAIL>': 1201,
                'Swee <EMAIL>': 1652,
                'Skymirror <EMAIL>': 2249,
                '<EMAIL>': 2446,
                '<EMAIL>': 2666
            },
            // 子分类映射
            subCategories: [
                { id: 2, name: 'Pickup' },
                { id: 3, name: 'Dropoff' },
                { id: 4, name: 'Charter' }
            ],
            // 车型映射（基于乘客人数，优先使用5 Seater）
            carTypes: [
                { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', passengerLimit: 3 },
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10 },
                { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12 },
                { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29 },
                { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43 },
                { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', passengerLimit: 0 },
                { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', passengerLimit: 0 }
            ],
            // 行驶区域映射
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)' },
                { id: 2, name: 'Penang (PNG)' },
                { id: 3, name: 'Johor (JB)' },
                { id: 4, name: 'Sabah (SBH)' },
                { id: 5, name: 'Singapore (SG)' },
                { id: 6, name: '携程专车 (CTRIP)' },
                { id: 8, name: 'Complete (COMPLETE)' },
                { id: 9, name: 'Paging (PG)' },
                { id: 10, name: 'Charter (CHRT)' },
                { id: 12, name: 'Malacca (MLK)' },
                { id: 13, name: 'SMW (SMW)' }
            ],
            // 语言映射
            languages: [
                { id: 2, name: 'English (EN)' },
                { id: 3, name: 'Malay (MY)' },
                { id: 4, name: 'Chinese (CN)' },
                { id: 5, name: 'Paging (PG)' },
                { id: 6, name: 'Charter (CHARTER)' },
                { id: 8, name: '携程司导 (IM)' },
                { id: 9, name: 'PSV (PSV)' },
                { id: 10, name: 'EVP (EVP)' },
                { id: 11, name: 'Car Type Reverify (CAR)' },
                { id: 12, name: 'Jetty (JETTY)' },
                { id: 13, name: 'PhotoSkill Proof (PHOTO)' }
            ]
        };

        this.orderParsingPrompt = `
你是一个高度智能的、专为马来西亚和新加坡设计的用车服务订单处理引擎。
你的 **唯一任务** 是精确地、无逻辑遗漏地解析非结构化订单文本，并根据下方定义的 **五步严格规则**，直接计算并输出一个 **JSON数组**。
即使只有一个订单，也必须以数组形式 \\\`[ { ... } ]\\\` 返回。

---

### **输出格式契约 (JSON Schema)**

你必须严格按照以下JSON结构输出。**所有未在文本中找到对应信息的字段，其值必须为 \\\`null\\\`**，不能省略字段。

\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null",
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null",
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null",
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\`

---

### **第一步：多订单识别**

-   **核心指令**: 仔细阅读整个文本，判断它是否包含多个独立的订单。独立的订单通常由换行符、分隔线（如"---"）或明确的编号（如“订单1”，“订单2”）分隔。
-   **输出**: 如果识别出多个订单，则在最终的JSON数组中为每个订单创建一个独立的对象。

---

### **第二步：应用核心业务逻辑和计算规则 (对每个订单独立应用)**

**规则1: 服务类型 ('sub_category_id') 自动判断 (带冲突解决)**
你必须根据以下 **优先级顺序** 决定服务类型：
  1.  **包车关键词 (最高优先级)**: 如果包含 "包车", "charter", "全天", "day tour", "多小时", "景点游", "半日游" -> **直接判定为包车 (4)**，并停止后续判断。
  2.  **地点判断**:
      -   如果\\\`pickup\\\`是机场而\\\`dropoff\\\`不是机场 -> **接机 (2)**。
      -   如果\\\`dropoff\\\`是机场而\\\`pickup\\\`不是机场 -> **送机 (3)**。
  3.  **航班类型判断**:
      -   如果 \\\`flight_type\\\` 是 'Arrival' 或文本含 "到达"、"落地" -> **接机 (2)**。
      -   如果 \\\`flight_type\\\` 是 'Departure' 或文本含 "出发"、"起飞" -> **送机 (3)**。
  4.  **通用关键词**:
      -   "接机", "迎接", "airport pickup", "从机场" -> 接机 (2)
      -   "送机", "去机场", "airport dropoff", "到机场" -> 送机 (3)
  5.  **默认值**: 如果以上规则都无法判断，默认为接机 (2)。

**规则2: 接送时间 ('pickup_time') 自动计算 (关键计算)**
你必须计算出最终的 'pickup_time'。
  -   **对于送机 (sub_category_id: 3)**:
      -   **如果有航班号信息 ('flight_info' 不为空)**：'pickup_time' = 'departure_time' 或 'flight_time' **减去 3.5 小时**。
      -   **如果没有航班号信息 ('flight_info' 为空)**：'pickup_time' = 客户指定的时间，**不需要提前**。
      -   **必须处理跨天**: 例如航班 '02:00'起飞，接送时间是前一天的 '22:30'，此时 **\\\`pickup_date\\\` 也必须相应地调整为前一天**。
  -   **对于接机 (sub_category_id: 2)**:
      -   'pickup_time' = 'arrival_time' 或 'flight_time'。
  -   **对于包车 (sub_category_id: 4)**:
      -   'pickup_time' 等于客户指定的开始时间。
  -   **重要**: 在返回的JSON中，'pickup_time' 必须是**计算后的最终结果**。

**规则3: 车型 ('car_type_id') 智能推荐 (考虑行李)**
根据'passenger_count'和'luggage_count'，从下面的 **车型ID参考表** 中选择**最经济且最合适**的车型ID。
  -   1-3人, 行李≤3: 5 (5 Seater)
  -   4人, 或行李>3: 37 (Extended 5) 或 35 (7 Seater SUV)
  -   5-6人: 15 (7 Seater MPV)
  -   7-10人: 20 (10 Seater MPV/Van)
  -   如果提到 "豪华", "高级", "Velfire", "Alphard" -> 优先考虑 32 (Velfire/Alphard)。

**规则4: 举牌服务 ('needs_paging_service') 自动识别**
  -   检测关键词：'举牌', '举牌接机', '举牌服务', 'meet and greet', 'meet & greet', 'meet-and-greet', '接机牌', '迎接服务', '接机服务', 'paging', 'paging service', '举牌迎接', '机场迎接', '接机员', '迎宾服务'
  -   如果检测到举牌服务关键词 -> 设置 'needs_paging_service': true, 'meetAndGreet': true
  -   否则 -> 设置 'needs_paging_service': false, 'meetAndGreet': false

**规则5: 语言 ('languages_id_array') 智能判断 (简化规则)**
  -   如果'customer_name'是中文，或文本中包含中文字符 -> **直接返回 \\\`[4]\\\` (中文)**。
  -   否则，**默认返回 \\\`[2]\\\` (英文)**。

---

### **第三步：数据格式化**

  -   **日期 ('pickup_date')**: 必须是 'YYYY-MM-DD'。必须能处理 "今天", "明天", "后天", "周三", "下周一" 等相对日期，并根据上方提供的**当前日期信息**计算出准确日期。**重要**: 年份必须根据当前年份正确计算，如果是跨年情况需要正确处理。
  -   **时间 ('pickup_time', etc.)**: 必须是 'HH:MM' 24小时制。必须能处理 "早上8点", "下午3:30", "8am", "3:30pm" 等格式。
  -   **价格 ('price')**: 必须是纯数字。支持多种价格格式识别：
      * "RM150", "RM 150", "150 RM" -> 提取数字 \\\`150\\\`
      * "$150", "USD 150", "150 USD", "150美元" -> 提取数字 \\\`150\\\`
      * "￥200", "200元", "200人民币", "200 RMB", "200 CNY" -> 提取数字 \\\`200\\\`
      * "150马币", "150令吉" -> 提取数字 \\\`150\\\`
      * 同时在 'currency' 字段中标注原始货币类型：'MYR', 'USD', 'CNY'
  -   **布尔值**: 'baby_chair' 等字段必须是 \`true\` 或 \`false\`，而不是字符串。

---

### **第四步：参考ID与地点数据库**

#### **ID参考表**
-   **sub_category_id**: 2:接机, 3:送机, 4:包车
-   **car_type_id**: 5:'5 Seater', 37:'Extended 5', 35:'7 Seater SUV', 15:'7 Seater MPV', 32:'Velfire/Alphard', 20:'10 Seater Van'
-   **driving_region_id**: 1:'KL/Selangor', 2:'Penang', 3:'Johor', 4:'Sabah', 5:'Singapore', 12:'Malacca'
-   **languages_id_array**: 2:'English', 4:'Chinese'

#### **地点数据库 (地点标准化)**
你必须使用此表将文本中的地点名称（特别是酒店）标准化为官方名称。

*   **主要机场**:
    *   \\\`KLIA\\\`, \\\`KUL\\\`: "Kuala Lumpur International Airport (KLIA1)"
    *   \\\`KLIA2\\\`: "Kuala Lumpur International Airport 2 (KLIA2)"
    *   \\\`PEN\\\`: "Penang International Airport"
    *   \\\`JHB\\\`, \\\`Senai\\\`: "Senai International Airport, Johor Bahru"
    *   \\\`BKI\\\`: "Kota Kinabalu International Airport"
    *   \\\`SIN\\\`, \\\`Changi\\\`: "Singapore Changi Airport"
*   **吉隆坡(Kuala Lumpur)酒店**:
    *   \\\`文华东方\\\`, \\\`Mandarin Oriental\\\`: "Mandarin Oriental Kuala Lumpur"
    *   \\\`香格里拉\\\`, \\\`Shangri-La\\\`: "Shangri-La Hotel Kuala Lumpur"
    *   \\\`希尔顿\\\`, \\\`Hilton\\\`: "Hilton Kuala Lumpur"
    *   \\\`逸林\\\`, \\\`DoubleTree\\\`: "DoubleTree by Hilton Kuala Lumpur"
    *   \\\`万豪\\\`, \\\`Marriott\\\`: "JW Marriott Hotel Kuala Lumpur"
    *   \\\`丽思卡尔顿\\\`, \\\`Ritz-Carlton\\\`: "The Ritz-Carlton Kuala Lumpur"
    *   \\\`君悦\\\`, \\\`Grand Hyatt\\\`: "Grand Hyatt Kuala Lumpur"
    *   \\\`四季\\\`, \\\`Four Seasons\\\`: "Four Seasons Hotel Kuala Lumpur"
    *   \\\`双威\\\`, \\\`Sunway\\\`: "Sunway Resort Hotel"
    *   \\\`百乐海\\\`, \\\`PARKROYAL\\\`: "PARKROYAL COLLECTION Kuala Lumpur"
    *   \\\`大华\\\`, \\\`Majestic\\\`: "The Majestic Hotel Kuala Lumpur"
    *   \\\`菲斯\\\`, \\\`FACE\\\`: "THE FACE Style Hotel"
*   **槟城(Penang)酒店**:
    *   \\\`东方大酒店\\\`, \\\`E&O\\\`: "Eastern & Oriental Hotel"
    *   \\\`硬石\\\`, \\\`Hard Rock\\\`: "Hard Rock Hotel Penang"
    *   \\\`香格里拉金沙\\\`, \\\`Golden Sands\\\`: "Shangri-La Golden Sands, Penang"
    *   \\\`百乐海\\\`, \\\`PARKROYAL\\\`: "PARKROYAL Penang Resort"
    *   \\\`蓝屋\\\`, \\\`张弼士\\\`: "Cheong Fatt Tze Mansion"
*   **新山(Johor Bahru)酒店**:
    *   \\\`逸林\\\`, \\\`DoubleTree\\\`: "DoubleTree by Hilton Hotel Johor Bahru"
    *   \\\`万丽\\\`, \\\`Renaissance\\\`: "Renaissance Johor Bahru Hotel"
    *   \\\`乐高\\\`, \\\`Legoland\\\`: "Legoland Hotel Malaysia"
*   **亚庇(Kota Kinabalu)酒店**:
    *   \\\`丹绒亚路香格里拉\\\`, \\\`Tanjung Aru\\\`: "Shangri-La Tanjung Aru, Kota Kinabalu"
    *   \\\`沙利雅香格里拉\\\`, \\\`Rasa Ria\\\`: "Shangri-La Rasa Ria, Kota Kinabalu"
    *   \\\`麦哲伦\\\`, \\\`Magellan\\\`: "The Magellan Sutera Resort"
    *   \\\`太平洋\\\`, \\\`Pacific\\\`: "The Pacific Sutera Hotel"
    *   \\\`凯悦\\\`, \\\`Hyatt\\\`: "Hyatt Regency Kinabalu"
*   **新加坡(Singapore)酒店**:
    *   \\\`滨海湾金沙\\\`, \\\`MBS\\\`: "Marina Bay Sands"
    *   \\\`莱佛士\\\`, \\\`Raffles\\\`: "Raffles Singapore"
    *   \\\`文华东方\\\`, \\\`Mandarin Oriental\\\`: "Mandarin Oriental Singapore"
    *   \\\`老板酒店\\\`, \\\`Hotel Boss\\\`: "Hotel Boss"

---

### **第五步：查看示例**

**示例1 (送机 - 计算时间并调整日期):**
"王先生, 明早航班MH123 02:00从KLIA起飞, 请从KL Hilton送机"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "王先生",
    "customer_contact": null,
    "sub_category_id": 3,
    "flight_info": "MH123",
    "departure_time": "02:00",
    "arrival_time": null,
    "flight_type": "Departure",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "22:30",
    "pickup": "Hilton Kuala Lumpur",
    "dropoff": "Kuala Lumpur International Airport (KLIA1)",
    "passenger_count": 1,
    "luggage_count": null,
    "car_type_id": 5,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "needs_paging_service": false,
    "price": null,
    "currency": null,
    "extra_requirement": null
  }
]
\\\`\\\`\\\`

**示例2 (多订单解析):**
"订单1: 李女士, 13800138000, 明天下午3点半CZ351降落KLIA2, 4大1小, 需要儿童座椅, 到Sunway酒店. 订单2: 同一个客户, 后天早上9点从Sunway酒店包车8小时, 中文司机"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "李女士",
    "customer_contact": "13800138000",
    "sub_category_id": 2,
    "flight_info": "CZ351",
    "arrival_time": "15:30",
    "departure_time": null,
    "flight_type": "Arrival",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "15:30",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "dropoff": "Sunway Resort Hotel",
    "passenger_count": 5,
    "luggage_count": null,
    "car_type_id": 15,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "baby_chair": true,
    "needs_paging_service": false,
    "price": null,
    "currency": null,
    "extra_requirement": null
  },
  {
    "customer_name": "李女士",
    "customer_contact": "13800138000",
    "sub_category_id": 4,
    "flight_info": null,
    "arrival_time": null,
    "departure_time": null,
    "flight_type": null,
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "09:00",
    "pickup": "Sunway Resort Hotel",
    "dropoff": null,
    "passenger_count": 5,
    "luggage_count": null,
    "car_type_id": 15,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "baby_chair": false,
    "needs_paging_service": false,
    "price": null,
    "currency": null,
    "extra_requirement": "包车8小时, 中文司机"
  }
]
\\\`\\\`\\\`

---

**最终指令：**
现在，请严格按照以上所有规则，分析给定的订单描述，并 **只输出一个符合规范的、完整的JSON数组**。
        `;

        // 初始化时构建通用化提示词（保留原有提示词作为备用）
        this.universalPrompt = this.buildUniversalPrompt('standard');
    }
    
    /**
     * 对Gemini返回的原始数据进行后期处理和规范化
     * @param {string} rawText - 从Gemini API返回的原始文本
     * @returns {object|null} - 解析和清理后的JSON对象，或在失败时返回null
     */
    postProcessParsedData(rawText) {
        if (!rawText) {
            getLogger().log('Gemini后处理失败：输入文本为空', 'error');
            return null;
        }

        // 尝试从Markdown代码块中提取JSON
        const jsonMatch = rawText.match(/```(json)?\s*([\s\S]*?)\s*```/);
        const jsonString = jsonMatch ? jsonMatch[2].trim() : rawText.trim();

        try {
            let data = JSON.parse(jsonString);
            
            // 新的返回格式是数组，确保后续处理能兼容
            // 如果不是数组，为了兼容旧格式，将其包装成数组
            if (!Array.isArray(data)) {
                data = [data];
            }
            
            // 对数组中的每个订单对象进行格式规范化
            const processedData = data.map(order => this.normalizeDataFormats(order));
            
            getLogger().log('Gemini后处理成功', 'success', { count: processedData.length, data: processedData });
            return processedData;

        } catch (error) {
            getLogger().log('JSON解析失败', 'error', { 
                originalText: rawText,
                jsonString: jsonString,
                error: error.message 
            });
            // 尝试进行更宽松的修复性解析
            return this.basicParse(jsonString);
        }
    }

    /**
     * 规范化单个订单对象中的数据格式
     * @param {object} data - 单个订单对象
     * @returns {object} - 格式规范化后的订单对象
     */
    normalizeDataFormats(data) {
        const normalizedData = { ...data };

        // 规范化电话号码
        if (normalizedData.customer_contact) {
            normalizedData.customer_contact = this.normalizePhoneNumber(normalizedData.customer_contact);
        }
        
        // 规范化日期
        if (normalizedData.pickup_date) {
            normalizedData.pickup_date = this.normalizeDate(normalizedData.pickup_date);
        }

        // 规范化时间
        if (normalizedData.pickup_time) {
            normalizedData.pickup_time = this.normalizeTime(normalizedData.pickup_time);
        }
        if (normalizedData.flight_time) {
            normalizedData.flight_time = this.normalizeTime(normalizedData.flight_time);
        }
        if (normalizedData.departure_time) {
            normalizedData.departure_time = this.normalizeTime(normalizedData.departure_time);
        }
        if (normalizedData.arrival_time) {
            normalizedData.arrival_time = this.normalizeTime(normalizedData.arrival_time);
        }

        // 规范化地点
        if (normalizedData.pickup) {
            normalizedData.pickup = this.normalizeLocation(normalizedData.pickup);
        }
        if (normalizedData.dropoff) {
            normalizedData.dropoff = this.normalizeLocation(normalizedData.dropoff);
        }

        // 确保数字段为数字类型
        const numericFields = ['passenger_count', 'luggage_count', 'price'];
        numericFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = parseInt(normalizedData[field], 10) || null;
            }
        });

        // 确保ID字段为数字
        const idFields = ['sub_category_id', 'car_type_id', 'driving_region_id'];
        idFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = parseInt(normalizedData[field], 10) || null;
            }
        });

        // 确保布尔字段为布尔类型
        const booleanFields = ['baby_chair', 'tour_guide', 'meet_and_greet'];
        booleanFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = String(normalizedData[field]).toLowerCase() === 'true';
            }
        });
        
        // 确保语言ID数组为数组
        if (normalizedData.languages_id_array && !Array.isArray(normalizedData.languages_id_array)) {
            // 尝试从字符串（如"[4]"）转换
            try {
                const parsedArray = JSON.parse(normalizedData.languages_id_array);
                if(Array.isArray(parsedArray)) {
                    normalizedData.languages_id_array = parsedArray;
                }
            } catch (e) {
                // 如果解析失败，则将其包装在数组中
                normalizedData.languages_id_array = [parseInt(normalizedData.languages_id_array, 10) || 2];
            }
        }

        // 字段名称映射 - 将Gemini生成的字段名映射为API要求的字段名
        // 注意：这里不进行映射，因为API服务的preprocessOrderData会处理映射
        // 保持Gemini输出的原始字段名，让API服务统一处理映射

        // 记录Gemini数据规范化完成
        getLogger().log('Gemini数据规范化完成', 'info', {
            hasPickupDate: !!normalizedData.pickup_date,
            hasPickupTime: !!normalizedData.pickup_time,
            hasPickup: !!normalizedData.pickup,
            hasDropoff: !!normalizedData.dropoff,
            hasLuggageCount: !!normalizedData.luggage_count
        });

        return normalizedData;
    }
    
    /**
     * 规范化电话号码（移除+、-、空格）
     * @param {string} phone - 原始电话号码
     * @returns {string} - 清理后的电话号码
     */
    normalizePhoneNumber(phone) {
        if (!phone || typeof phone !== 'string') return '';
        return phone.replace(/[+\-\s]/g, '');
    }

    /**
     * 规范化日期格式为 YYYY-MM-DD
     * @param {string} date - 原始日期字符串
     * @returns {string} - YYYY-MM-DD格式的日期
     */
    normalizeDate(date) {
        if (!date || typeof date !== 'string') return '';
        try {
            // 尝试直接解析
            const d = new Date(date);
            if (!isNaN(d.getTime())) {
                return d.toISOString().split('T')[0];
            }
        } catch (e) {
            // 失败则返回原始值
            return date;
        }
        return date;
    }
    
    /**
     * 规范化时间格式为 HH:MM
     * @param {string} time - 原始时间字符串
     * @returns {string} - HH:MM格式的时间
     */
    normalizeTime(time) {
        if (!time || typeof time !== 'string') return '';
        const match = time.match(/(\d{1,2}):(\d{2})/);
        if (match) {
            const hour = match[1].padStart(2, '0');
            const minute = match[2];
            return `${hour}:${minute}`;
        }
        return time; // 格式不符则返回原值
    }

    /**
     * 规范化地点名称（移除潜在的星号、多余空格）
     * @param {string} location - 原始地点字符串
     * @returns {string} - 清理后的地点字符串
     */
    normalizeLocation(location) {
        if (!location || typeof location !== 'string') return '';
        return location.replace(/\*/g, '').trim();
    }
    
    /**
     * 从解析数据中计算置信度分数
     * @param {object} data - 解析后的数据
     * @returns {number} - 0到1之间的置信度分数
     */
    calculateConfidence(data) {
        if (!data || typeof data !== 'object') return 0;

        const totalFields = Object.keys(data).length;
        if (totalFields === 0) return 0;

        let filledFields = 0;
        for (const key in data) {
            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                filledFields++;
            }
        }
        return filledFields / totalFields;
    }

    /**
     * 基础的、更宽松的JSON解析尝试
     * @param {string} text - 可能不完全是JSON的字符串
     * @returns {object|null} - 解析后的对象或null
     */
    basicParse(text) {
        try {
            // 这是一个非常基础的实现，未来可以扩展为更复杂的修复逻辑
            // 例如，尝试添加缺失的括号、引号等
            const sanitized = text
                .replace(/,\s*}/g, '}')
                .replace(/,\s*]/g, ']');
            
            let data = JSON.parse(sanitized);
             if (!Array.isArray(data)) {
                data = [data];
            }
            return data;
        } catch (error) {
            getLogger().log('基础JSON解析也失败', 'error', { text: text, error: error.message });
            return null;
        }
    }

    /**
     * 模拟网络延迟
     * @param {number} ms - 延迟毫秒数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取当前分析状态
     * @returns {object}
     */
    getStatus() {
        return {
            isAnalyzing: this.analysisState.isAnalyzing,
            lastAnalyzedText: this.analysisState.lastAnalyzedText
        };
    }
    
    /**
     * 生成一个随机的示例订单，用于测试
     * @returns {string} - 示例订单描述
     */
    generateSampleOrder() {
        const samples = [
            `客户：张三先生 +60123456789\n接送：KLIA2机场 到 Pavilion KL购物中心\n时间：明天 14:30\n人数：2大1小\n要求：需要儿童座椅`,
            `预订人：李小姐 (<EMAIL>)\n航班：MH370 15:45抵达\n从：吉隆坡国际机场\n到：Bukit Bintang武吉免登\n日期：后天下午4点\n乘客：3人 + 2件大行李\n特殊要求：司机能说中文`,
            `Customer: John Smith\nFrom: KL Sentral Station\nTo: Genting Highlands\nDate:下周三 09:00\nPassengers: 4 adults\nLuggage: 3 large suitcases\nService: Charter tour (8 hours)`,
            `订单1: 刘女士, 13912345678, 明天下午3点半航班CZ351降落KLIA2, 4大1小, 需要儿童座椅, 到Sunway酒店.\n---\n订单2: 同一个客户, 后天早上9点从Sunway酒店包车8小时, 中文司机, 需要去双子塔和独立广场`
        ];
        
        return samples[Math.floor(Math.random() * samples.length)];
    }

    /**
     * 更新Gemini内部的ID映射表（例如，从API获取最新数据后）
     * @param {object} systemData - 包含最新ID映射的对象
     */
    updateIdMappings(systemData) {
        try {
            if (systemData.backend_users) {
                this.idMappings.backendUsers = systemData.backend_users.reduce((acc, user) => {
                    acc[user.email] = user.id;
                    return acc;
                }, {});
            }
            if (systemData.sub_categories) {
                this.idMappings.subCategories = systemData.sub_categories;
            }
            if (systemData.car_types) {
                this.idMappings.carTypes = systemData.car_types.map(car => ({
                    id: car.id,
                    name: car.name,
                    passengerLimit: car.max_passenger_capacity || 0
                }));
            }
            if (systemData.driving_regions) {
                this.idMappings.drivingRegions = systemData.driving_regions;
            }
            if (systemData.languages) {
                this.idMappings.languages = systemData.languages;
            }
            getLogger().log('Gemini ID 映射已更新', 'info', this.idMappings);
        } catch (err) {
            getLogger().logError('同步 Gemini ID 映射失败', err);
        }
    }
    
    /**
     * 动态配置实时分析参数
     * @function
     * @param {Object} config - 实时分析配置项
     * @returns {void}
     */
    configureRealtimeAnalysis(config) {
        // 参数校验，确保传入为对象
        if (typeof config !== 'object' || config === null) return;
        // 合并配置到realtimeConfig
        this.realtimeConfig = { ...this.realtimeConfig, ...config };
    }

    /**
     * 动态配置实时分析参数（别名方法，供旧版本调用）
     * @function
     * @param {Object} config - 实时分析配置项
     * @returns {void}
     */
    setRealtimeAnalysis(config) {
        // 调用新的配置方法
        this.configureRealtimeAnalysis(config);
    }

    /**
     * 判断Gemini服务是否可用
     * @returns {boolean}
     */
    isAvailable() {
        // 只要apiKey存在且非空字符串即认为可用
        return typeof this.apiKey === 'string' && this.apiKey.trim().length > 0;
    }

    /**
     * 核心函数：解析订单文本
     * @param {string} text - 用户输入的订单描述
     * @param {boolean} isRealtime - 是否为实时分析模式
     * @returns {Promise<object|null>} - 解析后的数据或null
     */
    async parseOrder(text, isRealtime = false) {
        if (!text || text.length < (isRealtime ? this.realtimeConfig.minInputLength : 10)) {
            return null;
        }

        this.analysisState.isAnalyzing = true;
        this.analysisState.lastAnalyzedText = text;

        // 获取当前日期信息以帮助Gemini正确解析相对日期
        const currentDate = new Date();
        const dateContext = `
**当前日期信息（用于相对日期计算）:**
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;

        const requestBody = {
            contents: [{
                parts: [{ text: this.orderParsingPrompt + dateContext + "\n\n**订单描述:**\n" + text }]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 1,
                topP: 1,
                maxOutputTokens: 2048,
                stopSequences: []
            },
            safetySettings: [
                // 调整安全设置以适应业务场景
                { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
            ]
        };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            this.analysisState.currentRequest = controller;

            const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorBody = await response.json();
                throw new Error(`API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
            }

            const data = await response.json();
            const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;
            
            if (!rawText) {
                throw new Error('API返回内容为空或格式不正确');
            }

            const processedData = this.postProcessParsedData(rawText);
            this.analysisState.isAnalyzing = false;
            return processedData;

        } catch (error) {
            this.analysisState.isAnalyzing = false;
            if (error.name === 'AbortError') {
                getLogger().log('Gemini请求超时', 'error');
            } else {
                getLogger().logError('Gemini解析时发生严重错误', error);
            }
            return null;
        }
    }

    /**
     * 专门用于多订单解析的增强方法
     * @param {Array} orderSegments - 订单片段数组
     * @returns {Promise<Array>} - 解析后的多订单数据数组
     */
    async parseMultipleOrders(orderSegments) {
        if (!Array.isArray(orderSegments) || orderSegments.length === 0) {
            return [];
        }

        const logger = getLogger();
        logger?.log(`开始解析 ${orderSegments.length} 个订单片段`, 'info');

        const results = [];
        const errors = [];

        // 获取当前日期信息
        const currentDate = new Date();
        const dateContext = `
**当前日期信息（用于相对日期计算）:**
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;

        // 构建专门的多订单解析提示词
        const multiOrderPrompt = `
你是一个专业的多订单解析助手。我将提供多个独立的订单片段，请为每个片段解析出完整的订单信息。

**重要指令：**
1. 我提供的每个片段都是一个独立的订单
2. 为每个片段返回一个完整的JSON对象
3. 必须返回JSON数组格式，即使只有一个订单
4. 严格按照以下JSON schema格式返回

**JSON Schema:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null", 
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null", 
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null", 
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\`

${this.orderParsingPrompt.split('**第一步：多订单识别**')[1] || this.orderParsingPrompt}

${dateContext}

**待解析的订单片段：**
`;

        // 逐个解析订单片段
        for (let i = 0; i < orderSegments.length; i++) {
            const segment = orderSegments[i];
            
            try {
                logger?.log(`解析订单片段 ${i + 1}/${orderSegments.length}`, 'info');
                
                const fullPrompt = multiOrderPrompt + `\n\n**订单片段 ${i + 1}:**\n${segment}`;
                
                const requestBody = {
                    contents: [{
                        parts: [{ text: fullPrompt }]
                    }],
                    generationConfig: {
                        temperature: 0.1,
                        topK: 1,
                        topP: 1,
                        maxOutputTokens: 2048,
                        stopSequences: []
                    },
                    safetySettings: [
                        { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
                    ]
                };

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorBody = await response.json();
                    throw new Error(`API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
                }

                const data = await response.json();
                const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!rawText) {
                    throw new Error('API返回内容为空');
                }

                const processedData = this.postProcessParsedData(rawText);
                
                if (processedData && processedData.success) {
                    // 确保返回的是数组，取第一个元素作为此片段的解析结果
                    const orderData = Array.isArray(processedData.data) ? processedData.data[0] : processedData.data;
                    
                    if (orderData) {
                        // 添加元数据
                        orderData._segment_index = i;
                        orderData._original_text = segment;
                        orderData._confidence = processedData.confidence || 0;
                        
                        results.push(orderData);
                        logger?.log(`订单片段 ${i + 1} 解析成功`, 'success');
                    } else {
                        throw new Error('解析结果为空');
                    }
                } else {
                    throw new Error(processedData?.message || '解析失败');
                }

                // 添加延迟避免API限制
                if (i < orderSegments.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

            } catch (error) {
                logger?.logError(`订单片段 ${i + 1} 解析失败`, error);
                errors.push({
                    index: i,
                    segment: segment.substring(0, 100) + '...',
                    error: error.message
                });
                
                // 添加失败的占位符
                results.push({
                    _segment_index: i,
                    _original_text: segment,
                    _parse_error: error.message,
                    customer_name: null,
                    customer_contact: null,
                    pickup: null,
                    dropoff: null,
                    pickup_date: null,
                    pickup_time: null,
                    sub_category_id: 2, // 默认接机
                    passenger_count: 1
                });
            }
        }

        logger?.log(`多订单解析完成，成功: ${results.filter(r => !r._parse_error).length}, 失败: ${errors.length}`, 'info');

        return {
            success: true,
            data: results,
            errors: errors,
            totalSegments: orderSegments.length,
            successCount: results.filter(r => !r._parse_error).length,
            failureCount: errors.length
        };
    }

    /**
     * 🔧 增强版：带多重验证的多订单检测
     * @param {string} orderText - 原始订单文本
     * @param {Object} options - 验证选项
     * @returns {Promise<Object>} 包含检测结果和完整解析订单的对象
     */
    async detectAndSplitMultiOrdersWithVerification(orderText, options = {}) {
        const config = { ...this.verificationConfig, ...options };
        const logger = getLogger();
        
        if (!config.enabled) {
            // 如果验证被禁用，直接使用原始方法
            return await this.detectAndSplitMultiOrders(orderText);
        }
        
        logger?.log('🔄 开始多重验证多订单检测...', 'info', {
            attempts: config.attempts,
            consistencyThreshold: config.consistencyThreshold
        });
        
        const startTime = Date.now();
        this.validationStats.totalValidations++;
        
        // 检查缓存
        const cacheKey = this.generateCacheKey(orderText);
        if (this.promptCache.has(cacheKey)) {
            this.validationStats.cacheHits++;
            logger?.log('📋 使用缓存结果', 'info');
            return this.promptCache.get(cacheKey);
        }
        this.validationStats.cacheMisses++;
        
        // 执行多次分析
        const results = [];
        const errors = [];
        
        for (let attempt = 1; attempt <= config.attempts; attempt++) {
            try {
                logger?.log(`🔄 执行第 ${attempt} 次分析...`, 'info');
                
                // 添加轻微的随机性来避免完全相同的结果
                const modifiedPrompt = this.addPromptVariation(orderText, attempt);
                const result = await this.detectAndSplitMultiOrders(modifiedPrompt);
                
                if (result && result.orders) {
                    results.push({
                        attempt,
                        result,
                        timestamp: Date.now()
                    });
                    logger?.log(`✅ 第 ${attempt} 次分析完成`, 'success', {
                        orderCount: result.orderCount,
                        isMultiOrder: result.isMultiOrder
                    });
                } else {
                    throw new Error('分析结果无效');
                }
                
            } catch (error) {
                errors.push({ attempt, error: error.message });
                logger?.log(`❌ 第 ${attempt} 次分析失败: ${error.message}`, 'error');
            }
        }
        
        // 分析结果一致性
        const consistentResult = this.analyzeConsistency(results, config.consistencyThreshold);
        
        if (consistentResult) {
            this.validationStats.successfulValidations++;
            this.validationStats.consistentResults++;
            
            // 缓存结果
            this.promptCache.set(cacheKey, consistentResult);
            
            const endTime = Date.now();
            this.validationStats.averageAttempts = 
                (this.validationStats.averageAttempts * (this.validationStats.totalValidations - 1) + results.length) / 
                this.validationStats.totalValidations;
            
            logger?.log('🎯 多重验证成功', 'success', {
                attempts: results.length,
                timeElapsed: endTime - startTime,
                consistency: true
            });
            
            return consistentResult;
        } else {
            // 如果没有一致结果，返回最佳结果
            const bestResult = this.selectBestResult(results);
            
            if (bestResult) {
                this.validationStats.successfulValidations++;
                this.promptCache.set(cacheKey, bestResult);
                
                logger?.log('⚠️ 多重验证部分成功，使用最佳结果', 'warning', {
                    attempts: results.length,
                    consistency: false
                });
                
                return bestResult;
            } else {
                logger?.logError('❌ 多重验证完全失败', { results, errors });
                throw new Error('多重验证失败：无法获得可靠结果');
            }
        }
    }

    /**
     * 一体化智能多订单检测、分割和完整解析（方案一实现）
     * @param {string} orderText - 原始订单文本
     * @returns {Promise<Object>} 包含检测结果和完整解析订单的对象
     */
    async detectAndSplitMultiOrders(orderText) {
        if (!orderText || typeof orderText !== 'string' || orderText.trim().length < 50) {
            return {
                isMultiOrder: false,
                orders: [],
                orderCount: 0,
                confidence: 0,
                analysis: '输入文本过短或无效'
            };
        }

        const logger = getLogger();
        logger?.log('🤖 Gemini开始一体化多订单检测和完整解析...', 'info');

        try {
            // 获取当前日期信息
            const currentDate = new Date();
            const dateContext = `
**当前日期信息（用于相对日期计算）:**
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;

            // 🔧 强化JSON格式约束的一体化prompt：检测+分割+完整解析
            const enhancedMultiOrderPrompt = `
**【重要指令：必须返回有效JSON格式】**
**【关键要求：对于多订单情况，必须确保orderCount>=2且isMultiOrder=true】**

你是专业的多订单智能处理助手。请完成以下任务：
1. **仔细分析输入文本，识别是否包含多个独立的订单记录**
2. **如果文本中包含多个客户、多个团号、多个航班时间等，必须识别为多订单**
3. **特别注意：如果文本包含换行分隔的多个订单，每行一个订单，必须设置正确的orderCount**
4. 如果是多订单，分割并完整解析每个订单  
5. 返回结构化的订单数据

**【关键修复】多订单判断的最终检查：**
- 在生成最终JSON之前，重新计算orders数组的长度
- 如果orders.length > 1，强制设置：orderCount = orders.length, isMultiOrder = true
- 如果检测到举牌服务关键词，即使只有一个订单也要设置：isMultiOrder = true
- 如果检测到多个团号，必须设置：orderCount >= 团号数量, isMultiOrder = true

**返回格式（严格要求）：**
\`\`\`json
{
  "isMultiOrder": boolean,
  "orderCount": number,
  "confidence": number,
  "orders": [订单对象数组],
  "analysis": "分析说明"
}
\`\`\`

**重要：orderCount必须等于orders数组的实际长度！**

**多订单判断标准（按业务逻辑）：**
1. **按日期分离**：不同日期的服务必须分为不同订单
2. **按订单号/团号分离**：每个唯一的订单号/团号都是独立订单
3. **往返订单分离**：同一团号的往返（接机+送机）如果日期不同，必须分为两个订单
4. **举牌/Meet and Greet检测**：包含举牌、举牌接机、举牌服务、meet and greet、meet & greet、meet-and-greet、接机牌、迎接服务、接机服务、paging、paging service、举牌迎接、机场迎接、接机员、迎宾服务等服务的订单必须触发多订单模块（即使是单一订单也要设置isMultiOrder=true）
5. **服务类型分离**：接机、送机、包车、酒店接送等不同服务类型需要分开处理
6. **强制多订单标识**：如果文本包含多个团号（如EJBTBY250716-3, EJBTBY250716-4）或多个不同日期（如16/7, 21/7），**必须设置isMultiOrder=true且orderCount>=2**
7. **换行分隔识别**：每个换行的独立订单信息都应该被识别为单独的订单

**具体检测规则：**
- 包含多个不同的日期（如16/7、17/7、21/7等）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含多个不同的订单号/团号（如EJBTBY250716-3, EJBTBY250716-4等）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含接机和送机的组合（即使同一团号，不同日期需分离）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含举牌、举牌接机、举牌服务、meet and greet、meet & greet、meet-and-greet、接机牌、迎接服务、接机服务、paging、paging service、举牌迎接、机场迎接、接机员、迎宾服务等关键词 → **强制设置isMultiOrder=true**
- 包含多个不同的客户姓名或联系方式 → **强制设置orderCount>=2, isMultiOrder=true**
- 包含多个不同的航班信息和时间 → **强制设置orderCount>=2, isMultiOrder=true**
- 包含7 SEATER等特殊车型要求
- 包含聊天记录格式（如[2025/7/10 17:27]等时间戳格式）
- 每个消息块中的独立订单信息应分别解析
- **换行符分隔的订单**：如果文本包含多行独立的订单信息（每行都包含团号、日期、客人信息），**每行都是一个独立订单，必须设置orderCount=行数, isMultiOrder=true**

**多订单识别示例：**

**示例1 - 不同团号的订单（应识别为多订单）：**
\`\`\`
接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) 客人：农玉琴 客人联系：18589880205
接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) 客人：甘小姐 客人联系：13119629639
\`\`\`
结果：orderCount=2，isMultiOrder=true（不同团号）

**示例2 - 同一团号的往返订单（应识别为多订单）：**
\`\`\`
接机：团号：EJBTBY250717 2PAX 17/7 KLIA2 PICKUP 0600 客人：简锦霞 客人联系：13424915035
送机：团号：EJBTBY250717 2PAX 21/7 MOXY PUTRAJAYA PICKUP 0600 - KLIA2 (AK188 1000) 客人：简锦霞 客人联系：13424915035
\`\`\`
结果：orderCount=2，isMultiOrder=true（同团号不同日期的往返）

**示例3 - 包含举牌/Meet and Greet的订单（应识别为多订单）：**
\`\`\`
接机：团号：EJBTBY250716-1 2PAX 16/7 KLIA2 IN 1010 (AK115) Meet and Greet 客人：张三 客人联系：123456789
\`\`\`
结果：orderCount=1，isMultiOrder=true（包含Meet and Greet服务）

**示例4 - 聊天记录格式（应识别为多订单）：**
\`\`\`
[2025/7/10 17:27] Joshua: 接机：团号：EJBTBY250712-1 2PAX 13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA 客人：朱芸 客人联系：18130403306
[2025/7/11 19:22] Joshua: 送机：团号：EJBTBY250710-1 2PAX 14/7 THE FACE STYLE HOTEL KL 0500AM - KLIA2 (AK5136 0915) 客人：刘凯 客人联系：18764221412
[2025/7/11 19:22] Joshua: 接机：团号：EJBTBY250715 2PAX 15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA 客人：顾婉婷 & 苟晓琼 客人联系：13884407028
\`\`\`
结果：orderCount=3，isMultiOrder=true（不同团号、不同日期、不同客户）

**ID映射参考：**
- 子分类ID: 接机=2, 送机=3, 包车=4
- 行驶区域ID: 吉隆坡=1, 雪兰莪=2, 其他根据地点推测
- 车型ID: 根据乘客人数选择 (1-3人=5, 4人=37, 5人=15, 6人=32, 7人=20, 10人=23, 12人=24)
- 语言ID: 中文=1, 英文=2, 马来文=3, 多语言=[1,2]

**货币处理：**
- 识别并保留原始货币单位
- 支持: MYR, USD, SGD, CNY, RMB(转为CNY)

**【格式要求：必须严格遵守】**
1. 必须以\`\`\`json开头
2. 必须以\`\`\`结尾
3. 中间必须是完整有效的JSON对象
4. 不允许有任何额外的文字说明

\`\`\`json
{
  "isMultiOrder": true,
  "orderCount": 11,
  "confidence": 0.95,
  "analysis": "检测到11个独立的订单记录，包含不同的团号、日期、客户信息和服务类型",
  "orders": [
    {
      "rawText": "接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) - MOXY PUTRAJAYA 客人：农玉琴 客人联系：18589880205",
      "customerName": "农玉琴",
      "customerContact": "18589880205",
      "customerEmail": null,
      "pickup": "KLIA2",
      "dropoff": "MOXY PUTRAJAYA",
      "pickupDate": "2025-07-16",
      "pickupTime": "21:10",
      "passengerCount": 1,
      "luggageCount": 1,
      "flightInfo": "AK169",
      "otaReferenceNumber": "EJBTBY250716-3",
      "otaPrice": null,
      "currency": "MYR",
      "carTypeId": 5,
      "subCategoryId": 2,
      "drivingRegionId": 1,
      "languagesIdArray": [1, 2],
      "extraRequirement": null,
      "babyChair": false,
      "tourGuide": false,
      "meetAndGreet": false
    },
    {
      "rawText": "接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) - MOXY PUTRAJAYA 客人：甘小姐 客人联系：13119629639",
      "customerName": "甘小姐",
      "customerContact": "13119629639",
      "customerEmail": null,
      "pickup": "KLIA2",
      "dropoff": "MOXY PUTRAJAYA",
      "pickupDate": "2025-07-16",
      "pickupTime": "10:10",
      "passengerCount": 2,
      "luggageCount": 2,
      "flightInfo": "AK115",
      "otaReferenceNumber": "EJBTBY250716-4",
      "otaPrice": null,
      "currency": "MYR",
      "carTypeId": 5,
      "subCategoryId": 2,
      "drivingRegionId": 1,
      "languagesIdArray": [1, 2],
      "extraRequirement": null,
      "babyChair": false,
      "tourGuide": false,
      "meetAndGreet": false
    }
  ]
}
\\\`\\\`\\\`

**【重要约束】**
- **按业务逻辑分离**：每个唯一的团号/订单号 + 日期组合都是独立订单
- **往返订单分离**：同一团号的不同日期服务（接机+送机）必须分为两个订单
- **Meet and Greet触发**：包含举牌、meet and greet等服务的订单必须设置isMultiOrder=true
- **车型特殊要求**：7 SEATER等特殊车型要求应正确映射到carTypeId
- 如果是单订单，orders数组包含一个完整解析的订单对象
- 如果是多订单，orders数组包含多个完整解析的订单对象
- 所有字段必须尽可能准确解析，无法确定的使用null或合理默认值
- rawText字段保存对应的原始文本片段
- 日期格式必须为YYYY-MM-DD，时间格式为HH:MM (24小时制)
- 响应中除了JSON代码块外，不得包含任何其他内容

${dateContext}

**待分析和解析的文本：**
${orderText}

请分析并返回完整的JSON结果：`;

            const response = await this.generateContent(enhancedMultiOrderPrompt);
            
            if (!response || !response.text) {
                throw new Error('Gemini响应为空');
            }

            logger?.log('🤖 Gemini一体化解析完成，处理响应...', 'info');

            // 🔧 增强调试：记录原始响应
            logger?.log('📝 Gemini原始响应内容:', 'debug', { 
                responseLength: response.text.length,
                responsePreview: response.text.substring(0, 500) + '...'
            });

            // 🔧 使用新的智能容错解析系统
            logger?.log('🔍 开始智能容错解析...', 'debug');

            const parseResult = this.intelligentErrorRecovery(response.text, 1);

            if (parseResult && parseResult.orders && parseResult.orders.length > 0) {
                logger?.log('✅ 智能容错解析成功', 'success', {
                    orderCount: parseResult.orderCount,
                    confidence: parseResult.confidence
                });

                return parseResult;
            }

            // 如果智能解析也失败，尝试传统解析作为最后手段
            logger?.log('⚠️ 智能解析失败，尝试传统解析...', 'warning');

            let jsonString = null;
            let fallbackResult = null;

            // 策略1: 标准 ```json 格式
            let jsonMatch = response.text.match(/```json\s*([\s\S]*?)\s*```/i);
            if (jsonMatch) {
                jsonString = jsonMatch[1].trim();
                logger?.log('✅ 发现标准JSON代码块格式', 'debug');
            }
            
            // 策略2: 不带语言标识的代码块
            if (!jsonString) {
                jsonMatch = response.text.match(/```\s*(\{[\s\S]*?\})\s*```/);
                if (jsonMatch) {
                    jsonString = jsonMatch[1].trim();
                    logger?.log('✅ 发现无语言标识的代码块格式', 'debug');
                }
            }
            
            // 策略3: 纯JSON对象匹配 (最后一个完整的JSON对象)
            if (!jsonString) {
                const jsonMatches = response.text.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
                if (jsonMatches && jsonMatches.length > 0) {
                    // 优先选择最大的JSON对象
                    jsonString = jsonMatches.reduce((longest, current) => 
                        current.length > longest.length ? current : longest
                    );
                    logger?.log('✅ 发现纯JSON对象格式', 'debug');
                }
            }
            
            // 策略4: 尝试修复常见JSON格式问题
            if (!jsonString) {
                // 查找看起来像JSON的部分
                const possibleJson = response.text.match(/[\s\S]*?(\{[\s\S]*orderCount[\s\S]*\})/);
                if (possibleJson) {
                    jsonString = possibleJson[1].trim();
                    logger?.log('⚠️ 发现可能的JSON片段，尝试修复', 'warning');
                }
            }
            
            if (!jsonString) {
                logger?.logError('❌ 所有JSON解析策略都失败', { 
                    responseText: response.text,
                    responseLength: response.text.length,
                    containsJson: response.text.includes('{'),
                    containsCodeBlock: response.text.includes('```')
                });
                throw new Error(`未找到有效的JSON响应格式。响应内容: ${response.text.substring(0, 300)}...`);
            }

            logger?.log('🔍 提取到的JSON字符串:', 'debug', { 
                jsonString: jsonString.substring(0, 500) + (jsonString.length > 500 ? '...' : ''),
                length: jsonString.length
            });

            // 尝试解析JSON，带错误恢复
            try {
                parseResult = JSON.parse(jsonString);
                logger?.log('✅ JSON解析成功', 'success');
            } catch (parseError) {
                logger?.log('⚠️ 首次JSON解析失败，尝试修复...', 'warning');
                
                // 尝试修复常见的JSON错误
                let fixedJson = jsonString
                    .replace(/，/g, ',')           // 中文逗号
                    .replace(/：/g, ':')           // 中文冒号
                    .replace(/"/g, '"')           // 中文引号
                    .replace(/"/g, '"')           // 中文引号
                    .replace(/\n\s*\/\/.*$/gm, '') // 移除注释
                    .replace(/,(\s*[}\]])/g, '$1') // 移除多余逗号
                    .replace(/([{\[,]\s*)(\w+):/g, '$1"$2":'); // 修复未加引号的键
                
                try {
                    parseResult = JSON.parse(fixedJson);
                    logger?.log('✅ JSON修复并解析成功', 'success');
                } catch (fixError) {
                    logger?.logError('❌ JSON修复也失败了', { 
                        originalError: parseError.message,
                        fixError: fixError.message,
                        originalJson: jsonString.substring(0, 200),
                        fixedJson: fixedJson.substring(0, 200)
                    });
                    throw new Error(`JSON解析失败: ${parseError.message}。原始内容: ${jsonString.substring(0, 200)}...`);
                }
            }

            // 验证和清理解析结果
            const cleanedResult = this.validateAndCleanOrderResult(parseResult);

            // 🔧 集成举牌服务检测逻辑
            const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
            if (pagingServiceManager) {
                // 检测原始文本是否包含举牌服务
                const hasPagingService = pagingServiceManager.detectPagingService(orderText);
                
                if (hasPagingService) {
                    logger?.log('🏷️ 检测到举牌服务，触发多订单模式', 'info');
                    
                    // 强制设置为多订单模式
                    cleanedResult.isMultiOrder = true;
                    
                    // 为所有订单标记需要举牌服务
                    if (cleanedResult.orders && cleanedResult.orders.length > 0) {
                        cleanedResult.orders.forEach(order => {
                            order.meetAndGreet = true;
                            order.needsPagingService = true;
                        });
                    }
                    
                    // 更新分析说明
                    cleanedResult.analysis = `${cleanedResult.analysis} - 检测到举牌服务，已触发多订单模式`;
                }
            }

            logger?.log(`🎯 一体化解析结果: ${cleanedResult.isMultiOrder ? '多订单' : '单订单'}`, 
                cleanedResult.isMultiOrder ? 'success' : 'info', {
                orderCount: cleanedResult.orderCount,
                confidence: cleanedResult.confidence,
                analysis: cleanedResult.analysis
            });

            return cleanedResult;

        } catch (error) {
            logger?.logError('Gemini一体化解析失败，回退处理', error);
            
            // 回退到最基本的结构
            return {
                isMultiOrder: false,
                orderCount: 1,
                confidence: 0,
                analysis: `解析失败: ${error.message}`,
                orders: [{
                    rawText: orderText,
                    customerName: null,
                    customerContact: null,
                    customerEmail: null,
                    pickup: null,
                    dropoff: null,
                    pickupDate: null,
                    pickupTime: null,
                    passengerCount: 1,
                    luggageCount: 1,
                    flightInfo: null,
                    otaReferenceNumber: null,
                    otaPrice: null,
                    currency: 'MYR',
                    carTypeId: 5, // 默认5 Seater
                    subCategoryId: 2, // 默认接机
                    drivingRegionId: 1, // 默认吉隆坡
                    languagesIdArray: [1, 2], // 默认中英文
                    extraRequirement: null,
                    babyChair: false,
                    tourGuide: false,
                    meetAndGreet: false
                }],
                originalText: orderText,
                error: error.message
            };
        }
    }

    /**
     * 验证和清理订单解析结果
     * @param {Object} result - Gemini返回的原始结果
     * @returns {Object} 清理后的标准结果
     */
    validateAndCleanOrderResult(result) {
        // 确保基本结构存在
        const cleanedResult = {
            isMultiOrder: Boolean(result.isMultiOrder),
            orderCount: result.orderCount || (result.orders ? result.orders.length : 1),
            confidence: Math.max(0, Math.min(1, result.confidence || 0.8)),
            analysis: result.analysis || '解析完成',
            orders: []
        };

        // 处理订单数组
        if (result.orders && Array.isArray(result.orders)) {
            cleanedResult.orders = result.orders.map(order => this.validateOrderFields(order));
        } else {
            // 如果没有orders数组，创建一个默认订单
            cleanedResult.orders = [this.createDefaultOrder(result.originalText || '')];
        }

        return cleanedResult;
    }

    /**
     * 验证和清理单个订单字段
     * @param {Object} order - 原始订单对象
     * @returns {Object} 清理后的订单对象
     */
    validateOrderFields(order) {
        return {
            rawText: order.rawText || '',
            customerName: order.customerName || null,
            customerContact: this.cleanPhoneNumber(order.customerContact),
            customerEmail: this.validateEmail(order.customerEmail),
            pickup: order.pickup || null,
            dropoff: order.dropoff || null,
            pickupDate: this.validateDate(order.pickupDate),
            pickupTime: this.validateTime(order.pickupTime),
            passengerCount: Math.max(1, parseInt(order.passengerCount) || 1),
            luggageCount: Math.max(0, parseInt(order.luggageCount) || 1),
            flightInfo: order.flightInfo || null,
            otaReferenceNumber: order.otaReferenceNumber || null,
            otaPrice: this.validatePrice(order.otaPrice),
            currency: this.validateCurrency(order.currency),
            carTypeId: this.validateCarTypeId(order.carTypeId, order.passengerCount),
            subCategoryId: this.validateSubCategoryId(order.subCategoryId),
            drivingRegionId: this.validateDrivingRegionId(order.drivingRegionId),
            languagesIdArray: this.validateLanguagesArray(order.languagesIdArray),
            extraRequirement: order.extraRequirement || null,
            babyChair: Boolean(order.babyChair),
            tourGuide: Boolean(order.tourGuide),
            meetAndGreet: Boolean(order.meetAndGreet)
        };
    }

    // 字段验证辅助方法
    cleanPhoneNumber(phone) {
        if (!phone) return null;
        // 清理电话号码格式，保留数字、+、-、空格
        return phone.toString().replace(/[^\d+\-\s]/g, '').trim() || null;
    }

    validateEmail(email) {
        if (!email) return null;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) ? email : null;
    }

    validateDate(date) {
        if (!date) return null;
        // 验证YYYY-MM-DD格式
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(date)) {
            const dateObj = new Date(date);
            if (!isNaN(dateObj.getTime())) {
                return date;
            }
        }
        return null;
    }

    validateTime(time) {
        if (!time) return null;
        // 验证HH:MM格式
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return timeRegex.test(time) ? time : null;
    }

    validatePrice(price) {
        const numPrice = parseFloat(price);
        return (!isNaN(numPrice) && numPrice >= 0) ? numPrice : null;
    }

    validateCurrency(currency) {
        const validCurrencies = ['MYR', 'USD', 'SGD', 'CNY'];
        return validCurrencies.includes(currency) ? currency : 'MYR';
    }

    validateCarTypeId(carTypeId, passengerCount) {
        const id = parseInt(carTypeId);
        if (!isNaN(id) && id > 0) return id;
        
        // 根据乘客人数推荐车型
        const passengers = parseInt(passengerCount) || 1;
        if (passengers <= 3) return 5; // 5 Seater
        if (passengers <= 4) return 37; // Extended 5
        if (passengers <= 5) return 15; // 7 Seater MPV
        if (passengers <= 6) return 32; // Velfire/Alphard
        if (passengers <= 7) return 20; // 10 Seater MPV
        if (passengers <= 10) return 23; // 14 Seater Van
        if (passengers <= 12) return 24; // 18 Seater Van
        return 25; // 30 Seat Mini Bus
    }

    validateSubCategoryId(subCategoryId) {
        const id = parseInt(subCategoryId);
        const validIds = [2, 3, 4]; // 接机、送机、包车
        return validIds.includes(id) ? id : 2; // 默认接机
    }

    validateDrivingRegionId(drivingRegionId) {
        const id = parseInt(drivingRegionId);
        return (!isNaN(id) && id > 0) ? id : 1; // 默认吉隆坡
    }

    validateLanguagesArray(languages) {
        if (!Array.isArray(languages)) return [1, 2]; // 默认中英文
        const validIds = languages.filter(id => {
            const numId = parseInt(id);
            return !isNaN(numId) && numId > 0;
        });
        return validIds.length > 0 ? validIds : [1, 2];
    }

    createDefaultOrder(rawText) {
        return {
            rawText: rawText,
            customerName: null,
            customerContact: null,
            customerEmail: null,
            pickup: null,
            dropoff: null,
            pickupDate: null,
            pickupTime: null,
            passengerCount: 1,
            luggageCount: 1,
            flightInfo: null,
            otaReferenceNumber: null,
            otaPrice: null,
            currency: 'MYR',
            carTypeId: 5,
            subCategoryId: 2,
            drivingRegionId: 1,
            languagesIdArray: [1, 2],
            extraRequirement: null,
            babyChair: false,
            tourGuide: false,
            meetAndGreet: false
        };
    }

    /**
     * 解析图片分析返回的结构化文本，转换为标准订单数据格式
     * @param {string} analysisText - 图片分析返回的结构化文本
     * @returns {Object} 标准化的订单数据对象
     */
    parseImageAnalysisResult(analysisText) {
        if (!analysisText || typeof analysisText !== 'string') {
            return {
                isMultiOrder: false,
                orderCount: 0,
                confidence: 0,
                analysis: '图片分析结果为空',
                orders: []
            };
        }

        try {
            // 检查是否为无效结果
            if (analysisText.includes('无法识别') || analysisText.trim().length < 10) {
                return {
                    isMultiOrder: false,
                    orderCount: 0,
                    confidence: 0,
                    analysis: '图片中未识别到有效订单信息',
                    orders: []
                };
            }

            // 检查是否为部分信息
            if (analysisText.includes('部分信息:')) {
                const partialText = analysisText.replace('部分信息:', '').trim();
                return {
                    isMultiOrder: false,
                    orderCount: 1,
                    confidence: 0.3,
                    analysis: '识别到部分信息，需要人工确认',
                    orders: [{
                        rawText: partialText,
                        customerName: null,
                        customerContact: null,
                        customerEmail: null,
                        pickup: null,
                        dropoff: null,
                        pickupDate: null,
                        pickupTime: null,
                        passengerCount: 1,
                        luggageCount: 1,
                        flightInfo: null,
                        otaReferenceNumber: null,
                        otaPrice: null,
                        currency: 'MYR',
                        carTypeId: 5,
                        subCategoryId: 2,
                        drivingRegionId: 1,
                        languagesIdArray: [2, 4],
                        extraRequirement: `图片识别结果: ${partialText}`,
                        babyChair: false,
                        tourGuide: false,
                        meetAndGreet: false
                    }]
                };
            }

            // 分割多个订单（按"订单1:"、"订单2:"等标识分割）
            const orderSections = this.splitOrderSections(analysisText);
            const orders = [];
            let totalConfidence = 0;

            for (let i = 0; i < orderSections.length; i++) {
                const orderData = this.parseOrderSection(orderSections[i]);
                if (orderData) {
                    orders.push(orderData);
                    totalConfidence += orderData._confidence || 0.8;
                }
            }

            const avgConfidence = orders.length > 0 ? totalConfidence / orders.length : 0;

            return {
                isMultiOrder: orders.length > 1,
                orderCount: orders.length,
                confidence: Math.min(avgConfidence, 0.95), // 图片识别置信度上限95%
                analysis: `从图片识别到 ${orders.length} 个订单，平均置信度 ${Math.round(avgConfidence * 100)}%`,
                orders: orders
            };

        } catch (error) {
            getLogger().logError('图片分析结果解析失败', error);
            return {
                isMultiOrder: false,
                orderCount: 0,
                confidence: 0,
                analysis: `解析失败: ${error.message}`,
                orders: [],
                error: error.message
            };
        }
    }

    /**
     * 分割订单段落
     * @param {string} text - 分析文本
     * @returns {Array} 订单段落数组
     */
    splitOrderSections(text) {
        // 按订单标识分割
        const orderPattern = /订单\s*\d+\s*[:：]/g;
        const sections = [];
        
        if (orderPattern.test(text)) {
            // 有明确的订单标识
            const parts = text.split(orderPattern);
            // 跳过第一个空段落
            for (let i = 1; i < parts.length; i++) {
                if (parts[i].trim()) {
                    sections.push(parts[i].trim());
                }
            }
        } else {
            // 没有订单标识，作为单个订单处理
            sections.push(text.trim());
        }
        
        return sections.length > 0 ? sections : [text];
    }

    /**
     * 解析单个订单段落
     * @param {string} section - 订单段落文本
     * @returns {Object} 订单数据对象
     */
    parseOrderSection(section) {
        try {
            const orderData = {
                rawText: section,
                customerName: this.extractField(section, ['客户姓名', '姓名']),
                customerContact: this.extractField(section, ['联系电话', '电话', '手机']),
                customerEmail: this.extractField(section, ['客户邮箱', '邮箱']),
                pickup: this.extractField(section, ['上车地点', '出发地']),
                dropoff: this.extractField(section, ['目的地点', '目的地']),
                pickupDate: this.extractAndFormatDate(section, ['接送日期', '日期']),
                pickupTime: this.extractAndFormatTime(section, ['接送时间', '时间']),
                passengerCount: this.extractNumber(section, ['乘客人数', '人数']),
                luggageCount: this.extractNumber(section, ['行李件数', '行李']),
                flightInfo: this.extractField(section, ['航班信息', '航班号']),
                otaReferenceNumber: this.enhancedOtaReferenceExtractor(section), // 使用增强提取器
                otaPrice: this.extractPrice(section),
                currency: this.extractCurrency(section),
                carTypeId: this.recommendCarType(this.extractNumber(section, ['乘客人数', '人数'])),
                subCategoryId: this.detectServiceType(section),
                drivingRegionId: this.detectDrivingRegion(section),
                languagesIdArray: this.detectLanguages(section),
                extraRequirement: this.extractField(section, ['特殊要求', '备注', '要求']),
                babyChair: this.detectSpecialService(section, ['儿童座椅', '婴儿座椅']),
                tourGuide: this.detectSpecialService(section, ['导游', '司导']),
                meetAndGreet: this.detectSpecialService(section, ['迎接服务', '接机牌', '举牌']),
                _confidence: this.calculateSectionConfidence(section)
            };

            return orderData;

        } catch (error) {
            getLogger().logError('订单段落解析失败', error);
            return null;
        }
    }

    /**
     * 提取字段值
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} 提取的值
     */
    extractField(text, labels) {
        for (const label of labels) {
            const pattern = new RegExp(`${label}\\s*[:：]\\s*([^\\n\\r]+)`, 'i');
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim().replace(/\[.*?\]/g, ''); // 移除[待确认]等标记
            }
        }
        return null;
    }

    /**
     * 提取并格式化日期
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} YYYY-MM-DD格式的日期
     */
    extractAndFormatDate(text, labels) {
        const dateValue = this.extractField(text, labels);
        if (dateValue) {
            // 验证日期格式
            const datePattern = /(\d{4})-(\d{2})-(\d{2})/;
            const match = dateValue.match(datePattern);
            if (match) {
                return dateValue;
            }
        }
        return null;
    }

    /**
     * 提取并格式化时间
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} HH:MM格式的时间
     */
    extractAndFormatTime(text, labels) {
        const timeValue = this.extractField(text, labels);
        if (timeValue) {
            // 验证时间格式
            const timePattern = /(\d{1,2}):(\d{2})/;
            const match = timeValue.match(timePattern);
            if (match) {
                const hour = match[1].padStart(2, '0');
                const minute = match[2];
                return `${hour}:${minute}`;
            }
        }
        return null;
    }

    /**
     * 提取数字
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {number|null} 提取的数字
     */
    extractNumber(text, labels) {
        const value = this.extractField(text, labels);
        if (value) {
            const numberMatch = value.match(/(\d+)/);
            if (numberMatch) {
                return parseInt(numberMatch[1], 10);
            }
        }
        return null;
    }

    /**
     * 提取价格
     * @param {string} text - 文本
     * @returns {number|null} 价格数值
     */
    extractPrice(text) {
        const priceField = this.extractField(text, ['价格信息', '价格', '费用']);
        if (priceField) {
            const priceMatch = priceField.match(/(\d+(?:\.\d+)?)/);
            if (priceMatch) {
                return parseFloat(priceMatch[1]);
            }
        }
        return null;
    }

    /**
     * 提取货币类型
     * @param {string} text - 文本
     * @returns {string} 货币代码
     */
    extractCurrency(text) {
        const priceField = this.extractField(text, ['价格信息', '价格', '费用']);
        if (priceField) {
            if (priceField.includes('MYR') || priceField.includes('马币') || priceField.includes('令吉')) return 'MYR';
            if (priceField.includes('USD') || priceField.includes('美元')) return 'USD';
            if (priceField.includes('SGD') || priceField.includes('新币')) return 'SGD';
            if (priceField.includes('CNY') || priceField.includes('人民币') || priceField.includes('元')) return 'CNY';
        }
        return 'MYR'; // 默认马币
    }

    /**
     * 检测服务类型
     * @param {string} text - 文本
     * @returns {number} 服务类型ID
     */
    detectServiceType(text) {
        const serviceField = this.extractField(text, ['服务类型']);
        if (serviceField) {
            if (serviceField.includes('接机')) return 2;
            if (serviceField.includes('送机')) return 3;
            if (serviceField.includes('包车')) return 4;
        }
        return 2; // 默认接机
    }

    /**
     * 检测行驶区域
     * @param {string} text - 文本
     * @returns {number} 区域ID
     */
    detectDrivingRegion(text) {
        const lowerText = text.toLowerCase();
        if (lowerText.includes('kl') || lowerText.includes('kuala lumpur') || lowerText.includes('吉隆坡')) return 1;
        if (lowerText.includes('penang') || lowerText.includes('槟城')) return 2;
        if (lowerText.includes('johor') || lowerText.includes('新山')) return 3;
        if (lowerText.includes('sabah') || lowerText.includes('沙巴')) return 4;
        if (lowerText.includes('singapore') || lowerText.includes('新加坡')) return 5;
        return 1; // 默认吉隆坡
    }

    /**
     * 检测语言
     * @param {string} text - 文本
     * @returns {Array} 语言ID数组
     */
    detectLanguages(text) {
        const hasChinese = /[\u4e00-\u9fff]/.test(text);
        const hasEnglish = /[a-zA-Z]/.test(text);
        
        if (hasChinese && hasEnglish) return [2, 4]; // 英文+中文
        if (hasChinese) return [4]; // 中文
        return [2]; // 默认英文
    }

    /**
     * 推荐车型
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 车型ID
     */
    recommendCarType(passengerCount) {
        if (!passengerCount || passengerCount <= 3) return 5; // 5 Seater
        if (passengerCount <= 4) return 37; // Extended 5
        if (passengerCount <= 5) return 15; // 7 Seater MPV
        if (passengerCount <= 6) return 32; // Velfire/Alphard
        if (passengerCount <= 7) return 20; // 10 Seater MPV
        return 23; // 14 Seater Van
    }

    /**
     * 检测特殊服务
     * @param {string} text - 文本
     * @param {Array} keywords - 关键词数组
     * @returns {boolean} 是否包含特殊服务
     */
    detectSpecialService(text, keywords) {
        const lowerText = text.toLowerCase();
        return keywords.some(keyword => 
            lowerText.includes(keyword.toLowerCase()) || 
            text.includes(keyword)
        );
    }

    /**
     * 计算段落置信度
     * @param {string} section - 订单段落
     * @returns {number} 置信度 (0-1)
     */
    calculateSectionConfidence(section) {
        let confidence = 0.5; // 基础置信度
        
        // 根据包含的字段数量提升置信度
        const fields = ['客户姓名', '联系电话', '接送日期', '接送时间', '上车地点', '目的地点'];
        let fieldCount = 0;
        
        for (const field of fields) {
            if (section.includes(field)) {
                fieldCount++;
            }
        }
        
        confidence += (fieldCount / fields.length) * 0.4; // 最多提升40%
        
        // 如果包含结构化标记，提升置信度
        if (section.includes('【订单信息】')) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 0.95); // 图片识别最高95%置信度
    }

    /**
     * 分析图片内容并提取订单信息 (增强版)
     * @param {string} base64Image - Base64编码的图片
     * @returns {Promise<Object>} 标准化的订单数据对象
     */
    async analyzeImage(base64Image) {
        if (!base64Image || typeof base64Image !== 'string') {
            throw new Error('无效的图片数据');
        }

        try {
            // 移除data:image前缀，保留纯base64
            const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
            
            // 专业OTA订单图片分析提示词 - 与文本解析保持一致的业务逻辑
            const imageAnalysisPrompt = `
你是专业的OTA(在线旅游代理)订单图片分析专家，专门处理马来西亚和新加坡地区的用车服务订单。

**核心任务**: 从图片中识别并提取完整的订单信息，按照标准化格式输出。

**分析步骤**:

1. **文字识别** - 准确识别图片中的所有文字内容（中文、英文、马来文、数字、符号）

2. **多订单检测** - 判断图片是否包含多个独立订单：
   - 多个不同的日期/时间
   - 多个不同的客户信息
   - 多个不同的航班信息
   - 明确的订单分隔标识

3. **订单信息提取** - 对每个订单提取以下关键信息：
   - **客户信息**: 姓名、电话、邮箱
   - **时间信息**: 日期、时间、航班号、航班时间
   - **地点信息**: 上车地点、目的地、机场代码
   - **服务信息**: 乘客人数、行李数量、车型要求
   - **价格信息**: 金额、货币类型
   - **特殊要求**: 儿童座椅、导游、迎接服务等

4. **业务规则应用**:
   - **服务类型判断**: 机场接送=接机/送机，市内=包车
   - **时间计算**: 送机时间 = 航班时间 - 3.5小时
   - **车型推荐**: 根据乘客人数智能推荐合适车型
   - **地点标准化**: 统一机场、酒店名称格式

**输出格式要求**:

如果识别到订单信息，请按以下格式输出完整的结构化订单描述：

\`\`\`
【订单信息】
客户姓名: [姓名]
联系电话: [电话号码]
客户邮箱: [邮箱地址]
服务类型: [接机/送机/包车]
航班信息: [航班号 时间]
接送日期: [YYYY-MM-DD]
接送时间: [HH:MM]
上车地点: [具体地址]
目的地点: [具体地址]
乘客人数: [数字]人
行李件数: [数字]件
特殊要求: [详细描述]
价格信息: [金额] [货币]
\`\`\`

如果是多个订单，请为每个订单单独输出上述格式，并在开头标注"订单1:"、"订单2:"等。

如果无法识别到完整订单信息，但有部分相关文字，请输出："部分信息: [识别到的文字内容]"

如果完全无法识别任何文字，请输出："无法识别"

**重要注意事项**:
- 确保日期格式为 YYYY-MM-DD
- 确保时间格式为 24小时制 HH:MM
- 机场代码要转换为完整机场名称
- 价格要明确标注货币类型(MYR/USD/SGD/CNY)
- 电话号码保留原始格式
- 如有疑问的信息，在字段后添加"[待确认]"标记

请开始分析图片:`;

            const requestBody = {
                contents: [{
                    parts: [
                        { text: imageAnalysisPrompt },
                        {
                            inline_data: {
                                mime_type: "image/jpeg", // 假设JPEG，Gemini会自动检测
                                data: base64Data
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 1024
                },
                safetySettings: [
                    { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                    { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                    { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                    { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
                ]
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout * 2); // 图片分析需要更长时间

            const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorBody = await response.json();
                throw new Error(`Vision API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
            }

            const data = await response.json();
            const rawAnalysisText = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();
            
            if (rawAnalysisText) {
                getLogger().log('图片分析完成，正在解析结果...', 'info', { textLength: rawAnalysisText.length });
                
                // 使用新的解析方法转换为标准订单数据格式
                const parsedResult = this.parseImageAnalysisResult(rawAnalysisText);
                
                getLogger().log('图片分析结果解析完成', 'success', {
                    orderCount: parsedResult.orderCount,
                    confidence: parsedResult.confidence,
                    isMultiOrder: parsedResult.isMultiOrder
                });
                
                return parsedResult;
            } else {
                getLogger().log('图片中未检测到任何内容', 'warning');
                return this.parseImageAnalysisResult('无法识别');
            }

        } catch (error) {
            getLogger().logError('图片分析失败', error);
            if (error.name === 'AbortError') {
                throw new Error('图片分析超时');
            }
            throw new Error(`图片分析失败: ${error.message}`);
        }
    }

    /**
     * 生成内容的封装方法，支持重试机制
     * @param {string} prompt - 要发送的提示文本
     * @param {number} maxRetries - 最大重试次数
     * @returns {Promise<object>} - 包含生成内容的对象
     */
    async generateContent(prompt, maxRetries = 2) {
        const requestBody = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 1,
                topP: 1,
                maxOutputTokens: 2048,
                stopSequences: []
            },
            safetySettings: [
                { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
            ]
        };

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                this.analysisState.currentRequest = controller;

                const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorBody = await response.json();
                    throw new Error(`API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
                }

                const data = await response.json();
                const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!rawText) {
                    throw new Error('API返回内容为空或格式不正确');
                }

                return { text: rawText };

            } catch (error) {
                if (attempt === maxRetries) {
                    getLogger().logError('生成内容请求失败', error);
                    throw error;
                }
                // 可选：在重试前添加延迟
                await this.sleep(1000);
            }
        }
    }

    /**
     * 🔧 生成缓存键
     * @param {string} orderText - 订单文本
     * @returns {string} 缓存键
     */
    generateCacheKey(orderText) {
        // 简单的哈希函数
        let hash = 0;
        const str = orderText.trim().toLowerCase();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return `multi_order_${Math.abs(hash)}`;
    }

    /**
     * 构建通用化提示词
     * @param {string} type - 提示词类型 ('standard', 'multiOrder', 'simple')
     * @param {Object} options - 构建选项
     * @returns {string} 构建的提示词
     */
    buildUniversalPrompt(type = 'standard', options = {}) {
        const logger = getLogger();

        try {
            const templates = this.promptTemplates;
            let prompt = '';

            // 基础系统角色
            prompt += templates.systemRole.base + '\n';
            prompt += templates.systemRole.task + '\n';
            prompt += templates.systemRole.format + '\n\n';
            prompt += '---\n\n';

            // JSON Schema
            prompt += templates.jsonSchema.header + '\n\n';
            prompt += templates.jsonSchema.structure + '\n\n';
            prompt += '---\n\n';

            // 根据类型添加特定规则
            switch (type) {
                case 'multiOrder':
                    prompt += this.buildMultiOrderRules(options);
                    break;
                case 'simple':
                    prompt += this.buildSimpleRules(options);
                    break;
                case 'standard':
                default:
                    prompt += this.buildStandardRules(options);
                    break;
            }

            // 添加容错指令
            prompt += this.buildErrorHandlingInstructions();

            logger?.log(`✅ 构建${type}类型提示词完成`, 'info', {
                promptLength: prompt.length,
                type: type
            });

            return prompt;

        } catch (error) {
            logger?.logError('构建通用化提示词失败', error);
            return this.orderParsingPrompt; // 降级到原始提示词
        }
    }

    /**
     * 构建标准规则
     * @param {Object} options - 选项
     * @returns {string} 标准规则文本
     */
    buildStandardRules(options = {}) {
        return `### **处理规则**

**规则1: 多订单识别**
- 仔细阅读整个文本，判断是否包含多个独立订单
- 独立订单通常由换行符、分隔线或明确编号分隔
- 如果识别出多个订单，为每个订单创建独立的JSON对象

**规则2: 字段提取优先级**
- 优先提取明确标识的字段（如"客户姓名："、"联系电话："）
- 使用上下文推断缺失信息
- 对于模糊信息，选择最可能的解释

**规则3: 数据验证**
- 确保日期格式为YYYY-MM-DD
- 确保时间格式为HH:MM
- 确保数字字段为有效数值
- 确保布尔字段为true/false或null

`;
    }

    /**
     * 构建多订单规则
     * @param {Object} options - 选项
     * @returns {string} 多订单规则文本
     */
    buildMultiOrderRules(options = {}) {
        return `### **多订单处理规则**

**强制多订单检测条件：**
- 包含多个不同的日期（如16/7、17/7、21/7等）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含多个不同的订单号/团号→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含接机和送机的组合→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含举牌服务关键词→ **强制设置isMultiOrder=true**
- 包含多个不同的客户姓名或联系方式→ **强制设置orderCount>=2, isMultiOrder=true**

**分离逻辑：**
- 按换行符、分隔符或订单标识分离
- 每个分离的部分创建独立的订单对象
- 保持原始文本的完整性和上下文

`;
    }

    /**
     * 构建简化规则
     * @param {Object} options - 选项
     * @returns {string} 简化规则文本
     */
    buildSimpleRules(options = {}) {
        return `### **简化处理规则**

**基础提取：**
- 提取客户姓名、联系方式、地址信息
- 提取时间、日期相关信息
- 提取价格和货币信息
- 其他字段可设为null

**格式要求：**
- 严格按照JSON Schema输出
- 未找到的字段设为null
- 确保JSON格式正确

`;
    }

    /**
     * 构建错误处理指令
     * @returns {string} 错误处理指令文本
     */
    buildErrorHandlingInstructions() {
        return `### **重要提醒**

**输出要求：**
- 必须返回有效的JSON数组格式
- 即使只有一个订单也要用数组包装
- 所有字段都必须包含，未找到的设为null
- 不要添加任何解释文字，只返回JSON

**错误处理：**
- 如果文本难以理解，尽力提取可识别的信息
- 对于模糊信息，选择最合理的解释
- 确保输出格式始终正确

---

`;
    }

    /**
     * 获取适合的提示词
     * @param {string} context - 上下文类型 ('single', 'multi', 'simple', 'retry')
     * @param {Object} options - 选项
     * @returns {string} 适合的提示词
     */
    getContextualPrompt(context = 'single', options = {}) {
        const logger = getLogger();

        try {
            switch (context) {
                case 'multi':
                    return this.buildUniversalPrompt('multiOrder', options);
                case 'simple':
                    return this.buildUniversalPrompt('simple', options);
                case 'retry':
                    // 重试时使用简化版本
                    return this.buildUniversalPrompt('simple', { ...options, simplified: true });
                case 'single':
                default:
                    return this.universalPrompt || this.orderParsingPrompt;
            }
        } catch (error) {
            logger?.logError('获取上下文提示词失败', error);
            return this.orderParsingPrompt; // 降级到原始提示词
        }
    }

    /**
     * 智能提示词选择
     * @param {string} orderText - 订单文本
     * @param {Object} analysisContext - 分析上下文
     * @returns {string} 选择的提示词
     */
    selectOptimalPrompt(orderText, analysisContext = {}) {
        const logger = getLogger();

        try {
            const textLength = orderText.length;
            const lineCount = orderText.split('\n').length;
            const hasMultipleTimePoints = (orderText.match(/\d{1,2}:\d{2}/g) || []).length > 1;
            const hasMultipleDates = (orderText.match(/\d{1,2}\/\d{1,2}/g) || []).length > 1;

            // 基于文本特征选择提示词
            if (hasMultipleDates || hasMultipleTimePoints || lineCount > 10) {
                logger?.log('🎯 选择多订单提示词', 'info');
                return this.getContextualPrompt('multi', analysisContext);
            } else if (textLength < 200 || analysisContext.isRetry) {
                logger?.log('🎯 选择简化提示词', 'info');
                return this.getContextualPrompt('simple', analysisContext);
            } else {
                logger?.log('🎯 选择标准提示词', 'info');
                return this.getContextualPrompt('single', analysisContext);
            }

        } catch (error) {
            logger?.logError('智能提示词选择失败', error);
            return this.orderParsingPrompt;
        }
    }

    /**
     * 🔧 增强版OTA参考号提取器
     * @param {string} text - 输入文本
     * @param {string} otaType - OTA平台类型（可选）
     * @returns {string|null} 提取的参考号或null
     */
    enhancedOtaReferenceExtractor(text, otaType = null) {
        if (!text || typeof text !== 'string') {
            return null;
        }

        const logger = getLogger();
        const cleanText = text.trim();

        try {
            // 步骤1: 使用平台特定规则（如果指定了平台）
            if (otaType && this.otaReferenceConfig.platformRules[otaType]) {
                const platformRule = this.otaReferenceConfig.platformRules[otaType];
                for (const pattern of platformRule.patterns) {
                    const match = cleanText.match(pattern);
                    if (match) {
                        logger?.log(`✅ 平台特定规则匹配: ${match[0]} (${otaType})`, 'success');
                        return match[0];
                    }
                }
            }

            // 步骤2: 查找团号/确认号等明确标识
            for (const [key, pattern] of Object.entries(this.otaReferenceConfig.targetPatterns)) {
                if (key === 'teamNumber' || key === 'confirmationCode') {
                    const match = cleanText.match(pattern);
                    if (match && match[1]) {
                        const candidate = match[1].trim();
                        if (this.isValidOtaReference(candidate)) {
                            logger?.log(`✅ 标识符匹配: ${candidate} (${key})`, 'success');
                            return candidate;
                        }
                    }
                }
            }

            // 步骤3: 分词并逐个检查候选项
            const words = this.extractPotentialReferences(cleanText);
            const validCandidates = [];

            for (const word of words) {
                // 排除明显不是参考号的内容
                if (this.shouldExcludeAsReference(word)) {
                    continue;
                }

                // 检查是否匹配目标模式
                if (this.matchesTargetPattern(word)) {
                    validCandidates.push({
                        value: word,
                        score: this.calculateReferenceScore(word, otaType)
                    });
                }
            }

            // 步骤4: 选择最佳候选项
            if (validCandidates.length > 0) {
                validCandidates.sort((a, b) => b.score - a.score);
                const bestCandidate = validCandidates[0];

                logger?.log(`✅ 最佳候选项: ${bestCandidate.value} (得分: ${bestCandidate.score})`, 'success');
                return bestCandidate.value;
            }

            logger?.log('❌ 未找到有效的OTA参考号', 'warn');
            return null;

        } catch (error) {
            logger?.logError('OTA参考号提取失败', error);
            return null;
        }
    }

    /**
     * 提取潜在的参考号候选项
     * @param {string} text - 输入文本
     * @returns {Array<string>} 候选项数组
     */
    extractPotentialReferences(text) {
        // 使用多种分割方式提取候选项
        const separators = /[\s,，。；;：:\n\r\t]+/;
        const words = text.split(separators).filter(word => word.trim().length >= 4);

        // 额外提取括号内容和特殊格式
        const bracketMatches = text.match(/[\(（][^）\)]+[\)）]/g) || [];
        const colonMatches = text.match(/[:：]\s*([A-Z0-9\-]{4,20})/g) || [];

        const allCandidates = [
            ...words,
            ...bracketMatches.map(m => m.replace(/[\(（）\)]/g, '')),
            ...colonMatches.map(m => m.replace(/[:：]\s*/, ''))
        ];

        return [...new Set(allCandidates)].filter(c => c.trim().length >= 4);
    }

    /**
     * 检查是否应该排除某个候选项
     * @param {string} candidate - 候选项
     * @returns {boolean} 是否应该排除
     */
    shouldExcludeAsReference(candidate) {
        const cleanCandidate = candidate.trim();

        for (const [key, pattern] of Object.entries(this.otaReferenceConfig.excludePatterns)) {
            if (pattern.test(cleanCandidate)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查候选项是否匹配目标模式
     * @param {string} candidate - 候选项
     * @returns {boolean} 是否匹配
     */
    matchesTargetPattern(candidate) {
        const cleanCandidate = candidate.trim().toUpperCase();

        for (const pattern of Object.values(this.otaReferenceConfig.targetPatterns)) {
            if (pattern.test(cleanCandidate)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 计算参考号候选项的得分
     * @param {string} candidate - 候选项
     * @param {string} otaType - OTA平台类型
     * @returns {number} 得分
     */
    calculateReferenceScore(candidate, otaType) {
        let score = 0;
        const cleanCandidate = candidate.trim().toUpperCase();

        // 基础得分：长度合理性
        if (cleanCandidate.length >= 6 && cleanCandidate.length <= 20) {
            score += 10;
        }

        // 格式得分：字母数字组合
        if (/^[A-Z0-9\-_]+$/.test(cleanCandidate)) {
            score += 15;
        }

        // 平台特定得分
        if (otaType && this.otaReferenceConfig.platformRules[otaType]) {
            const platformRule = this.otaReferenceConfig.platformRules[otaType];
            for (const pattern of platformRule.patterns) {
                if (pattern.test(cleanCandidate)) {
                    score += platformRule.priority * 5;
                    break;
                }
            }
        }

        // 特殊格式得分
        if (cleanCandidate.includes('-') || cleanCandidate.includes('_')) {
            score += 5; // 分隔符通常表示结构化编号
        }

        return score;
    }

    /**
     * 验证参考号是否有效
     * @param {string} reference - 参考号
     * @returns {boolean} 是否有效
     */
    isValidOtaReference(reference) {
        if (!reference || typeof reference !== 'string') {
            return false;
        }

        const cleanRef = reference.trim();

        // 基本长度检查
        if (cleanRef.length < 4 || cleanRef.length > 30) {
            return false;
        }

        // 不能全是数字或全是字母
        if (/^\d+$/.test(cleanRef) || /^[A-Za-z]+$/.test(cleanRef)) {
            return false;
        }

        // 必须包含字母或数字
        if (!/[A-Za-z0-9]/.test(cleanRef)) {
            return false;
        }

        return true;
    }

    /**
     * 智能容错解析
     * @param {string} responseText - Gemini响应文本
     * @param {number} attempt - 当前尝试次数
     * @returns {Object} 解析结果
     */
    intelligentErrorRecovery(responseText, attempt = 1) {
        const logger = getLogger();
        const maxAttempts = 3;

        try {
            // 尝试标准JSON解析
            const result = this.parseGeminiResponse(responseText);
            if (result && result.orders) {
                return result;
            }

            // 如果标准解析失败，尝试容错策略
            if (attempt < maxAttempts) {
                logger?.log(`🔄 尝试容错解析策略 ${attempt}/${maxAttempts}`, 'info');

                const strategies = [
                    () => this.tryFixJsonFormat(responseText),
                    () => this.tryExtractPartialData(responseText),
                    () => this.tryFallbackParsing(responseText)
                ];

                const strategy = strategies[attempt - 1];
                if (strategy) {
                    const recoveredResult = strategy();
                    if (recoveredResult) {
                        logger?.log(`✅ 容错策略 ${attempt} 成功`, 'success');
                        return recoveredResult;
                    }
                }
            }

            // 所有策略都失败，返回默认结果
            logger?.log('❌ 所有容错策略失败，返回默认结果', 'warn');
            return this.createFallbackResult(responseText);

        } catch (error) {
            logger?.logError('智能容错解析失败', error);
            return this.createFallbackResult(responseText);
        }
    }

    /**
     * 尝试修复JSON格式
     * @param {string} text - 响应文本
     * @returns {Object|null} 修复后的结果
     */
    tryFixJsonFormat(text) {
        try {
            // 移除可能的markdown格式
            let cleanText = text.replace(/```json\s*|\s*```/g, '');

            // 尝试修复常见的JSON格式问题
            cleanText = cleanText.replace(/,\s*}/g, '}'); // 移除尾随逗号
            cleanText = cleanText.replace(/,\s*]/g, ']'); // 移除数组尾随逗号

            // 尝试解析
            const parsed = JSON.parse(cleanText);
            if (Array.isArray(parsed)) {
                return {
                    isMultiOrder: parsed.length > 1,
                    orderCount: parsed.length,
                    orders: parsed,
                    confidence: 0.7,
                    analysis: '通过JSON格式修复解析'
                };
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 尝试提取部分数据
     * @param {string} text - 响应文本
     * @returns {Object|null} 部分数据结果
     */
    tryExtractPartialData(text) {
        try {
            const logger = getLogger();

            // 使用正则表达式提取关键信息
            const customerName = text.match(/"customer_name":\s*"([^"]+)"/)?.[1];
            const customerContact = text.match(/"customer_contact":\s*"([^"]+)"/)?.[1];
            const pickup = text.match(/"pickup":\s*"([^"]+)"/)?.[1];
            const dropoff = text.match(/"dropoff":\s*"([^"]+)"/)?.[1];

            if (customerName || customerContact || pickup || dropoff) {
                const partialOrder = {
                    customer_name: customerName || null,
                    customer_contact: customerContact || null,
                    customer_email: null,
                    ota_reference_number: null,
                    flight_info: null,
                    departure_time: null,
                    arrival_time: null,
                    flight_type: null,
                    pickup_date: null,
                    pickup_time: null,
                    pickup: pickup || null,
                    dropoff: dropoff || null,
                    passenger_count: null,
                    luggage_count: null,
                    sub_category_id: 2, // 默认接机
                    car_type_id: 1, // 默认车型
                    driving_region_id: 1, // 默认区域
                    languages_id_array: [2], // 默认英语
                    baby_chair: null,
                    tour_guide: null,
                    meet_and_greet: null,
                    needs_paging_service: null,
                    price: null,
                    currency: null,
                    extra_requirement: null
                };

                logger?.log('✅ 成功提取部分数据', 'info');
                return {
                    isMultiOrder: false,
                    orderCount: 1,
                    orders: [partialOrder],
                    confidence: 0.5,
                    analysis: '通过部分数据提取解析'
                };
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 尝试降级解析
     * @param {string} text - 响应文本
     * @returns {Object} 降级结果
     */
    tryFallbackParsing(text) {
        const logger = getLogger();

        // 创建最基本的订单结构
        const fallbackOrder = {
            customer_name: null,
            customer_contact: null,
            customer_email: null,
            ota_reference_number: null,
            flight_info: null,
            departure_time: null,
            arrival_time: null,
            flight_type: null,
            pickup_date: null,
            pickup_time: null,
            pickup: null,
            dropoff: null,
            passenger_count: 1,
            luggage_count: 1,
            sub_category_id: 2, // 默认接机
            car_type_id: 1, // 默认车型
            driving_region_id: 1, // 默认区域
            languages_id_array: [2], // 默认英语
            baby_chair: false,
            tour_guide: false,
            meet_and_greet: false,
            needs_paging_service: false,
            price: null,
            currency: 'MYR',
            extra_requirement: null
        };

        logger?.log('⚠️ 使用降级解析，返回默认订单结构', 'warn');
        return {
            isMultiOrder: false,
            orderCount: 1,
            orders: [fallbackOrder],
            confidence: 0.3,
            analysis: '降级解析 - 使用默认结构'
        };
    }

    /**
     * 创建降级结果
     * @param {string} originalText - 原始文本
     * @returns {Object} 降级结果
     */
    createFallbackResult(originalText) {
        return {
            isMultiOrder: false,
            orderCount: 0,
            orders: [],
            confidence: 0,
            analysis: '解析完全失败',
            error: '无法解析订单内容',
            originalText: originalText
        };
    }

    /**
     * 🔧 添加提示词变化
     * @param {string} orderText - 原始订单文本
     * @param {number} attempt - 尝试次数
     * @returns {string} 修改后的文本
     */
    addPromptVariation(orderText, attempt) {
        // 为了避免完全相同的结果，添加轻微的变化
        const variations = [
            orderText,
            orderText + '\n', // 添加换行
            orderText.replace(/\s+/g, ' '), // 标准化空格
        ];

        return variations[(attempt - 1) % variations.length];
    }

    /**
     * 🔧 分析结果一致性
     * @param {Array} results - 分析结果数组
     * @param {number} threshold - 一致性阈值
     * @returns {Object|null} 一致的结果或null
     */
    analyzeConsistency(results, threshold = 0.8) {
        if (!results || results.length === 0) return null;
        
        // 按订单数量分组
        const orderCountGroups = {};
        results.forEach(({ result }) => {
            const count = result.orderCount || 0;
            if (!orderCountGroups[count]) {
                orderCountGroups[count] = [];
            }
            orderCountGroups[count].push(result);
        });
        
        // 找到最常见的订单数量
        const mostCommonCount = Object.keys(orderCountGroups)
            .map(count => ({
                count: parseInt(count),
                results: orderCountGroups[count],
                frequency: orderCountGroups[count].length / results.length
            }))
            .sort((a, b) => b.frequency - a.frequency)[0];
        
        // 检查是否达到一致性阈值
        if (mostCommonCount.frequency >= threshold) {
            // 选择该组中置信度最高的结果
            const bestResult = mostCommonCount.results
                .sort((a, b) => (b.confidence || 0) - (a.confidence || 0))[0];
            
            const logger = getLogger();
            logger?.log('✅ 检测到一致性结果', 'success', {
                orderCount: mostCommonCount.count,
                frequency: mostCommonCount.frequency,
                threshold: threshold,
                confidence: bestResult.confidence
            });
            
            return bestResult;
        }
        
        return null;
    }

    /**
     * 🔧 选择最佳结果
     * @param {Array} results - 分析结果数组
     * @returns {Object|null} 最佳结果或null
     */
    selectBestResult(results) {
        if (!results || results.length === 0) return null;
        
        // 按置信度排序，选择最高的
        const sortedResults = results
            .map(({ result }) => result)
            .filter(result => result && result.orders)
            .sort((a, b) => (b.confidence || 0) - (a.confidence || 0));
        
        return sortedResults[0] || null;
    }

    /**
     * 🔧 获取验证统计信息
     * @returns {Object} 统计信息
     */
    getValidationStats() {
        return {
            ...this.validationStats,
            cacheSize: this.promptCache.size,
            successRate: this.validationStats.totalValidations > 0 ? 
                this.validationStats.successfulValidations / this.validationStats.totalValidations : 0,
            consistencyRate: this.validationStats.successfulValidations > 0 ? 
                this.validationStats.consistentResults / this.validationStats.successfulValidations : 0
        };
    }

    /**
     * 🔧 清理缓存
     * @param {number} maxAge - 最大缓存时间（毫秒）
     */
    cleanCache(maxAge = 3600000) { // 默认1小时
        const now = Date.now();
        const toDelete = [];
        
        for (const [key, value] of this.promptCache.entries()) {
            if (value.timestamp && (now - value.timestamp) > maxAge) {
                toDelete.push(key);
            }
        }
        
        toDelete.forEach(key => this.promptCache.delete(key));
        
        const logger = getLogger();
        logger?.log(`🧹 清理缓存：删除 ${toDelete.length} 个过期项`, 'info');
    }

} // GeminiService Class End

    // 创建全局Gemini服务实例（单例模式）
    let geminiServiceInstance = null;

    /**
     * 获取Gemini服务实例
     * @returns {GeminiService} Gemini服务实例
     */
    function getGeminiService() {
        if (!geminiServiceInstance) {
            geminiServiceInstance = new GeminiService();
        }
        return geminiServiceInstance;
    }

    // 创建默认实例以保持向后兼容性
    const geminiService = getGeminiService();

    // 将实例暴露到全局OTA命名空间
    window.OTA.geminiService = geminiService;
    window.OTA.getGeminiService = getGeminiService;

    // 为了向后兼容，也暴露到全局window
    window.geminiService = geminiService;
    window.getGeminiService = getGeminiService;

    // 🔧 添加全局验证管理命令
    window.geminiValidation = {
        /**
         * 获取验证统计信息
         */
        getStats: () => {
            const stats = geminiService.getValidationStats();
            console.log('📊 Gemini验证统计信息:', stats);
            return stats;
        },
        
        /**
         * 清理缓存
         */
        clearCache: (maxAge = 3600000) => {
            geminiService.cleanCache(maxAge);
            console.log('🧹 缓存已清理');
        },
        
        /**
         * 配置验证参数
         */
        configure: (options = {}) => {
            Object.assign(geminiService.verificationConfig, options);
            console.log('⚙️ 验证配置已更新:', geminiService.verificationConfig);
        },
        
        /**
         * 启用/禁用验证
         */
        toggle: (enabled = !geminiService.verificationConfig.enabled) => {
            geminiService.verificationConfig.enabled = enabled;
            console.log(`🔄 验证已${enabled ? '启用' : '禁用'}`);
        },
        
        /**
         * 测试验证功能
         */
        test: async (orderText = `接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) 客人：农玉琴 客人联系：18589880205
接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) 客人：甘小姐 客人联系：13119629639`) => {
            console.log('🧪 开始验证测试...');
            try {
                const result = await geminiService.detectAndSplitMultiOrdersWithVerification(orderText);
                console.log('✅ 验证测试成功:', result);
                return result;
            } catch (error) {
                console.error('❌ 验证测试失败:', error);
                throw error;
            }
        }
    };

    // 在控制台显示验证功能说明
    console.log(`
🔧 Gemini验证功能已启用！
使用以下命令管理验证系统：
- geminiValidation.getStats() - 查看统计信息
- geminiValidation.clearCache() - 清理缓存
- geminiValidation.configure({attempts: 5, consistencyThreshold: 0.9}) - 配置参数
- geminiValidation.toggle(true/false) - 启用/禁用验证
- geminiValidation.test() - 测试验证功能
    `);

})();