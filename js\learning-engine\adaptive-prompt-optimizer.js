/**
 * 智能学习型格式预处理引擎 - 自适应提示词优化器
 * 负责动态优化AI提示词，基于历史表现和用户反馈进行A/B测试
 * 提高AI分析的准确性和效率
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getServiceFromContainer(name) {
        return window.OTA.container ? window.OTA.container.get(name) : getService(name);
    }

    function getLearningConfig() {
        return getServiceFromContainer('learningConfig');
    }

    function getLearningStorageManager() {
        return getServiceFromContainer('learningStorageManager');
    }

    function getUserOperationLearner() {
        return getServiceFromContainer('userOperationLearner');
    }

    function getLogger() {
        return getServiceFromContainer('logger');
    }

    /**
     * 自适应提示词优化器类
     * 动态优化AI提示词以提高分析准确性
     */
    class AdaptivePromptOptimizer {
        constructor() {
            this.config = getLearningConfig();
            this.storageManager = getLearningStorageManager();
            this.operationLearner = getUserOperationLearner();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 提示词模板库
            this.promptTemplates = this.initializePromptTemplates();
            
            // A/B测试配置
            this.abTestConfig = {
                enabled: true,
                testDuration: 7 * 24 * 60 * 60 * 1000, // 7天
                minSampleSize: 50,
                significanceLevel: 0.05
            };
            
            // 当前A/B测试
            this.activeTests = new Map();
            
            // 性能统计
            this.performanceStats = new Map();
            
            // 优化历史
            this.optimizationHistory = [];

            this.initialize();
        }

        /**
         * 初始化自适应提示词优化器
         */
        initialize() {
            try {
                // 加载现有的优化数据
                this.loadOptimizationData();
                
                // 恢复活跃的A/B测试
                this.restoreActiveTests();
                
                this.logger?.log('自适应提示词优化器初始化完成', 'info', {
                    version: this.version,
                    templateCount: Object.keys(this.promptTemplates).length,
                    activeTests: this.activeTests.size
                });

            } catch (error) {
                this.logger?.logError('自适应提示词优化器初始化失败', error);
            }
        }

        /**
         * 初始化提示词模板
         */
        initializePromptTemplates() {
            return {
                // 多订单检测模板
                multiOrderDetection: {
                    baseline: {
                        id: 'multi_order_baseline',
                        template: `请分析以下订单文本，识别是否包含多个订单。如果是多个订单，请将它们分离并提取每个订单的详细信息。

订单文本：
{orderText}

请以JSON格式返回结果，包含以下字段：
- orderCount: 订单数量
- orders: 订单数组，每个订单包含客户信息、接送信息、时间、价格等`,
                        performance: { accuracy: 0.8, speed: 1.0, userSatisfaction: 0.75 }
                    },
                    optimized_v1: {
                        id: 'multi_order_optimized_v1',
                        template: `作为专业的订单分析专家，请仔细分析以下OTA订单文本。重点关注：
1. 不同的客户姓名和联系方式
2. 不同的接送时间和地点
3. 分隔符（如"---"、"订单2"等）

订单文本：
{orderText}

分析要求：
- 如果发现多个独立订单，请分别提取
- 确保每个订单信息完整准确
- 特别注意马来西亚地名和中文姓名的处理

返回JSON格式，结构如下：
{
  "orderCount": 数字,
  "confidence": 0-1之间的置信度,
  "orders": [订单数组]
}`,
                        performance: { accuracy: 0.85, speed: 0.9, userSatisfaction: 0.8 }
                    },
                    context_aware: {
                        id: 'multi_order_context_aware',
                        template: `基于以下上下文信息分析订单：

历史模式：{historicalPatterns}
常见错误：{commonErrors}
用户偏好：{userPreferences}

订单文本：
{orderText}

请根据历史经验和常见模式进行分析，特别注意：
- 之前类似订单的处理方式
- 避免历史上的常见错误
- 符合用户的处理偏好

返回详细的JSON分析结果。`,
                        performance: { accuracy: 0.9, speed: 0.8, userSatisfaction: 0.85 }
                    }
                },

                // 字段提取模板
                fieldExtraction: {
                    baseline: {
                        id: 'field_extraction_baseline',
                        template: `从以下文本中提取{fieldName}信息：

文本：{text}

请返回提取的{fieldName}值。`,
                        performance: { accuracy: 0.75, speed: 1.0, userSatisfaction: 0.7 }
                    },
                    enhanced: {
                        id: 'field_extraction_enhanced',
                        template: `作为数据提取专家，请从以下文本中准确提取{fieldName}信息。

文本：{text}

提取要求：
- 确保格式正确（{fieldFormat}）
- 如果有多个候选值，选择最合适的
- 如果信息不明确，请标注置信度

返回格式：
{
  "value": "提取的值",
  "confidence": 置信度,
  "alternatives": ["其他可能的值"]
}`,
                        performance: { accuracy: 0.82, speed: 0.9, userSatisfaction: 0.78 }
                    }
                },

                // 错误修正模板
                errorCorrection: {
                    baseline: {
                        id: 'error_correction_baseline',
                        template: `请修正以下{fieldName}字段的错误：

原始值：{originalValue}
错误类型：{errorType}

请提供修正后的值。`,
                        performance: { accuracy: 0.7, speed: 1.0, userSatisfaction: 0.65 }
                    },
                    contextual: {
                        id: 'error_correction_contextual',
                        template: `基于上下文修正{fieldName}字段错误：

原始值：{originalValue}
错误类型：{errorType}
上下文：{context}
相似案例：{similarCases}

请考虑上下文信息和历史案例，提供最合适的修正值。

返回格式：
{
  "correctedValue": "修正后的值",
  "confidence": 置信度,
  "reasoning": "修正理由"
}`,
                        performance: { accuracy: 0.88, speed: 0.85, userSatisfaction: 0.82 }
                    }
                }
            };
        }

        /**
         * 生成优化的提示词
         * @param {string} templateType - 模板类型
         * @param {Object} variables - 变量替换
         * @param {Object} context - 上下文信息
         * @returns {Object} 优化的提示词结果
         */
        generateOptimizedPrompt(templateType, variables = {}, context = {}) {
            try {
                // 选择最佳模板
                const selectedTemplate = this.selectBestTemplate(templateType, context);
                
                // 应用变量替换
                const optimizedPrompt = this.applyVariableSubstitution(selectedTemplate.template, variables, context);
                
                // 记录使用情况
                this.recordPromptUsage(selectedTemplate.id, context);
                
                // 如果有A/B测试，可能返回测试版本
                const finalPrompt = this.applyABTesting(templateType, optimizedPrompt, selectedTemplate);
                
                return {
                    prompt: finalPrompt,
                    templateId: selectedTemplate.id,
                    templateType: templateType,
                    variables: variables,
                    context: context,
                    timestamp: new Date().toISOString()
                };

            } catch (error) {
                this.logger?.logError('生成优化提示词失败', error);
                return this.getFallbackPrompt(templateType, variables);
            }
        }

        /**
         * 选择最佳模板
         * @param {string} templateType - 模板类型
         * @param {Object} context - 上下文
         * @returns {Object} 选择的模板
         */
        selectBestTemplate(templateType, context) {
            const templates = this.promptTemplates[templateType];
            if (!templates) {
                throw new Error(`未找到模板类型: ${templateType}`);
            }

            // 基于性能指标选择最佳模板
            let bestTemplate = null;
            let bestScore = -1;

            Object.values(templates).forEach(template => {
                const score = this.calculateTemplateScore(template, context);
                if (score > bestScore) {
                    bestScore = score;
                    bestTemplate = template;
                }
            });

            return bestTemplate || templates.baseline;
        }

        /**
         * 计算模板分数
         * @param {Object} template - 模板
         * @param {Object} context - 上下文
         * @returns {number} 分数
         */
        calculateTemplateScore(template, context) {
            const performance = template.performance;
            const stats = this.performanceStats.get(template.id);
            
            // 基础分数（基于设计时的性能）
            let score = performance.accuracy * 0.4 + 
                       performance.speed * 0.3 + 
                       performance.userSatisfaction * 0.3;

            // 如果有实际使用统计，调整分数
            if (stats) {
                const actualAccuracy = stats.successRate || performance.accuracy;
                const actualSpeed = stats.averageResponseTime ? 
                    Math.max(0, 1 - (stats.averageResponseTime - 1000) / 5000) : performance.speed;
                
                score = actualAccuracy * 0.5 + actualSpeed * 0.3 + performance.userSatisfaction * 0.2;
            }

            // 基于上下文调整
            if (context.priority === 'accuracy') {
                score = performance.accuracy * 0.7 + performance.speed * 0.15 + performance.userSatisfaction * 0.15;
            } else if (context.priority === 'speed') {
                score = performance.accuracy * 0.3 + performance.speed * 0.5 + performance.userSatisfaction * 0.2;
            }

            return score;
        }

        /**
         * 应用变量替换
         * @param {string} template - 模板字符串
         * @param {Object} variables - 变量
         * @param {Object} context - 上下文
         * @returns {string} 替换后的提示词
         */
        applyVariableSubstitution(template, variables, context) {
            let result = template;

            // 基本变量替换
            Object.entries(variables).forEach(([key, value]) => {
                const placeholder = `{${key}}`;
                result = result.replace(new RegExp(placeholder, 'g'), value);
            });

            // 上下文增强
            if (context.historicalPatterns) {
                result = result.replace('{historicalPatterns}', 
                    this.formatHistoricalPatterns(context.historicalPatterns));
            }

            if (context.commonErrors) {
                result = result.replace('{commonErrors}', 
                    this.formatCommonErrors(context.commonErrors));
            }

            if (context.userPreferences) {
                result = result.replace('{userPreferences}', 
                    this.formatUserPreferences(context.userPreferences));
            }

            // 清理未替换的占位符
            result = result.replace(/\{[^}]+\}/g, '');

            return result;
        }

        /**
         * 应用A/B测试
         * @param {string} templateType - 模板类型
         * @param {string} prompt - 提示词
         * @param {Object} template - 模板信息
         * @returns {string} 可能修改的提示词
         */
        applyABTesting(templateType, prompt, template) {
            if (!this.abTestConfig.enabled) {
                return prompt;
            }

            const activeTest = this.activeTests.get(templateType);
            if (!activeTest) {
                return prompt;
            }

            // 随机分配到测试组
            const testGroup = Math.random() < 0.5 ? 'A' : 'B';
            
            // 记录测试分配
            this.recordABTestAssignment(activeTest.id, testGroup, template.id);

            // 返回对应的提示词版本
            return testGroup === 'A' ? activeTest.versionA : activeTest.versionB;
        }

        /**
         * 启动A/B测试
         * @param {string} templateType - 模板类型
         * @param {string} versionA - 版本A
         * @param {string} versionB - 版本B
         * @param {Object} testConfig - 测试配置
         * @returns {string} 测试ID
         */
        startABTest(templateType, versionA, versionB, testConfig = {}) {
            try {
                const testId = this.generateTestId();
                const test = {
                    id: testId,
                    templateType: templateType,
                    versionA: versionA,
                    versionB: versionB,
                    startTime: new Date().toISOString(),
                    duration: testConfig.duration || this.abTestConfig.testDuration,
                    minSampleSize: testConfig.minSampleSize || this.abTestConfig.minSampleSize,
                    results: {
                        A: { count: 0, successRate: 0, responseTime: 0 },
                        B: { count: 0, successRate: 0, responseTime: 0 }
                    },
                    assignments: []
                };

                this.activeTests.set(templateType, test);
                
                this.logger?.log('A/B测试已启动', 'info', {
                    testId: testId,
                    templateType: templateType,
                    duration: test.duration
                });

                return testId;

            } catch (error) {
                this.logger?.logError('启动A/B测试失败', error);
                return null;
            }
        }

        /**
         * 记录A/B测试分配
         */
        recordABTestAssignment(testId, group, templateId) {
            const test = Array.from(this.activeTests.values()).find(t => t.id === testId);
            if (test) {
                test.assignments.push({
                    group: group,
                    templateId: templateId,
                    timestamp: new Date().toISOString()
                });
            }
        }

        /**
         * 记录提示词使用情况
         * @param {string} templateId - 模板ID
         * @param {Object} context - 上下文
         */
        recordPromptUsage(templateId, context) {
            if (!this.performanceStats.has(templateId)) {
                this.performanceStats.set(templateId, {
                    usageCount: 0,
                    successCount: 0,
                    totalResponseTime: 0,
                    successRate: 0,
                    averageResponseTime: 0
                });
            }

            const stats = this.performanceStats.get(templateId);
            stats.usageCount++;
        }

        /**
         * 记录提示词性能结果
         * @param {string} templateId - 模板ID
         * @param {Object} result - 结果
         */
        recordPromptResult(templateId, result) {
            try {
                const stats = this.performanceStats.get(templateId);
                if (!stats) {
                    return;
                }

                if (result.success) {
                    stats.successCount++;
                }

                if (result.responseTime) {
                    stats.totalResponseTime += result.responseTime;
                }

                // 更新计算字段
                stats.successRate = stats.successCount / stats.usageCount;
                stats.averageResponseTime = stats.totalResponseTime / stats.usageCount;

                // 更新A/B测试结果
                this.updateABTestResults(templateId, result);

            } catch (error) {
                this.logger?.logError('记录提示词性能结果失败', error);
            }
        }

        /**
         * 更新A/B测试结果
         */
        updateABTestResults(templateId, result) {
            this.activeTests.forEach(test => {
                const assignment = test.assignments.find(a => a.templateId === templateId);
                if (assignment) {
                    const group = assignment.group;
                    const groupResults = test.results[group];
                    
                    groupResults.count++;
                    if (result.success) {
                        groupResults.successRate = 
                            (groupResults.successRate * (groupResults.count - 1) + 1) / groupResults.count;
                    } else {
                        groupResults.successRate = 
                            (groupResults.successRate * (groupResults.count - 1)) / groupResults.count;
                    }
                    
                    if (result.responseTime) {
                        groupResults.responseTime = 
                            (groupResults.responseTime * (groupResults.count - 1) + result.responseTime) / groupResults.count;
                    }
                }
            });
        }

        /**
         * 分析A/B测试结果
         * @param {string} testId - 测试ID
         * @returns {Object} 分析结果
         */
        analyzeABTestResults(testId) {
            const test = Array.from(this.activeTests.values()).find(t => t.id === testId);
            if (!test) {
                return null;
            }

            const resultsA = test.results.A;
            const resultsB = test.results.B;

            // 检查样本大小
            if (resultsA.count < test.minSampleSize || resultsB.count < test.minSampleSize) {
                return {
                    status: 'insufficient_data',
                    message: '样本大小不足，需要更多数据'
                };
            }

            // 简化的统计显著性检验
            const successRateDiff = Math.abs(resultsA.successRate - resultsB.successRate);
            const isSignificant = successRateDiff > 0.05; // 简化阈值

            const winner = resultsA.successRate > resultsB.successRate ? 'A' : 'B';
            const confidence = isSignificant ? 0.95 : 0.5;

            return {
                status: 'completed',
                winner: winner,
                confidence: confidence,
                isSignificant: isSignificant,
                results: {
                    A: resultsA,
                    B: resultsB
                },
                recommendation: isSignificant ? 
                    `版本${winner}表现更好，建议采用` : 
                    '两个版本表现相近，可以选择任一版本'
            };
        }

        /**
         * 完成A/B测试
         * @param {string} testId - 测试ID
         * @returns {Object} 测试结果
         */
        completeABTest(testId) {
            try {
                const analysis = this.analyzeABTestResults(testId);
                if (!analysis) {
                    return null;
                }

                // 移除活跃测试
                const test = Array.from(this.activeTests.values()).find(t => t.id === testId);
                if (test) {
                    this.activeTests.delete(test.templateType);
                    
                    // 保存到优化历史
                    this.optimizationHistory.push({
                        testId: testId,
                        templateType: test.templateType,
                        completedAt: new Date().toISOString(),
                        analysis: analysis
                    });

                    // 如果有明显的赢家，更新模板
                    if (analysis.isSignificant) {
                        this.updateTemplateBasedOnResults(test, analysis);
                    }
                }

                this.logger?.log('A/B测试已完成', 'info', {
                    testId: testId,
                    winner: analysis.winner,
                    confidence: analysis.confidence
                });

                return analysis;

            } catch (error) {
                this.logger?.logError('完成A/B测试失败', error);
                return null;
            }
        }

        /**
         * 基于测试结果更新模板
         */
        updateTemplateBasedOnResults(test, analysis) {
            const templateType = test.templateType;
            const winnerVersion = analysis.winner === 'A' ? test.versionA : test.versionB;
            
            // 创建新的优化模板
            const newTemplateId = `${templateType}_optimized_${Date.now()}`;
            
            if (!this.promptTemplates[templateType]) {
                this.promptTemplates[templateType] = {};
            }
            
            this.promptTemplates[templateType][newTemplateId] = {
                id: newTemplateId,
                template: winnerVersion,
                performance: {
                    accuracy: analysis.results[analysis.winner].successRate,
                    speed: 1.0 / (analysis.results[analysis.winner].responseTime / 1000),
                    userSatisfaction: 0.8 // 默认值
                },
                optimizedAt: new Date().toISOString(),
                basedOnTest: test.id
            };
        }

        /**
         * 获取回退提示词
         */
        getFallbackPrompt(templateType, variables) {
            const fallbackTemplate = this.promptTemplates[templateType]?.baseline;
            if (fallbackTemplate) {
                return {
                    prompt: this.applyVariableSubstitution(fallbackTemplate.template, variables, {}),
                    templateId: fallbackTemplate.id,
                    templateType: templateType,
                    isFallback: true
                };
            }

            return {
                prompt: `请分析以下内容：${JSON.stringify(variables)}`,
                templateId: 'emergency_fallback',
                templateType: templateType,
                isFallback: true
            };
        }

        /**
         * 格式化历史模式
         */
        formatHistoricalPatterns(patterns) {
            if (!patterns || patterns.length === 0) {
                return '暂无历史模式';
            }
            return patterns.map(p => `- ${p.description}: ${p.frequency}次`).join('\n');
        }

        /**
         * 格式化常见错误
         */
        formatCommonErrors(errors) {
            if (!errors || errors.length === 0) {
                return '暂无常见错误记录';
            }
            return errors.map(e => `- ${e.type}: ${e.description}`).join('\n');
        }

        /**
         * 格式化用户偏好
         */
        formatUserPreferences(preferences) {
            if (!preferences) {
                return '暂无用户偏好设置';
            }
            return Object.entries(preferences)
                .map(([key, value]) => `- ${key}: ${value}`)
                .join('\n');
        }

        /**
         * 生成测试ID
         */
        generateTestId() {
            return `abtest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 加载优化数据
         */
        loadOptimizationData() {
            try {
                // 从存储中加载优化历史和性能统计
                // 简化实现
                this.logger?.log('优化数据加载完成', 'info');
            } catch (error) {
                this.logger?.logError('加载优化数据失败', error);
            }
        }

        /**
         * 恢复活跃测试
         */
        restoreActiveTests() {
            try {
                // 从存储中恢复未完成的A/B测试
                // 简化实现
                this.logger?.log('活跃测试恢复完成', 'info');
            } catch (error) {
                this.logger?.logError('恢复活跃测试失败', error);
            }
        }

        /**
         * 获取优化统计
         */
        getOptimizationStats() {
            return {
                templateCount: Object.keys(this.promptTemplates).length,
                activeTests: this.activeTests.size,
                completedTests: this.optimizationHistory.length,
                performanceStats: Object.fromEntries(this.performanceStats)
            };
        }

        /**
         * 获取模板性能报告
         */
        getTemplatePerformanceReport() {
            const report = {};
            
            Object.entries(this.promptTemplates).forEach(([type, templates]) => {
                report[type] = {};
                Object.values(templates).forEach(template => {
                    const stats = this.performanceStats.get(template.id);
                    report[type][template.id] = {
                        performance: template.performance,
                        actualStats: stats || null
                    };
                });
            });

            return report;
        }
    }

    // 创建全局实例
    const adaptivePromptOptimizer = new AdaptivePromptOptimizer();

    // 导出到全局命名空间
    window.OTA.adaptivePromptOptimizer = adaptivePromptOptimizer;
    window.adaptivePromptOptimizer = adaptivePromptOptimizer; // 向后兼容

    // 工厂函数
    window.getAdaptivePromptOptimizer = function() {
        return window.OTA.adaptivePromptOptimizer || window.adaptivePromptOptimizer;
    };

    console.log('自适应提示词优化器加载完成', {
        version: adaptivePromptOptimizer.version,
        templateTypes: Object.keys(adaptivePromptOptimizer.promptTemplates).length,
        abTestEnabled: adaptivePromptOptimizer.abTestConfig.enabled
    });

})();
