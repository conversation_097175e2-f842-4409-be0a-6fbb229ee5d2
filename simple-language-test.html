<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单语言切换测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>简单语言切换测试</h1>
    
    <div class="test-section">
        <h2>测试内容</h2>
        <p data-i18n="header.title">OTA订单处理系统</p>
        <p data-i18n="login.title">用户登录</p>
        <p data-i18n="common.loading">加载中...</p>
        
        <select id="languageSelect">
            <option value="zh">中文</option>
            <option value="en">English</option>
        </select>
        
        <button onclick="switchLanguage()">切换语言</button>
        <button onclick="checkStatus()">检查状态</button>
        
        <div id="results"></div>
    </div>

    <!-- 只加载必要的脚本 -->
    <script src="js/i18n.js"></script>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            results.appendChild(div);
        }

        function checkStatus() {
            document.getElementById('results').innerHTML = '';
            
            // 检查全局函数
            log(`window.getI18nManager: ${typeof window.getI18nManager}`, 
                typeof window.getI18nManager === 'function' ? 'success' : 'error');
            
            // 检查OTA命名空间
            log(`window.OTA: ${typeof window.OTA}`, 
                typeof window.OTA === 'object' ? 'success' : 'error');
            
            if (window.OTA) {
                log(`window.OTA.i18nManager: ${typeof window.OTA.i18nManager}`, 
                    typeof window.OTA.i18nManager === 'object' ? 'success' : 'error');
            }
            
            // 尝试获取实例
            try {
                const i18nManager = window.getI18nManager();
                if (i18nManager) {
                    log(`获取实例成功，当前语言: ${i18nManager.getCurrentLanguage()}`, 'success');
                    
                    // 初始化（如果需要）
                    if (typeof i18nManager.init === 'function') {
                        i18nManager.init();
                        log('初始化完成', 'success');
                    }
                } else {
                    log('获取实例失败', 'error');
                }
            } catch (error) {
                log(`获取实例出错: ${error.message}`, 'error');
            }
        }

        function switchLanguage() {
            const selectedLanguage = document.getElementById('languageSelect').value;
            
            try {
                const i18nManager = window.getI18nManager();
                if (!i18nManager) {
                    log('无法获取国际化管理器', 'error');
                    return;
                }

                // 确保已初始化
                if (typeof i18nManager.init === 'function') {
                    i18nManager.init();
                }

                const oldLanguage = i18nManager.getCurrentLanguage();
                log(`切换前语言: ${oldLanguage}`);
                
                // 切换语言
                i18nManager.setLanguage(selectedLanguage);
                
                const newLanguage = i18nManager.getCurrentLanguage();
                log(`切换后语言: ${newLanguage}`, newLanguage === selectedLanguage ? 'success' : 'error');
                
                // 手动更新UI（因为没有完整的事件系统）
                updateUI(i18nManager);
                
            } catch (error) {
                log(`语言切换出错: ${error.message}`, 'error');
            }
        }

        function updateUI(i18nManager) {
            // 更新所有带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                const translation = i18nManager.t(key);
                element.textContent = translation;
                log(`更新 ${key}: "${translation}"`);
            });
        }

        // 页面加载完成后自动检查状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkStatus, 100);
        });
    </script>
</body>
</html>
