/**
 * 田字格网格调整器
 * 负责处理灵活网格系统的拖拽调整功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块 - 使用统一的服务定位器
function getLogger() {
    return getService('logger');
}

class GridResizer {
    constructor() {
        this.gridContainer = null;
        this.isResizing = false;
        this.currentHandle = null;
        this.currentPanel = null;
        this.currentColumn = null;
        this.startPosition = { x: 0, y: 0 };
        this.startGridSizes = {};

        // 严格的布局约束配置 - 更新为4+3布局
        this.layoutConstraints = {
            desktop: {
                leftColumn: {
                    minHeights: [200, 180, 120, 100], // 订单输入, 行程信息, 特殊需求, 额外要求
                    maxHeights: [400, 300, 200, 200]
                },
                rightColumn: {
                    minHeights: [160, 180, 220], // 基本信息, 客户信息, 服务配置
                    maxHeights: [300, 350, 400]
                }
            },
            tablet: {
                leftColumn: {
                    minHeights: [160, 140, 100, 80],
                    maxHeights: [350, 280, 180, 160]
                },
                rightColumn: {
                    minHeights: [140, 160, 180],
                    maxHeights: [280, 320, 350]
                }
            },
            mobile: {
                leftColumn: {
                    minHeights: [120, 100, 80, 60],
                    maxHeights: [300, 250, 150, 120]
                },
                rightColumn: {
                    minHeights: [100, 120, 140],
                    maxHeights: [250, 280, 300]
                }
            },
            portrait: {
                leftColumn: {
                    minHeights: [150, 120, 80, 80], // 竖屏时减少最小高度
                    maxHeights: [300, 250, 150, 150]
                },
                rightColumn: {
                    minHeights: [120, 140, 160], // 确保服务配置可见
                    maxHeights: [250, 280, 300]
                }
            }
        };

        this.contentMinHeights = new Map();
        this.resizeObserver = null;
        this.focusedPanel = null;
        this.keyboardAdjustmentStep = 0.05;
        this.init();
    }

    /**
     * 初始化网格调整器
     */
    init() {
        this.gridContainer = document.querySelector('.grid-container-new');
        if (!this.gridContainer) {
            const logger = getLogger();
            if (logger) {
                logger.log('新网格容器未找到，跳过网格调整器初始化', 'warning');
            }
            return;
        }

        this.setupEventListeners();
        this.initializeGridSizes();
        this.setupContentObserver();
        this.calculateContentMinHeights();
        this.enforceLayoutConstraints(); // 应用严格布局约束

        const logger = getLogger();
        if (logger) {
            logger.log('新网格调整器已初始化，应用严格布局约束', 'info');
        }
    }

    /**
     * 获取当前屏幕类型 - 支持竖屏检测
     */
    getCurrentScreenType() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        const isPortrait = height > width;

        // 竖屏模式优先级最高，解决内容被截断问题
        if (isPortrait) {
            return 'portrait';
        }

        if (width > 1024) return 'desktop';
        if (width > 768) return 'tablet';
        return 'mobile';
    }

    /**
     * 强制应用布局约束
     */
    enforceLayoutConstraints() {
        const screenType = this.getCurrentScreenType();
        const constraints = this.layoutConstraints[screenType];

        // 应用左列约束
        const leftColumn = this.gridContainer.querySelector('.column-left');
        if (leftColumn && constraints.leftColumn) {
            this.applyColumnConstraints(leftColumn, constraints.leftColumn, 'left');
        }

        // 应用右列约束
        const rightColumn = this.gridContainer.querySelector('.column-right');
        if (rightColumn && constraints.rightColumn) {
            this.applyColumnConstraints(rightColumn, constraints.rightColumn, 'right');
        }

        const logger = getLogger();
        if (logger) {
            logger.log(`应用${screenType}屏幕布局约束`, 'info');
        }
    }

    /**
     * 应用列约束
     */
    applyColumnConstraints(column, constraints, columnType) {
        const panels = column.querySelectorAll('.panel');
        const minHeights = constraints.minHeights;
        const maxHeights = constraints.maxHeights;

        panels.forEach((panel, index) => {
            if (index < minHeights.length) {
                panel.style.minHeight = `${minHeights[index]}px`;
                panel.style.maxHeight = `${maxHeights[index]}px`;
            }
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 为所有拖拽手柄添加事件监听器
        const handles = this.gridContainer.querySelectorAll('.resize-handle');
        handles.forEach(handle => {
            // 鼠标事件
            handle.addEventListener('mousedown', this.handleMouseDown.bind(this));
            // 触摸事件
            handle.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        });

        // 全局鼠标事件
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));

        // 全局触摸事件
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));

        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('focusin', this.handleFocusIn.bind(this));
        document.addEventListener('focusout', this.handleFocusOut.bind(this));

        // 防止文本选择
        document.addEventListener('selectstart', this.preventSelection.bind(this));

        // 窗口大小变化监听器 - 重新应用布局约束
        window.addEventListener('resize', this.handleWindowResize.bind(this));
    }

    /**
     * 初始化网格尺寸
     */
    initializeGridSizes() {
        // 只计算内容最小高度，不自动调整布局
        this.calculateContentMinHeights();
        
        // 保存当前的网格状态，但不修改
        const leftPanels = this.gridContainer.querySelectorAll('.column-left .panel');
        const rightPanels = this.gridContainer.querySelectorAll('.column-right .panel');
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整器已准备就绪，保持当前布局', 'info', {
                leftPanels: leftPanels.length,
                rightPanels: rightPanels.length
            });
        }
    }

    /**
     * 计算最优面板尺寸分配
     */
    calculateOptimalSizes(panels) {
        if (!panels || panels.length === 0) return [];
        
        const sizes = [];
        let totalContentHeight = 0;
        const panelHeights = [];
        
        // 计算每个面板的内容高度
        panels.forEach((panel, index) => {
            const content = panel.querySelector('.panel-content');
            const header = panel.querySelector('.section-header');
            
            if (content && header) {
                const contentHeight = Math.max(content.scrollHeight, 120); // 最小120px
                const headerHeight = header.offsetHeight || 40;
                const totalHeight = contentHeight + headerHeight + 32; // 加上padding
                
                panelHeights.push(totalHeight);
                totalContentHeight += totalHeight;
            } else {
                panelHeights.push(200); // 默认高度
                totalContentHeight += 200;
            }
        });
        
        // 计算相对比例
        panelHeights.forEach(height => {
            const ratio = height / totalContentHeight * panels.length;
            sizes.push(Math.max(ratio, 0.5)); // 最小比例0.5
        });
        
        return sizes;
    }

    /**
     * 设置内容观察器（已禁用自动响应）
     */
    setupContentObserver() {
        // 暂时禁用ResizeObserver以避免意外的自动调整
        // 这可以防止内容变化时触发不必要的布局重新计算
        const logger = getLogger();
        if (logger) {
            logger.log('内容观察器已禁用，避免自动布局调整', 'info');
        }
        
        /* 已禁用的代码：
        if (typeof ResizeObserver !== 'undefined') {
            this.resizeObserver = new ResizeObserver((entries) => {
                // 当板块内容发生变化时，重新计算最小高度
                this.calculateContentMinHeights();
            });

            // 观察所有板块的内容区域
            const panels = this.gridContainer.querySelectorAll('.panel-content');
            panels.forEach(panel => {
                this.resizeObserver.observe(panel);
            });
        }
        */
    }

    /**
     * 计算每个板块的内容最小高度
     */
    calculateContentMinHeights() {
        const panels = this.gridContainer.querySelectorAll('.panel');

        panels.forEach(panel => {
            const panelId = panel.getAttribute('data-panel');
            const content = panel.querySelector('.panel-content');
            const header = panel.querySelector('.section-header');

            if (content && header) {
                // 计算内容的自然高度
                const contentHeight = content.scrollHeight;
                const headerHeight = header.offsetHeight;
                const padding = 16; // 额外的内边距

                const minHeight = contentHeight + headerHeight + padding;
                this.contentMinHeights.set(panelId, Math.max(minHeight, this.minPanelHeight));

                // 设置CSS变量以便样式使用
                panel.style.setProperty('--panel-min-height', `${minHeight}px`);
            }
        });

        const logger = getLogger();
        if (logger) {
            logger.log('板块内容最小高度已更新', 'info', Object.fromEntries(this.contentMinHeights));
        }
    }

    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(event) {
        event.preventDefault();

        this.isResizing = true;
        this.currentHandle = event.target;
        this.currentPanel = event.target.closest('.panel');
        this.currentColumn = event.target.closest('.column-left, .column-right');
        this.startPosition = { x: event.clientX, y: event.clientY };

        // 获取当前列的尺寸
        this.startGridSizes = this.getCurrentColumnSizes();

        // 添加调整中的样式
        this.gridContainer.classList.add('resizing');
        this.currentPanel.classList.add('resizing');

        const logger = getLogger();
        if (logger) {
            const columnType = this.currentColumn.classList.contains('column-left') ? '左列' : '右列';
            const panelType = this.currentPanel.getAttribute('data-panel');
            logger.log(`开始调整${columnType}的${panelType}板块`, 'info');
        }
    }

    /**
     * 处理鼠标移动事件
     */
    handleMouseMove(event) {
        if (!this.isResizing || !this.currentHandle) return;
        
        event.preventDefault();
        
        const deltaX = event.clientX - this.startPosition.x;
        const deltaY = event.clientY - this.startPosition.y;
        
        this.updateGridBasedOnHandle(deltaX, deltaY);
    }

    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(event) {
        if (!this.isResizing) return;
        
        this.isResizing = false;
        
        // 移除调整中的样式
        this.gridContainer.classList.remove('resizing');
        const resizingItems = this.gridContainer.querySelectorAll('.grid-item.resizing');
        resizingItems.forEach(item => item.classList.remove('resizing'));
        
        this.currentHandle = null;
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整完成', 'info');
        }
    }

    /**
     * 防止文本选择
     */
    preventSelection(event) {
        if (this.isResizing) {
            event.preventDefault();
        }
    }

    /**
     * 处理键盘按下事件
     */
    handleKeyDown(event) {
        // 只在有焦点板块时处理键盘事件
        if (!this.focusedPanel) return;

        // Ctrl + 上/下箭头：调整板块高度
        if (event.ctrlKey && (event.key === 'ArrowUp' || event.key === 'ArrowDown')) {
            event.preventDefault();

            const direction = event.key === 'ArrowUp' ? -1 : 1;
            this.adjustPanelWithKeyboard(this.focusedPanel, direction);
        }

        // Tab键：在可调整的板块间导航（由浏览器默认处理）
        // Escape键：清除焦点
        if (event.key === 'Escape') {
            this.clearPanelFocus();
        }
    }

    /**
     * 处理焦点进入事件
     */
    handleFocusIn(event) {
        const panel = event.target.closest('.panel');
        if (panel && panel.querySelector('.resize-handle-bottom')) {
            this.focusedPanel = panel;
            panel.classList.add('keyboard-focused');

            const logger = getLogger();
            if (logger) {
                const panelId = panel.getAttribute('data-panel');
                logger.log(`键盘焦点: ${panelId}`, 'debug');
            }
        }
    }

    /**
     * 处理焦点离开事件
     */
    handleFocusOut(event) {
        const panel = event.target.closest('.panel');
        if (panel) {
            // 延迟检查，确保焦点真的离开了板块
            setTimeout(() => {
                if (!panel.contains(document.activeElement)) {
                    panel.classList.remove('keyboard-focused');
                    if (this.focusedPanel === panel) {
                        this.focusedPanel = null;
                    }
                }
            }, 10);
        }
    }

    /**
     * 键盘调整板块高度
     */
    adjustPanelWithKeyboard(panel, direction) {
        const column = panel.closest('.column-left, .column-right');
        if (!column) return;

        const columnType = column.classList.contains('column-left') ? 'left' : 'right';
        const panels = column.querySelectorAll('.panel');
        const panelIndex = Array.from(panels).indexOf(panel);

        // 只能调整非最后一个板块
        if (panelIndex >= panels.length - 1) return;

        // 模拟拖拽调整
        this.currentPanel = panel;
        this.currentColumn = column;

        // 计算调整量
        const columnHeight = column.getBoundingClientRect().height;
        const deltaY = direction * this.keyboardAdjustmentStep * columnHeight;

        this.updateGridBasedOnHandle(0, deltaY);

        const logger = getLogger();
        if (logger) {
            const panelId = panel.getAttribute('data-panel');
            logger.log(`键盘调整: ${panelId}, 方向: ${direction > 0 ? '增加' : '减少'}`, 'info');
        }
    }

    /**
     * 清除板块焦点
     */
    clearPanelFocus() {
        if (this.focusedPanel) {
            this.focusedPanel.classList.remove('keyboard-focused');
            this.focusedPanel = null;
        }
    }

    /**
     * 处理触摸开始事件
     */
    handleTouchStart(event) {
        event.preventDefault();

        // 获取第一个触摸点
        const touch = event.touches[0];
        if (!touch) return;

        // 模拟鼠标按下事件
        const mouseEvent = {
            target: event.target,
            clientX: touch.clientX,
            clientY: touch.clientY,
            preventDefault: () => event.preventDefault()
        };

        this.handleMouseDown(mouseEvent);

        // 添加触摸反馈
        if (event.target.classList.contains('resize-handle-bottom')) {
            event.target.style.transform = 'scaleY(1.5)';

            // 如果支持震动，提供触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }
    }

    /**
     * 处理触摸移动事件
     */
    handleTouchMove(event) {
        if (!this.isResizing) return;

        event.preventDefault();

        // 获取第一个触摸点
        const touch = event.touches[0];
        if (!touch) return;

        // 模拟鼠标移动事件
        const mouseEvent = {
            clientX: touch.clientX,
            clientY: touch.clientY,
            preventDefault: () => event.preventDefault()
        };

        this.handleMouseMove(mouseEvent);
    }

    /**
     * 处理触摸结束事件
     */
    handleTouchEnd(event) {
        if (!this.isResizing) return;

        // 移除触摸反馈
        const handles = this.gridContainer.querySelectorAll('.resize-handle-bottom');
        handles.forEach(handle => {
            handle.style.transform = '';
        });

        // 模拟鼠标释放事件
        this.handleMouseUp(event);
    }

    /**
     * 根据拖拽手柄更新网格 - 改进的内容感知调整系统
     */
    updateGridBasedOnHandle(deltaX, deltaY) {
        if (!this.currentPanel || !this.currentColumn) return;

        const columnType = this.currentColumn.classList.contains('column-left') ? 'left' : 'right';
        const panels = this.currentColumn.querySelectorAll('.panel');
        const currentPanelIndex = Array.from(panels).indexOf(this.currentPanel);

        // 计算列容器高度
        const columnRect = this.currentColumn.getBoundingClientRect();
        const columnHeight = columnRect.height;

        // 获取当前列的尺寸比例
        const currentSizes = this.getCurrentColumnSizes()[columnType];
        const newSizes = [...currentSizes];

        // 计算新的比例
        const deltaRatio = deltaY / columnHeight;

        // 调整当前板块和下一个板块的比例
        if (currentPanelIndex < panels.length - 1) {
            const currentPanelId = panels[currentPanelIndex].getAttribute('data-panel');
            const nextPanelId = panels[currentPanelIndex + 1].getAttribute('data-panel');

            // 使用内容最小高度计算最小比例
            const currentMinHeight = this.contentMinHeights.get(currentPanelId) || this.minPanelHeight;
            const nextMinHeight = this.contentMinHeights.get(nextPanelId) || this.minPanelHeight;

            const currentMinRatio = currentMinHeight / columnHeight;
            const nextMinRatio = nextMinHeight / columnHeight;

            // 计算新的比例，确保不低于内容最小高度
            const newCurrentRatio = Math.max(currentMinRatio, newSizes[currentPanelIndex] + deltaRatio);
            const newNextRatio = Math.max(nextMinRatio, newSizes[currentPanelIndex + 1] - deltaRatio);

            // 验证调整的有效性
            const actualDelta = (newCurrentRatio - newSizes[currentPanelIndex]) + (newNextRatio - newSizes[currentPanelIndex + 1]);

            // 只有在变化合理的情况下才应用调整
            if (Math.abs(actualDelta) < 0.02) { // 允许小的误差
                newSizes[currentPanelIndex] = newCurrentRatio;
                newSizes[currentPanelIndex + 1] = newNextRatio;

                this.updateColumnSizes(columnType, newSizes);

                const logger = getLogger();
                if (logger) {
                    logger.log(`板块调整: ${currentPanelId}=${newCurrentRatio.toFixed(3)}, ${nextPanelId}=${newNextRatio.toFixed(3)}`, 'debug');
                }
            }
        }
    }

    /**
     * 更新列尺寸
     */
    updateColumnSizes(columnType, sizes) {
        const column = this.gridContainer.querySelector(`.column-${columnType}`);
        if (!column) return;

        const sizeString = sizes.map(size => `${size}fr`).join(' ');
        column.style.gridTemplateRows = sizeString;

        // 同时更新CSS变量以便其他地方使用
        sizes.forEach((size, index) => {
            this.gridContainer.style.setProperty(`--${columnType}-row-${index + 1}`, `${size}fr`);
        });
        
        // 注意：不再自动调用updateGridContainerRows，避免意外的布局变化
    }
    
    /**
     * 手动优化布局（只在用户明确要求时使用）
     */
    optimizeLayout() {
        const logger = getLogger();
        if (logger) {
            logger.log('手动优化布局开始', 'info');
        }
        
        // 设置优化的左列比例（基于内容需求）
        const leftPanels = this.gridContainer.querySelectorAll('.column-left .panel');
        const leftSizes = this.calculateOptimalSizes(leftPanels);
        this.updateColumnSizes('left', leftSizes);
        
        // 设置优化的右列比例（基于内容需求）
        const rightPanels = this.gridContainer.querySelectorAll('.column-right .panel');
        const rightSizes = this.calculateOptimalSizes(rightPanels);
        this.updateColumnSizes('right', rightSizes);
        
        if (logger) {
            logger.log('布局优化完成', 'success', {
                leftSizes,
                rightSizes
            });
        }
    }

    /**
     * 更新Grid容器的行配置（已禁用自动调用）
     */
    updateGridContainerRows() {
        // 注意：此方法现在只在手动优化时调用
        const logger = getLogger();
        if (logger) {
            logger.log('updateGridContainerRows被调用（应该只在手动优化时发生）', 'warning');
        }
        
        const leftPanels = this.gridContainer.querySelectorAll('.column-left .panel');
        const rightPanels = this.gridContainer.querySelectorAll('.column-right .panel');
        
        // 计算所有面板的最佳高度
        const leftSizes = this.calculateOptimalSizes(leftPanels);
        const rightSizes = this.calculateOptimalSizes(rightPanels);
        
        // 转换为像素值
        const leftPixelSizes = leftSizes.map(size => Math.max(size * 100, 200)); // 每fr大概100px，最小200px
        const rightPixelSizes = rightSizes.map(size => Math.max(size * 100, 200));
        
        // 构建grid-template-rows字符串
        const allSizes = [];
        for (let i = 0; i < Math.max(leftPixelSizes.length, rightPixelSizes.length); i++) {
            if (i < leftPixelSizes.length) allSizes.push(`${leftPixelSizes[i]}px`);
            if (i < rightPixelSizes.length) allSizes.push(`${rightPixelSizes[i]}px`);
        }
        
        this.gridContainer.style.gridTemplateRows = allSizes.join(' ');
        
        if (logger) {
            logger.log('Grid容器行配置已更新', 'info', {
                leftSizes: leftPixelSizes,
                rightSizes: rightPixelSizes,
                gridTemplateRows: allSizes.join(' ')
            });
        }
    }

    /**
     * 获取当前列尺寸
     */
    getCurrentColumnSizes() {
        const leftColumn = this.gridContainer.querySelector('.column-left');
        const rightColumn = this.gridContainer.querySelector('.column-right');

        const result = {};

        if (leftColumn) {
            const leftRows = getComputedStyle(leftColumn).gridTemplateRows.split(' ');
            result.left = leftRows.map(row => parseFloat(row) || 1);
        }

        if (rightColumn) {
            const rightRows = getComputedStyle(rightColumn).gridTemplateRows.split(' ');
            result.right = rightRows.map(row => parseFloat(row) || 1);
        }

        return result;
    }



    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 防抖处理，避免频繁调用
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            this.enforceLayoutConstraints();
            const logger = getLogger();
            if (logger) {
                logger.log('窗口大小变化，重新应用布局约束', 'info');
            }
        }, 250);
    }

    /**
     * 获取拖拽手柄类型
     */
    getHandleType(handle) {
        if (handle.classList.contains('resize-handle-right')) return 'column';
        if (handle.classList.contains('resize-handle-bottom')) return 'row';
        if (handle.classList.contains('resize-handle-corner')) return 'both';
        return 'unknown';
    }

    /**
     * 重置网格为默认尺寸
     */
    resetGrid() {
        this.updateColumnSizes('left', [1, 1, 1]);
        this.updateColumnSizes('right', [1, 1, 1, 1]);

        const logger = getLogger();
        if (logger) {
            logger.log('网格已重置为默认尺寸', 'info');
        }
    }

    /**
     * 销毁网格调整器
     */
    destroy() {
        if (this.gridContainer) {
            const handles = this.gridContainer.querySelectorAll('.resize-handle');
            handles.forEach(handle => {
                handle.removeEventListener('mousedown', this.handleMouseDown.bind(this));
            });
        }
        
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
        document.removeEventListener('selectstart', this.preventSelection.bind(this));
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整器已销毁', 'info');
        }
    }
}

// 导出到全局命名空间
if (typeof window !== 'undefined') {
    if (!window.OTA) {
        window.OTA = {};
    }
    window.OTA.GridResizer = GridResizer;
    
    // 向后兼容
    window.GridResizer = GridResizer;
}
