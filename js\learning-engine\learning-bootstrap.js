/**
 * 学习引擎启动器
 * 解决学习模块的循环依赖问题，确保服务注册顺序正确
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-18
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 学习引擎启动器类
     * 负责学习引擎服务的注册和初始化
     */
    class LearningBootstrap {
        constructor() {
            this.version = '1.0.0';
            this.initialized = false;
            this.services = new Map();
        }

        /**
         * 启动学习引擎
         */
        async initialize() {
            if (this.initialized) {
                return;
            }

            try {
                console.log('🚀 学习引擎启动器初始化开始');
                
                // 等待依赖容器准备就绪
                await this.waitForContainer();
                
                // 注册基础服务
                this.registerBaseServices();
                
                // 注册学习引擎服务
                await this.registerLearningServices();
                
                // 初始化核心服务
                await this.initializeCoreServices();
                
                this.initialized = true;
                console.log('✅ 学习引擎启动器初始化完成');
                
            } catch (error) {
                console.error('❌ 学习引擎启动器初始化失败:', error);
                throw error;
            }
        }

        /**
         * 等待依赖容器准备就绪
         */
        async waitForContainer() {
            return new Promise((resolve) => {
                const checkContainer = () => {
                    if (window.OTA && window.OTA.container) {
                        resolve();
                    } else {
                        setTimeout(checkContainer, 50);
                    }
                };
                checkContainer();
            });
        }

        /**
         * 注册基础服务
         */
        registerBaseServices() {
            const container = window.OTA.container;
            
            // 注册logger（如果尚未注册）
            if (!container.has('logger')) {
                container.register('logger', () => {
                    return window.OTA.logger || window.logger || {
                        log: (msg, level = 'info') => console.log(`[${level.toUpperCase()}] ${msg}`),
                        logError: (msg, error) => console.error(msg, error)
                    };
                });
            }

            // 注册基础配置
            if (!container.has('learningConfig')) {
                container.register('learningConfig', () => {
                    return window.learningConfig || window.OTA.learningConfig || {
                        get: (key, defaultValue) => {
                            const config = {
                                'storage.keyPrefix': 'ota_learning_',
                                'validation.enabled': true,
                                'cache.enabled': true,
                                'performance.monitoring': true
                            };
                            return config[key] || defaultValue;
                        },
                        set: (key, value) => {
                            console.log(`[LearningConfig] Setting ${key} = ${value}`);
                        }
                    };
                });
            }
        }

        /**
         * 注册学习引擎服务
         */
        async registerLearningServices() {
            const container = window.OTA.container;
            
            // 服务注册顺序很重要，确保依赖关系正确
            const serviceRegistry = [
                {
                    name: 'learningStorageManager',
                    factory: () => {
                        try {
                            return window.getLearningStorageManager ? window.getLearningStorageManager() : null;
                        } catch (e) {
                            console.warn('LearningStorageManager未加载，使用回退实现');
                            return this.createFallbackStorageManager();
                        }
                    },
                    dependencies: ['learningConfig', 'logger']
                },
                {
                    name: 'userOperationLearner',
                    factory: () => {
                        try {
                            return window.getUserOperationLearner ? window.getUserOperationLearner() : null;
                        } catch (e) {
                            console.warn('UserOperationLearner未加载，使用回退实现');
                            return this.createFallbackOperationLearner();
                        }
                    },
                    dependencies: ['learningConfig', 'learningStorageManager']
                },
                {
                    name: 'patternMatchingEngine',
                    factory: () => {
                        try {
                            return window.getPatternMatchingEngine ? window.getPatternMatchingEngine() : null;
                        } catch (e) {
                            console.warn('PatternMatchingEngine未加载，使用回退实现');
                            return this.createFallbackPatternMatcher();
                        }
                    },
                    dependencies: ['learningConfig']
                },
                {
                    name: 'errorClassificationSystem',
                    factory: () => {
                        try {
                            return window.getErrorClassificationSystem ? window.getErrorClassificationSystem() : null;
                        } catch (e) {
                            console.warn('ErrorClassificationSystem未加载，使用回退实现');
                            return this.createFallbackErrorClassifier();
                        }
                    },
                    dependencies: ['learningConfig', 'userOperationLearner']
                },
                {
                    name: 'intelligentCacheManager',
                    factory: () => {
                        try {
                            return window.getIntelligentCacheManager ? window.getIntelligentCacheManager() : null;
                        } catch (e) {
                            console.warn('IntelligentCacheManager未加载，使用回退实现');
                            return this.createFallbackCacheManager();
                        }
                    },
                    dependencies: ['learningConfig', 'patternMatchingEngine']
                },
                {
                    name: 'ruleGenerationEngine',
                    factory: () => {
                        try {
                            return window.getRuleGenerationEngine ? window.getRuleGenerationEngine() : null;
                        } catch (e) {
                            console.warn('RuleGenerationEngine未加载，使用回退实现');
                            return this.createFallbackRuleEngine();
                        }
                    },
                    dependencies: ['learningConfig', 'patternMatchingEngine', 'userOperationLearner']
                },
                {
                    name: 'performanceMonitor',
                    factory: () => {
                        try {
                            return window.getPerformanceMonitor ? window.getPerformanceMonitor() : null;
                        } catch (e) {
                            console.warn('PerformanceMonitor未加载，使用回退实现');
                            return this.createFallbackPerformanceMonitor();
                        }
                    },
                    dependencies: ['learningConfig']
                },
                {
                    name: 'performanceOptimizer',
                    factory: () => {
                        try {
                            return window.getPerformanceOptimizer ? window.getPerformanceOptimizer() : null;
                        } catch (e) {
                            console.warn('PerformanceOptimizer未加载，使用回退实现');
                            return this.createFallbackPerformanceOptimizer();
                        }
                    },
                    dependencies: ['learningConfig', 'intelligentCacheManager', 'performanceMonitor', 'userOperationLearner', 'ruleGenerationEngine']
                },
                {
                    name: 'intelligentFormManager',
                    factory: () => {
                        try {
                            return window.getIntelligentFormManager ? window.getIntelligentFormManager() : null;
                        } catch (e) {
                            console.warn('IntelligentFormManager未加载，使用回退实现');
                            return this.createFallbackFormManager();
                        }
                    },
                    dependencies: ['learningConfig', 'userOperationLearner', 'patternMatchingEngine', 'intelligentCacheManager']
                },
                {
                    name: 'realTimeValidator',
                    factory: () => {
                        try {
                            return window.getRealTimeValidator ? window.getRealTimeValidator() : null;
                        } catch (e) {
                            console.warn('RealTimeValidator未加载，使用回退实现');
                            return this.createFallbackValidator();
                        }
                    },
                    dependencies: ['learningConfig', 'errorClassificationSystem', 'ruleGenerationEngine', 'patternMatchingEngine']
                }
            ];

            // 按顺序注册服务
            serviceRegistry.forEach(service => {
                if (!container.has(service.name)) {
                    container.register(service.name, service.factory, {
                        singleton: true,
                        dependencies: service.dependencies
                    });
                }
            });
        }

        /**
         * 初始化核心服务
         */
        async initializeCoreServices() {
            const container = window.OTA.container;
            
            // 初始化基础服务
            const coreServices = ['learningConfig', 'logger'];
            coreServices.forEach(serviceName => {
                if (container.has(serviceName)) {
                    container.get(serviceName);
                }
            });

            console.log('📋 学习引擎核心服务初始化完成');
        }

        // 回退实现（简化版本）
        createFallbackStorageManager() {
            return {
                get: (key) => JSON.parse(localStorage.getItem(key) || 'null'),
                set: (key, value) => localStorage.setItem(key, JSON.stringify(value)),
                remove: (key) => localStorage.removeItem(key),
                clear: () => localStorage.clear(),
                getAllKeys: () => Object.keys(localStorage)
            };
        }

        createFallbackOperationLearner() {
            return {
                recordOperation: (operation) => console.log('记录操作:', operation),
                getUserOperations: () => [],
                getRecentOperations: (count) => [],
                getOperationCount: () => 0,
                cleanupOldOperations: () => {}
            };
        }

        createFallbackPatternMatcher() {
            return {
                calculateSimilarity: (a, b) => {
                    if (!a || !b) return 0;
                    const aStr = a.toString().toLowerCase();
                    const bStr = b.toString().toLowerCase();
                    if (aStr === bStr) return 1;
                    return aStr.includes(bStr) || bStr.includes(aStr) ? 0.7 : 0;
                },
                matchPattern: (value, pattern) => ({ hasMatch: false, confidence: 0 })
            };
        }

        createFallbackErrorClassifier() {
            return {
                classifyError: (error) => 'unknown',
                recordError: (error) => console.log('记录错误:', error),
                getValidationRules: () => ({}),
                getErrorStats: () => ({})
            };
        }

        createFallbackCacheManager() {
            return {
                set: (key, value, options) => console.log('缓存设置:', key, value),
                get: (key) => null,
                has: (key) => false,
                clear: () => console.log('缓存已清空'),
                getStats: () => ({ hitRate: 0, size: 0 })
            };
        }

        createFallbackRuleEngine() {
            return {
                generateValidationRule: (data) => console.log('生成规则:', data),
                getAllRules: () => [],
                cleanupIneffectiveRules: () => {},
                detectRuleConflicts: () => [],
                resolveRuleConflicts: (conflicts) => []
            };
        }

        createFallbackPerformanceMonitor() {
            return {
                startOperation: (name) => Date.now(),
                endOperation: (name, start) => console.log(`操作 ${name} 耗时: ${Date.now() - start}ms`),
                getRealTimeMetrics: () => ({}),
                registerAlertHandler: (handler) => {}
            };
        }

        createFallbackPerformanceOptimizer() {
            return {
                performOptimization: async (options) => ({ success: true, message: '优化完成' }),
                setAutoOptimization: (enabled) => console.log('自动优化:', enabled),
                getOptimizationStats: () => ({ totalOptimizations: 0, averageImprovement: 0 })
            };
        }

        createFallbackFormManager() {
            return {
                smartAutoFill: async (data) => ({}),
                generateFieldSuggestions: async (field, value) => [],
                getFormStats: () => ({})
            };
        }

        createFallbackValidator() {
            return {
                validateField: async (field, value) => ({ isValid: true }),
                getValidationStats: () => ({}),
                cleanupCache: () => {}
            };
        }

        /**
         * 获取启动器状态
         */
        getStatus() {
            return {
                initialized: this.initialized,
                services: Array.from(this.services.keys())
            };
        }
    }

    // 创建全局实例
    const learningBootstrap = new LearningBootstrap();

    // 导出到全局命名空间
    window.OTA.learningBootstrap = learningBootstrap;
    window.learningBootstrap = learningBootstrap;

    // 自动启动
    setTimeout(() => {
        learningBootstrap.initialize().catch(console.error);
    }, 100);

    console.log('🔄 学习引擎启动器已加载');

})();