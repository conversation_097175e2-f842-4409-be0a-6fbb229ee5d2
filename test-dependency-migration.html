<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>依赖迁移测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 依赖迁移测试报告</h1>
        
        <div class="test-section">
            <h3>核心架构测试</h3>
            <button class="test-button" onclick="testCoreArchitecture()">测试核心架构</button>
            <div id="coreArchitectureResults"></div>
        </div>

        <div class="test-section">
            <h3>服务定位器测试</h3>
            <button class="test-button" onclick="testServiceLocator()">测试服务定位器</button>
            <div id="serviceLocatorResults"></div>
        </div>

        <div class="test-section">
            <h3>依赖容器测试</h3>
            <button class="test-button" onclick="testDependencyContainer()">测试依赖容器</button>
            <div id="dependencyContainerResults"></div>
        </div>

        <div class="test-section">
            <h3>核心服务测试</h3>
            <button class="test-button" onclick="testCoreServices()">测试核心服务</button>
            <div id="coreServicesResults"></div>
        </div>

        <div class="test-section">
            <h3>学习引擎服务测试</h3>
            <button class="test-button" onclick="testLearningEngineServices()">测试学习引擎</button>
            <div id="learningEngineResults"></div>
        </div>

        <div class="test-section">
            <h3>工厂函数兼容性测试</h3>
            <button class="test-button" onclick="testFactoryFunctionsCompatibility()">测试工厂函数</button>
            <div id="factoryFunctionsResults"></div>
        </div>

        <div class="test-section">
            <h3>全面系统测试</h3>
            <button class="test-button" onclick="runFullSystemTest()">运行全面测试</button>
            <div id="fullSystemResults"></div>
        </div>
    </div>

    <!-- 核心架构模块 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>

    <!-- 基础工具和日志 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/multi-select-dropdown.js"></script>
    <script src="js/grid-resizer.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/paging-service-manager.js"></script>

    <!-- 学习引擎模块 -->
    <script src="js/learning-engine/learning-config.js"></script>
    <script src="js/learning-engine/learning-storage-manager.js"></script>
    <script src="js/learning-engine/user-operation-learner.js"></script>
    <script src="js/learning-engine/error-classification-system.js"></script>
    <script src="js/learning-engine/pattern-matching-engine.js"></script>
    <script src="js/learning-engine/correction-interface.js"></script>
    <script src="js/learning-engine/rule-generation-engine.js"></script>
    <script src="js/learning-engine/system-integration.js"></script>
    <script src="js/learning-engine/ui-correction-manager.js"></script>
    <script src="js/learning-engine/data-persistence-manager.js"></script>
    <script src="js/learning-engine/predictive-corrector.js"></script>
    <script src="js/learning-engine/adaptive-prompt-optimizer.js"></script>
    <script src="js/learning-engine/learning-effectiveness-evaluator.js"></script>
    <script src="js/learning-engine/intelligent-cache-manager.js"></script>
    <script src="js/learning-engine/performance-monitor.js"></script>
    <script src="js/learning-engine/performance-optimizer.js"></script>

    <script>
        // 测试工具函数
        function displayResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            element.appendChild(resultDiv);
        }

        function clearResults(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '';
        }

        // 测试核心架构
        function testCoreArchitecture() {
            clearResults('coreArchitectureResults');
            
            const tests = [
                {
                    name: '依赖容器存在',
                    check: () => !!window.OTA.container,
                    message: '依赖容器已正确初始化'
                },
                {
                    name: '服务定位器存在',
                    check: () => !!window.OTA.serviceLocator,
                    message: '服务定位器已正确初始化'
                },
                {
                    name: '应用启动协调器存在',
                    check: () => !!window.OTA.ApplicationBootstrap,
                    message: '应用启动协调器已正确加载'
                },
                {
                    name: '全局getService函数存在',
                    check: () => typeof window.getService === 'function',
                    message: '全局getService函数已正确暴露'
                },
                {
                    name: 'OTA命名空间完整',
                    check: () => window.OTA && typeof window.OTA === 'object',
                    message: 'OTA命名空间已正确设置'
                }
            ];

            tests.forEach(test => {
                try {
                    if (test.check()) {
                        displayResult('coreArchitectureResults', `✅ ${test.name}: ${test.message}`, 'success');
                    } else {
                        displayResult('coreArchitectureResults', `❌ ${test.name}: 测试失败`, 'error');
                    }
                } catch (error) {
                    displayResult('coreArchitectureResults', `❌ ${test.name}: ${error.message}`, 'error');
                }
            });
        }

        // 测试服务定位器
        function testServiceLocator() {
            clearResults('serviceLocatorResults');
            
            const coreServices = [
                'appState', 'logger', 'apiService', 'geminiService', 'utils',
                'imageUploadManager', 'currencyConverter', 'i18nManager'
            ];

            coreServices.forEach(serviceName => {
                try {
                    const service = getService(serviceName);
                    if (service) {
                        displayResult('serviceLocatorResults', `✅ ${serviceName}: 服务获取成功`, 'success');
                    } else {
                        displayResult('serviceLocatorResults', `❌ ${serviceName}: 服务获取失败`, 'error');
                    }
                } catch (error) {
                    displayResult('serviceLocatorResults', `❌ ${serviceName}: ${error.message}`, 'error');
                }
            });
        }

        // 测试依赖容器
        function testDependencyContainer() {
            clearResults('dependencyContainerResults');
            
            const container = window.OTA.container;
            if (!container) {
                displayResult('dependencyContainerResults', '❌ 依赖容器未找到', 'error');
                return;
            }

            try {
                const status = container.getStatus();
                displayResult('dependencyContainerResults', `✅ 容器状态: 已注册${status.registeredServices}个服务，已创建${status.createdInstances}个实例`, 'success');
                
                const services = container.getRegisteredServices();
                displayResult('dependencyContainerResults', `📋 已注册服务: ${services.join(', ')}`, 'info');
                
                const instances = container.getCreatedInstances();
                displayResult('dependencyContainerResults', `🏗️ 已创建实例: ${instances.join(', ')}`, 'info');
                
                // 测试诊断功能
                const diagnosis = container.diagnose();
                if (diagnosis.healthy) {
                    displayResult('dependencyContainerResults', `✅ 容器健康检查: 通过`, 'success');
                } else {
                    displayResult('dependencyContainerResults', `⚠️ 容器健康检查: ${diagnosis.issues.join(', ')}`, 'warning');
                }
            } catch (error) {
                displayResult('dependencyContainerResults', `❌ 容器测试失败: ${error.message}`, 'error');
            }
        }

        // 测试核心服务
        function testCoreServices() {
            clearResults('coreServicesResults');
            
            const services = [
                { name: 'appState', methods: ['get', 'set', 'clear'] },
                { name: 'logger', methods: ['log', 'logError'] },
                { name: 'apiService', methods: ['getAllSystemData', 'createOrder'] },
                { name: 'geminiService', methods: ['analyzeOrderText', 'isAvailable'] },
                { name: 'utils', methods: ['formatDate', 'debounce'] }
            ];

            services.forEach(serviceInfo => {
                try {
                    const service = getService(serviceInfo.name);
                    if (service) {
                        displayResult('coreServicesResults', `✅ ${serviceInfo.name}: 服务存在`, 'success');
                        
                        // 测试方法是否存在
                        serviceInfo.methods.forEach(method => {
                            if (typeof service[method] === 'function') {
                                displayResult('coreServicesResults', `  ✅ ${serviceInfo.name}.${method}(): 方法存在`, 'success');
                            } else {
                                displayResult('coreServicesResults', `  ⚠️ ${serviceInfo.name}.${method}(): 方法不存在`, 'warning');
                            }
                        });
                    } else {
                        displayResult('coreServicesResults', `❌ ${serviceInfo.name}: 服务获取失败`, 'error');
                    }
                } catch (error) {
                    displayResult('coreServicesResults', `❌ ${serviceInfo.name}: ${error.message}`, 'error');
                }
            });
        }

        // 测试学习引擎服务
        function testLearningEngineServices() {
            clearResults('learningEngineResults');
            
            const learningServices = [
                'learningConfig', 'learningStorageManager', 'userOperationLearner',
                'errorClassificationSystem', 'patternMatchingEngine', 'correctionInterface',
                'ruleGenerationEngine', 'predictiveCorrector', 'performanceMonitor',
                'intelligentCacheManager'
            ];

            learningServices.forEach(serviceName => {
                try {
                    const service = getService(serviceName);
                    if (service) {
                        displayResult('learningEngineResults', `✅ ${serviceName}: 学习引擎服务存在`, 'success');
                        
                        // 测试配置是否有效
                        if (serviceName === 'learningConfig' && service.validate) {
                            const validation = service.validate();
                            if (validation.valid) {
                                displayResult('learningEngineResults', `  ✅ ${serviceName}: 配置验证通过`, 'success');
                            } else {
                                displayResult('learningEngineResults', `  ⚠️ ${serviceName}: 配置验证失败`, 'warning');
                            }
                        }
                    } else {
                        displayResult('learningEngineResults', `❌ ${serviceName}: 服务获取失败`, 'error');
                    }
                } catch (error) {
                    displayResult('learningEngineResults', `❌ ${serviceName}: ${error.message}`, 'error');
                }
            });
        }

        // 测试工厂函数兼容性
        function testFactoryFunctionsCompatibility() {
            clearResults('factoryFunctionsResults');
            
            const factoryFunctions = [
                'getAppState', 'getLogger', 'getApiService', 'getGeminiService', 'getUtils',
                'getImageUploadManager', 'getCurrencyConverter', 'getMultiOrderManager',
                'getOrderHistoryManager', 'getLearningConfig', 'getCorrectionInterface'
            ];

            factoryFunctions.forEach(funcName => {
                try {
                    if (typeof window[funcName] === 'function') {
                        const service = window[funcName]();
                        if (service) {
                            displayResult('factoryFunctionsResults', `✅ ${funcName}(): 工厂函数正常工作`, 'success');
                        } else {
                            displayResult('factoryFunctionsResults', `❌ ${funcName}(): 返回null或undefined`, 'error');
                        }
                    } else {
                        displayResult('factoryFunctionsResults', `❌ ${funcName}: 工厂函数不存在`, 'error');
                    }
                } catch (error) {
                    displayResult('factoryFunctionsResults', `❌ ${funcName}: ${error.message}`, 'error');
                }
            });
        }

        // 运行全面系统测试
        function runFullSystemTest() {
            clearResults('fullSystemResults');
            
            displayResult('fullSystemResults', '🚀 开始全面系统测试...', 'info');
            
            // 测试1: 核心架构
            testCoreArchitecture();
            
            // 测试2: 服务定位器
            testServiceLocator();
            
            // 测试3: 依赖容器
            testDependencyContainer();
            
            // 测试4: 核心服务
            testCoreServices();
            
            // 测试5: 学习引擎
            testLearningEngineServices();
            
            // 测试6: 工厂函数兼容性
            testFactoryFunctionsCompatibility();
            
            // 生成测试报告
            setTimeout(() => {
                const successCount = document.querySelectorAll('.test-result.success').length;
                const errorCount = document.querySelectorAll('.test-result.error').length;
                const warningCount = document.querySelectorAll('.test-result.warning').length;
                
                displayResult('fullSystemResults', 
                    `🎯 测试完成！\n✅ 成功: ${successCount}\n❌ 失败: ${errorCount}\n⚠️ 警告: ${warningCount}`, 
                    errorCount > 0 ? 'error' : successCount > 0 ? 'success' : 'warning'
                );
            }, 1000);
        }
    </script>
</body>
</html>