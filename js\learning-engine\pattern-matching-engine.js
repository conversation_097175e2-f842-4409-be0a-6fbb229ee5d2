/**
 * 智能学习型格式预处理引擎 - 模式匹配引擎
 * 负责文本模式匹配、相似度计算和上下文匹配
 * 为学习规则生成提供模式识别基础
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 容器优先的服务访问函数
    function getServiceFromContainer(name) {
        return window.OTA.container ? window.OTA.container.get(name) : getService(name);
    }

    // 获取依赖模块 - 使用容器优先的服务访问
    function getLearningConfig() {
        return getServiceFromContainer('learningConfig');
    }

    function getLogger() {
        return getServiceFromContainer('logger');
    }

    // 服务定位器函数 - 向后兼容
    function getService(serviceName) {
        const serviceMap = {
            'learningConfig': () => window.OTA.learningConfig || window.learningConfig,
            'logger': () => window.OTA.logger || window.logger
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 模式匹配引擎类
     * 提供多种文本匹配和相似度计算算法
     */
    class PatternMatchingEngine {
        constructor() {
            this.config = getLearningConfig();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            this.matchingConfig = this.config.get('patternMatching');
            
            // 缓存系统
            this.patternCache = new Map();
            this.similarityCache = new Map();
            
            // 预编译的正则表达式模式
            this.commonPatterns = this.initializeCommonPatterns();
            
            this.initialize();
        }

        /**
         * 初始化模式匹配引擎
         */
        initialize() {
            try {
                this.logger?.log('模式匹配引擎初始化完成', 'info', {
                    version: this.version,
                    cacheEnabled: this.matchingConfig.cache.enabled,
                    algorithms: this.matchingConfig.algorithms
                });
            } catch (error) {
                this.logger?.logError('模式匹配引擎初始化失败', error);
            }
        }

        /**
         * 初始化常用模式
         */
        initializeCommonPatterns() {
            return {
                // 日期模式
                datePatterns: [
                    /\d{4}-\d{2}-\d{2}/g,           // YYYY-MM-DD
                    /\d{2}\/\d{2}\/\d{4}/g,         // MM/DD/YYYY
                    /\d{2}-\d{2}-\d{4}/g            // DD-MM-YYYY
                ],
                
                // 时间模式
                timePatterns: [
                    /\d{2}:\d{2}/g,                 // HH:MM
                    /\d{1,2}:\d{2}\s?(AM|PM)/gi     // H:MM AM/PM
                ],
                
                // 邮箱模式
                emailPatterns: [
                    /[\w\.-]+@[\w\.-]+\.\w+/g
                ],
                
                // 电话模式
                phonePatterns: [
                    /\+?[\d\s\-\(\)]{7,}/g
                ],
                
                // 航班号模式
                flightPatterns: [
                    /[A-Z]{2,3}\s?\d{3,4}/gi
                ],
                
                // 价格模式
                pricePatterns: [
                    /[A-Z]{3}\s?\d+\.?\d*/gi,       // USD 150.50
                    /\$\d+\.?\d*/g,                 // $150.50
                    /\d+\.?\d*\s?[A-Z]{3}/gi        // 150.50 USD
                ],
                
                // 数字模式
                numberPatterns: [
                    /\d+/g
                ]
            };
        }

        /**
         * 执行模式匹配
         * @param {string} text - 待匹配文本
         * @param {string|RegExp} pattern - 匹配模式
         * @param {Object} options - 匹配选项
         * @returns {Object} 匹配结果
         */
        matchPattern(text, pattern, options = {}) {
            try {
                const cacheKey = `${text}_${pattern.toString()}`;
                
                // 检查缓存
                if (this.matchingConfig.cache.enabled && this.patternCache.has(cacheKey)) {
                    return this.patternCache.get(cacheKey);
                }

                let matches = [];
                let confidence = 0;

                if (pattern instanceof RegExp) {
                    matches = this.regexMatch(text, pattern, options);
                } else if (typeof pattern === 'string') {
                    matches = this.stringMatch(text, pattern, options);
                }

                // 计算匹配置信度
                confidence = this.calculateMatchConfidence(text, matches, options);

                const result = {
                    matches: matches,
                    confidence: confidence,
                    matchCount: matches.length,
                    hasMatch: matches.length > 0,
                    timestamp: new Date().toISOString()
                };

                // 缓存结果
                if (this.matchingConfig.cache.enabled) {
                    this.cacheResult(cacheKey, result);
                }

                return result;

            } catch (error) {
                this.logger?.logError('模式匹配失败', error);
                return this.createEmptyResult();
            }
        }

        /**
         * 正则表达式匹配
         */
        regexMatch(text, regex, options) {
            const matches = [];
            let match;

            while ((match = regex.exec(text)) !== null) {
                matches.push({
                    value: match[0],
                    index: match.index,
                    groups: match.slice(1),
                    length: match[0].length
                });

                // 防止无限循环
                if (!regex.global) break;
            }

            return matches;
        }

        /**
         * 字符串匹配
         */
        stringMatch(text, pattern, options) {
            const matches = [];
            const caseSensitive = options.caseSensitive || this.matchingConfig.similarity.caseSensitive;
            
            const searchText = caseSensitive ? text : text.toLowerCase();
            const searchPattern = caseSensitive ? pattern : pattern.toLowerCase();

            let index = 0;
            while ((index = searchText.indexOf(searchPattern, index)) !== -1) {
                matches.push({
                    value: text.substr(index, pattern.length),
                    index: index,
                    length: pattern.length
                });
                index += pattern.length;
            }

            return matches;
        }

        /**
         * 计算文本相似度
         * @param {string} text1 - 文本1
         * @param {string} text2 - 文本2
         * @param {string} algorithm - 算法类型
         * @returns {number} 相似度分数 (0-1)
         */
        calculateSimilarity(text1, text2, algorithm = 'levenshtein') {
            try {
                const cacheKey = `${text1}_${text2}_${algorithm}`;
                
                // 检查缓存
                if (this.matchingConfig.cache.enabled && this.similarityCache.has(cacheKey)) {
                    return this.similarityCache.get(cacheKey);
                }

                let similarity = 0;

                switch (algorithm) {
                    case 'levenshtein':
                        similarity = this.levenshteinSimilarity(text1, text2);
                        break;
                    case 'jaccard':
                        similarity = this.jaccardSimilarity(text1, text2);
                        break;
                    case 'cosine':
                        similarity = this.cosineSimilarity(text1, text2);
                        break;
                    default:
                        similarity = this.levenshteinSimilarity(text1, text2);
                }

                // 缓存结果
                if (this.matchingConfig.cache.enabled) {
                    this.similarityCache.set(cacheKey, similarity);
                }

                return similarity;

            } catch (error) {
                this.logger?.logError('相似度计算失败', error);
                return 0;
            }
        }

        /**
         * Levenshtein距离相似度
         */
        levenshteinSimilarity(str1, str2) {
            const distance = this.levenshteinDistance(str1, str2);
            const maxLength = Math.max(str1.length, str2.length);
            return maxLength === 0 ? 1 : 1 - (distance / maxLength);
        }

        /**
         * 计算Levenshtein距离
         */
        levenshteinDistance(str1, str2) {
            const matrix = [];

            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }

            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }

            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j] + 1
                        );
                    }
                }
            }

            return matrix[str2.length][str1.length];
        }

        /**
         * Jaccard相似度
         */
        jaccardSimilarity(str1, str2) {
            const set1 = new Set(str1.toLowerCase().split(''));
            const set2 = new Set(str2.toLowerCase().split(''));
            
            const intersection = new Set([...set1].filter(x => set2.has(x)));
            const union = new Set([...set1, ...set2]);
            
            return union.size === 0 ? 0 : intersection.size / union.size;
        }

        /**
         * 余弦相似度
         */
        cosineSimilarity(str1, str2) {
            const vector1 = this.getCharVector(str1);
            const vector2 = this.getCharVector(str2);
            
            const dotProduct = this.dotProduct(vector1, vector2);
            const magnitude1 = this.magnitude(vector1);
            const magnitude2 = this.magnitude(vector2);
            
            return (magnitude1 * magnitude2) === 0 ? 0 : dotProduct / (magnitude1 * magnitude2);
        }

        /**
         * 获取字符向量
         */
        getCharVector(str) {
            const vector = {};
            for (const char of str.toLowerCase()) {
                vector[char] = (vector[char] || 0) + 1;
            }
            return vector;
        }

        /**
         * 计算向量点积
         */
        dotProduct(vec1, vec2) {
            let product = 0;
            for (const key in vec1) {
                if (vec2[key]) {
                    product += vec1[key] * vec2[key];
                }
            }
            return product;
        }

        /**
         * 计算向量模长
         */
        magnitude(vector) {
            let sum = 0;
            for (const key in vector) {
                sum += vector[key] * vector[key];
            }
            return Math.sqrt(sum);
        }

        /**
         * 上下文匹配
         * @param {string} text - 待匹配文本
         * @param {Object} context - 上下文信息
         * @returns {Object} 上下文匹配结果
         */
        contextMatch(text, context) {
            try {
                const results = {
                    fieldMatches: {},
                    patternMatches: {},
                    contextScore: 0,
                    suggestions: []
                };

                // 基于字段类型进行上下文匹配
                if (context.fieldType) {
                    results.fieldMatches = this.matchByFieldType(text, context.fieldType);
                }

                // 基于历史模式进行匹配
                if (context.historicalPatterns) {
                    results.patternMatches = this.matchHistoricalPatterns(text, context.historicalPatterns);
                }

                // 计算上下文分数
                results.contextScore = this.calculateContextScore(results);

                // 生成建议
                results.suggestions = this.generateContextSuggestions(results);

                return results;

            } catch (error) {
                this.logger?.logError('上下文匹配失败', error);
                return { fieldMatches: {}, patternMatches: {}, contextScore: 0, suggestions: [] };
            }
        }

        /**
         * 根据字段类型匹配
         */
        matchByFieldType(text, fieldType) {
            const matches = {};
            
            switch (fieldType) {
                case 'date':
                    matches.dates = this.matchMultiplePatterns(text, this.commonPatterns.datePatterns);
                    break;
                case 'time':
                    matches.times = this.matchMultiplePatterns(text, this.commonPatterns.timePatterns);
                    break;
                case 'email':
                    matches.emails = this.matchMultiplePatterns(text, this.commonPatterns.emailPatterns);
                    break;
                case 'contact':
                    matches.phones = this.matchMultiplePatterns(text, this.commonPatterns.phonePatterns);
                    break;
                case 'flight':
                    matches.flights = this.matchMultiplePatterns(text, this.commonPatterns.flightPatterns);
                    break;
                case 'price':
                    matches.prices = this.matchMultiplePatterns(text, this.commonPatterns.pricePatterns);
                    break;
                case 'number':
                    matches.numbers = this.matchMultiplePatterns(text, this.commonPatterns.numberPatterns);
                    break;
            }

            return matches;
        }

        /**
         * 匹配多个模式
         */
        matchMultiplePatterns(text, patterns) {
            const allMatches = [];
            
            patterns.forEach(pattern => {
                const result = this.matchPattern(text, pattern);
                if (result.hasMatch) {
                    allMatches.push(...result.matches);
                }
            });

            return allMatches;
        }

        /**
         * 匹配历史模式
         */
        matchHistoricalPatterns(text, historicalPatterns) {
            const matches = {};
            
            historicalPatterns.forEach((pattern, index) => {
                const similarity = this.calculateSimilarity(text, pattern.example);
                if (similarity > this.matchingConfig.similarity.threshold) {
                    matches[`pattern_${index}`] = {
                        similarity: similarity,
                        pattern: pattern,
                        confidence: similarity
                    };
                }
            });

            return matches;
        }

        /**
         * 计算匹配置信度
         */
        calculateMatchConfidence(text, matches, options) {
            if (matches.length === 0) return 0;

            let confidence = 0.5; // 基础置信度

            // 基于匹配数量调整
            confidence += Math.min(0.3, matches.length * 0.1);

            // 基于匹配覆盖率调整
            const totalMatchLength = matches.reduce((sum, match) => sum + match.length, 0);
            const coverageRatio = totalMatchLength / text.length;
            confidence += coverageRatio * 0.2;

            return Math.min(1.0, confidence);
        }

        /**
         * 计算上下文分数
         */
        calculateContextScore(results) {
            let score = 0;

            // 字段匹配分数
            const fieldMatchCount = Object.keys(results.fieldMatches).length;
            score += fieldMatchCount * 0.3;

            // 模式匹配分数
            const patternMatchCount = Object.keys(results.patternMatches).length;
            score += patternMatchCount * 0.4;

            return Math.min(1.0, score);
        }

        /**
         * 生成上下文建议
         */
        generateContextSuggestions(results) {
            const suggestions = [];

            if (results.contextScore < 0.5) {
                suggestions.push('建议提供更多上下文信息以提高匹配准确性');
            }

            if (Object.keys(results.fieldMatches).length === 0) {
                suggestions.push('未找到预期的字段模式，请检查输入格式');
            }

            return suggestions;
        }

        /**
         * 缓存结果
         */
        cacheResult(key, result) {
            if (this.patternCache.size >= this.matchingConfig.cache.maxSize) {
                // 删除最旧的缓存项
                const firstKey = this.patternCache.keys().next().value;
                this.patternCache.delete(firstKey);
            }
            
            this.patternCache.set(key, result);
        }

        /**
         * 创建空结果
         */
        createEmptyResult() {
            return {
                matches: [],
                confidence: 0,
                matchCount: 0,
                hasMatch: false,
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 清理缓存
         */
        clearCache() {
            this.patternCache.clear();
            this.similarityCache.clear();
            this.logger?.log('模式匹配缓存已清理', 'info');
        }

        /**
         * 获取缓存统计
         */
        getCacheStats() {
            return {
                patternCacheSize: this.patternCache.size,
                similarityCacheSize: this.similarityCache.size,
                maxCacheSize: this.matchingConfig.cache.maxSize
            };
        }
    }

    // 创建全局实例
    const patternMatchingEngine = new PatternMatchingEngine();

    // 导出到全局命名空间
    window.OTA.patternMatchingEngine = patternMatchingEngine;
    window.patternMatchingEngine = patternMatchingEngine; // 向后兼容

    // 工厂函数
    window.getPatternMatchingEngine = function() {
        return window.OTA.patternMatchingEngine || window.patternMatchingEngine;
    };

    console.log('模式匹配引擎加载完成', {
        version: patternMatchingEngine.version,
        algorithms: patternMatchingEngine.matchingConfig.algorithms,
        cacheEnabled: patternMatchingEngine.matchingConfig.cache.enabled
    });

})();
