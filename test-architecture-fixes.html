<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统架构修复验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .test-section { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 5px 0; padding: 8px; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .manager-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 15px 0; }
        .manager-card { padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa; }
        h1, h2, h3 { color: #333; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OTA系统架构修复验证</h1>
        <p>验证命名空间暴露、初始化时序、事件系统一致性等架构问题的修复效果</p>
        
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearAllResults()">🧹 清空结果</button>
    </div>

    <div class="container">
        <h2>1. 命名空间暴露检查</h2>
        <button onclick="testNamespaceExposure()">检查命名空间暴露</button>
        <div id="namespaceResults"></div>
    </div>

    <div class="container">
        <h2>2. 依赖容器兼容性检查</h2>
        <button onclick="testDependencyContainer()">检查依赖容器</button>
        <div id="dependencyResults"></div>
    </div>

    <div class="container">
        <h2>3. 实例创建一致性检查</h2>
        <button onclick="testInstanceCreation()">检查实例创建</button>
        <div id="instanceResults"></div>
    </div>

    <div class="container">
        <h2>4. 向后兼容性检查</h2>
        <button onclick="testBackwardCompatibility()">检查向后兼容性</button>
        <div id="compatibilityResults"></div>
    </div>

    <!-- 加载核心脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/paging-service-manager.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        // 测试配置
        const MANAGERS_TO_TEST = [
            'i18nManager',
            'imageUploadManager', 
            'currencyConverter',
            'orderHistoryManager',
            'pagingServiceManager',
            'multiOrderManager'
        ];

        const CORE_SERVICES = [
            'appState',
            'logger',
            'utils',
            'apiService',
            'geminiService'
        ];

        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
        }

        function clear(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function clearAllResults() {
            ['namespaceResults', 'dependencyResults', 'instanceResults', 'compatibilityResults'].forEach(clear);
        }

        function testNamespaceExposure() {
            clear('namespaceResults');
            log('namespaceResults', '开始检查命名空间暴露...', 'info');

            // 检查OTA命名空间存在
            if (typeof window.OTA === 'object') {
                log('namespaceResults', '✅ window.OTA 命名空间存在', 'success');
            } else {
                log('namespaceResults', '❌ window.OTA 命名空间不存在', 'error');
                return;
            }

            // 检查所有管理器的OTA命名空间暴露
            MANAGERS_TO_TEST.forEach(managerName => {
                const otaManager = window.OTA[managerName];
                const otaFactory = window.OTA[`get${managerName.charAt(0).toUpperCase() + managerName.slice(1)}`];
                const otaClass = window.OTA[managerName.charAt(0).toUpperCase() + managerName.slice(1)];

                if (otaManager && typeof otaManager === 'object') {
                    log('namespaceResults', `✅ window.OTA.${managerName} 实例存在`, 'success');
                } else {
                    log('namespaceResults', `❌ window.OTA.${managerName} 实例不存在`, 'error');
                }

                if (otaFactory && typeof otaFactory === 'function') {
                    log('namespaceResults', `✅ window.OTA.get${managerName.charAt(0).toUpperCase() + managerName.slice(1)} 工厂函数存在`, 'success');
                } else {
                    log('namespaceResults', `❌ window.OTA.get${managerName.charAt(0).toUpperCase() + managerName.slice(1)} 工厂函数不存在`, 'error');
                }

                if (otaClass && typeof otaClass === 'function') {
                    log('namespaceResults', `✅ window.OTA.${managerName.charAt(0).toUpperCase() + managerName.slice(1)} 类存在`, 'success');
                } else {
                    log('namespaceResults', `❌ window.OTA.${managerName.charAt(0).toUpperCase() + managerName.slice(1)} 类不存在`, 'error');
                }
            });

            // 检查核心服务
            CORE_SERVICES.forEach(serviceName => {
                const otaService = window.OTA[serviceName];
                if (otaService) {
                    log('namespaceResults', `✅ window.OTA.${serviceName} 存在`, 'success');
                } else {
                    log('namespaceResults', `❌ window.OTA.${serviceName} 不存在`, 'error');
                }
            });
        }

        function testDependencyContainer() {
            clear('dependencyResults');
            log('dependencyResults', '开始检查依赖容器兼容性...', 'info');

            if (!window.OTA.container) {
                log('dependencyResults', '❌ 依赖容器不存在', 'error');
                return;
            }

            // 测试依赖注册
            try {
                const bootstrap = new window.OTA.ApplicationBootstrap();
                log('dependencyResults', '✅ 应用启动器创建成功', 'success');

                // 检查依赖注册
                const allServices = [...MANAGERS_TO_TEST, ...CORE_SERVICES];
                allServices.forEach(serviceName => {
                    try {
                        if (window.OTA.container.has(serviceName)) {
                            log('dependencyResults', `✅ ${serviceName} 已在依赖容器中注册`, 'success');
                        } else {
                            log('dependencyResults', `⚠️ ${serviceName} 未在依赖容器中注册`, 'warning');
                        }
                    } catch (error) {
                        log('dependencyResults', `❌ 检查 ${serviceName} 时出错: ${error.message}`, 'error');
                    }
                });

            } catch (error) {
                log('dependencyResults', `❌ 依赖容器测试失败: ${error.message}`, 'error');
            }
        }

        function testInstanceCreation() {
            clear('instanceResults');
            log('instanceResults', '开始检查实例创建一致性...', 'info');

            MANAGERS_TO_TEST.forEach(managerName => {
                try {
                    const factoryName = `get${managerName.charAt(0).toUpperCase() + managerName.slice(1)}`;
                    const globalFactory = window[factoryName];
                    const otaFactory = window.OTA[factoryName];
                    const otaInstance = window.OTA[managerName];
                    const globalInstance = window[managerName];

                    // 检查工厂函数返回的实例是否一致
                    if (globalFactory && otaFactory) {
                        const instance1 = globalFactory();
                        const instance2 = otaFactory();
                        
                        if (instance1 === instance2) {
                            log('instanceResults', `✅ ${managerName} 工厂函数返回相同实例`, 'success');
                        } else {
                            log('instanceResults', `❌ ${managerName} 工厂函数返回不同实例`, 'error');
                        }

                        // 检查实例是否与暴露的实例一致
                        if (instance1 === otaInstance && instance1 === globalInstance) {
                            log('instanceResults', `✅ ${managerName} 所有实例引用一致`, 'success');
                        } else {
                            log('instanceResults', `❌ ${managerName} 实例引用不一致`, 'error');
                        }
                    } else {
                        log('instanceResults', `❌ ${managerName} 工厂函数不完整`, 'error');
                    }

                } catch (error) {
                    log('instanceResults', `❌ 测试 ${managerName} 实例创建时出错: ${error.message}`, 'error');
                }
            });
        }

        function testBackwardCompatibility() {
            clear('compatibilityResults');
            log('compatibilityResults', '开始检查向后兼容性...', 'info');

            // 检查全局暴露
            MANAGERS_TO_TEST.forEach(managerName => {
                const factoryName = `get${managerName.charAt(0).toUpperCase() + managerName.slice(1)}`;
                const className = managerName.charAt(0).toUpperCase() + managerName.slice(1);

                // 检查全局工厂函数
                if (typeof window[factoryName] === 'function') {
                    log('compatibilityResults', `✅ window.${factoryName} 全局工厂函数存在`, 'success');
                } else {
                    log('compatibilityResults', `❌ window.${factoryName} 全局工厂函数不存在`, 'error');
                }

                // 检查全局类
                if (typeof window[className] === 'function') {
                    log('compatibilityResults', `✅ window.${className} 全局类存在`, 'success');
                } else {
                    log('compatibilityResults', `❌ window.${className} 全局类不存在`, 'error');
                }

                // 检查全局实例
                if (typeof window[managerName] === 'object') {
                    log('compatibilityResults', `✅ window.${managerName} 全局实例存在`, 'success');
                } else {
                    log('compatibilityResults', `❌ window.${managerName} 全局实例不存在`, 'error');
                }
            });

            // 检查核心服务的向后兼容性
            CORE_SERVICES.forEach(serviceName => {
                const factoryName = `get${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)}`;
                
                if (typeof window[factoryName] === 'function' || typeof window[serviceName] === 'object') {
                    log('compatibilityResults', `✅ ${serviceName} 向后兼容性良好`, 'success');
                } else {
                    log('compatibilityResults', `⚠️ ${serviceName} 向后兼容性可能有问题`, 'warning');
                }
            });
        }

        function runAllTests() {
            clearAllResults();
            setTimeout(() => testNamespaceExposure(), 100);
            setTimeout(() => testDependencyContainer(), 500);
            setTimeout(() => testInstanceCreation(), 1000);
            setTimeout(() => testBackwardCompatibility(), 1500);
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
