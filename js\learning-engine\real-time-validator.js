/**
 * 智能学习型格式预处理引擎 - 实时验证器
 * 提供实时输入验证、智能错误预防和纠正建议
 * 集成学习系统实现动态验证规则
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-18
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 容器优先的服务访问函数
    function getServiceFromContainer(name) {
        return window.OTA.container ? window.OTA.container.get(name) : getService(name);
    }

    function getLearningConfig() {
        return getServiceFromContainer('learningConfig');
    }

    function getErrorClassificationSystem() {
        return getServiceFromContainer('errorClassificationSystem');
    }

    function getRuleGenerationEngine() {
        return getServiceFromContainer('ruleGenerationEngine');
    }

    function getPatternMatchingEngine() {
        return getServiceFromContainer('patternMatchingEngine');
    }

    function getLogger() {
        return getServiceFromContainer('logger');
    }

    // 服务定位器函数 - 向后兼容
    function getService(serviceName) {
        const serviceMap = {
            'learningConfig': () => window.OTA.learningConfig || window.learningConfig,
            'errorClassificationSystem': () => window.OTA.errorClassificationSystem || window.errorClassificationSystem,
            'ruleGenerationEngine': () => window.OTA.ruleGenerationEngine || window.ruleGenerationEngine,
            'patternMatchingEngine': () => window.OTA.patternMatchingEngine || window.patternMatchingEngine,
            'logger': () => window.OTA.logger || window.logger
        };
        
        const getter = serviceMap[serviceName];
        return getter ? getter() : null;
    }

    /**
     * 实时验证器类
     * 提供实时输入验证、错误预防和智能纠正建议
     */
    class RealTimeValidator {
        constructor() {
            this.config = getLearningConfig();
            this.errorClassifier = getErrorClassificationSystem();
            this.ruleEngine = getRuleGenerationEngine();
            this.patternMatcher = getPatternMatchingEngine();
            this.logger = getLogger();
            
            this.version = '1.0.0';
            
            // 验证配置
            this.validationConfig = {
                debounceDelay: 300, // 防抖延迟(ms)
                minLength: 2, // 最小验证长度
                validationTimeout: 1000, // 验证超时(ms)
                errorDisplayDelay: 2000, // 错误显示延迟(ms)
                confidenceThreshold: 0.8, // 建议置信度阈值
                autoCorrectConfidence: 0.95 // 自动纠正置信度阈值
            };
            
            // 验证规则缓存
            this.validationRules = new Map();
            this.errorPatterns = new Map();
            this.correctionCache = new Map();
            
            // 状态管理
            this.activeValidations = new Map();
            this.fieldErrors = new Map();
            this.validationStats = {
                totalValidations: 0,
                errorsDetected: 0,
                correctionsApplied: 0,
                falsePositives: 0
            };
            
            // 字段验证器映射
            this.fieldValidators = this.initializeFieldValidators();
            
            this.initialize();
        }

        /**
         * 初始化实时验证器
         */
        initialize() {
            try {
                this.loadValidationRules();
                this.setupFieldValidation();
                this.setupRealTimeMonitoring();
                
                this.logger?.log('实时验证器初始化完成', 'info', {
                    version: this.version,
                    debounceDelay: this.validationConfig.debounceDelay,
                    confidenceThreshold: this.validationConfig.confidenceThreshold
                });
            } catch (error) {
                this.logger?.logError('实时验证器初始化失败', error);
            }
        }

        /**
         * 初始化字段验证器
         */
        initializeFieldValidators() {
            return {
                customerName: {
                    rules: [
                        { type: 'required', message: '客户姓名不能为空' },
                        { type: 'pattern', pattern: /^[\u4e00-\u9fa5a-zA-Z\s]{2,50}$/, message: '请输入有效的姓名' }
                    ],
                    suggestions: [
                        { pattern: /^[\u4e00-\u9fa5]{2,4}$/, suggestion: '中文姓名格式正确' },
                        { pattern: /^[A-Za-z\s]{2,30}$/, suggestion: '英文姓名格式正确' }
                    ]
                },
                customerContact: {
                    rules: [
                        { type: 'required', message: '联系电话不能为空' },
                        { type: 'pattern', pattern: /^\+?[\d\s\-\(\)]{7,15}$/, message: '请输入有效的联系电话' }
                    ],
                    suggestions: [
                        { pattern: /^\d{11}$/, suggestion: '11位手机号格式' },
                        { pattern: /^\+\d{1,3}\d{7,10}$/, suggestion: '国际号码格式' }
                    ]
                },
                pickup: {
                    rules: [
                        { type: 'required', message: '上车地点不能为空' },
                        { type: 'minLength', min: 3, message: '地点名称至少3个字符' }
                    ],
                    suggestions: [
                        { pattern: /机场|airport/i, suggestion: '建议包含机场航站楼信息' },
                        { pattern: /酒店|hotel/i, suggestion: '建议包含酒店名称和地址' }
                    ]
                },
                dropoff: {
                    rules: [
                        { type: 'required', message: '下车地点不能为空' },
                        { type: 'minLength', min: 3, message: '地点名称至少3个字符' }
                    ],
                    suggestions: [
                        { pattern: /机场|airport/i, suggestion: '建议包含机场航站楼信息' },
                        { pattern: /酒店|hotel/i, suggestion: '建议包含酒店名称和地址' }
                    ]
                },
                pickupTime: {
                    rules: [
                        { type: 'required', message: '上车时间不能为空' },
                        { type: 'pattern', pattern: /^\d{2}:\d{2}$/, message: '时间格式应为HH:MM' }
                    ],
                    suggestions: [
                        { pattern: /^\d{2}:\d{2}$/, suggestion: '24小时制时间格式' }
                    ]
                },
                pickupDate: {
                    rules: [
                        { type: 'required', message: '上车日期不能为空' },
                        { type: 'futureDate', message: '日期必须在未来' }
                    ]
                },
                passengerCount: {
                    rules: [
                        { type: 'required', message: '乘客数量不能为空' },
                        { type: 'range', min: 1, max: 9, message: '乘客数量应在1-9之间' }
                    ],
                    suggestions: [
                        { pattern: /^[1-9]$/, suggestion: '标准乘客数量' },
                        { pattern: /^[1-4]$/, suggestion: '小型车辆建议' }
                    ]
                },
                otaPrice: {
                    rules: [
                        { type: 'required', message: '订单价格不能为空' },
                        { type: 'range', min: 1, max: 10000, message: '价格应在1-10000之间' }
                    ],
                    suggestions: [
                        { pattern: /^\d+$/, suggestion: '整数价格' },
                        { pattern: /^\d+\.\d{2}$/, suggestion: '精确到分的价格' }
                    ]
                }
            };
        }

        /**
         * 加载验证规则
         */
        loadValidationRules() {
            try {
                // 从错误分类系统加载规则
                const rules = this.errorClassifier.getValidationRules();
                if (rules) {
                    Object.entries(rules).forEach(([fieldName, fieldRules]) => {
                        this.validationRules.set(fieldName, fieldRules);
                    });
                }

                // 从规则引擎加载智能规则
                const smartRules = this.ruleEngine.getSmartValidationRules();
                if (smartRules) {
                    Object.entries(smartRules).forEach(([fieldName, rules]) => {
                        const existing = this.validationRules.get(fieldName) || {};
                        this.validationRules.set(fieldName, { ...existing, ...rules });
                    });
                }

            } catch (error) {
                this.logger?.logError('加载验证规则失败', error);
            }
        }

        /**
         * 设置字段验证
         */
        setupFieldValidation() {
            // 监听所有表单字段
            document.addEventListener('input', (e) => {
                if (e.target.matches('[data-field-name]')) {
                    this.handleFieldInput(e.target);
                }
            });

            document.addEventListener('blur', (e) => {
                if (e.target.matches('[data-field-name]')) {
                    this.handleFieldBlur(e.target);
                }
            });

            document.addEventListener('focus', (e) => {
                if (e.target.matches('[data-field-name]')) {
                    this.showFieldHints(e.target);
                }
            });
        }

        /**
         * 设置实时监控
         */
        setupRealTimeMonitoring() {
            // 监听表单提交
            document.addEventListener('submit', (e) => {
                if (e.target.matches('#orderForm')) {
                    this.validateBeforeSubmission(e.target);
                }
            });
        }

        /**
         * 处理字段输入
         */
        handleFieldInput(inputElement) {
            const fieldName = inputElement.dataset.fieldName;
            const value = inputElement.value;

            if (!value || value.length < this.validationConfig.minLength) {
                this.clearFieldErrors(fieldName);
                return;
            }

            // 防抖处理
            clearTimeout(this.activeValidations.get(fieldName));
            const timeout = setTimeout(() => {
                this.validateField(fieldName, value, inputElement);
            }, this.validationConfig.debounceDelay);
            
            this.activeValidations.set(fieldName, timeout);
        }

        /**
         * 处理字段失去焦点
         */
        handleFieldBlur(inputElement) {
            const fieldName = inputElement.dataset.fieldName;
            const value = inputElement.value;

            // 清除防抖定时器
            clearTimeout(this.activeValidations.get(fieldName));
            this.activeValidations.delete(fieldName);

            // 立即验证
            this.validateField(fieldName, value, inputElement);
        }

        /**
         * 验证单个字段
         */
        async validateField(fieldName, value, inputElement) {
            try {
                this.validationStats.totalValidations++;

                const rules = this.getFieldRules(fieldName);
                const result = await this.performValidation(rules, value, fieldName);

                if (result.isValid) {
                    this.clearFieldErrors(fieldName);
                    this.showSuccess(inputElement, result.suggestion);
                } else {
                    this.validationStats.errorsDetected++;
                    this.showError(inputElement, result.error, result.suggestions);
                    this.learnFromError(fieldName, value, result.error);
                }

                return result;

            } catch (error) {
                this.logger?.logError('字段验证失败', error);
                return { isValid: true, error: null };
            }
        }

        /**
         * 获取字段验证规则
         */
        getFieldRules(fieldName) {
            const baseRules = this.fieldValidators[fieldName] || {};
            const learnedRules = this.validationRules.get(fieldName) || {};
            
            return { ...baseRules, ...learnedRules };
        }

        /**
         * 执行验证
         */
        async performValidation(rules, value, fieldName) {
            try {
                const errors = [];
                const suggestions = [];

                // 应用标准规则
                if (rules.rules) {
                    for (const rule of rules.rules) {
                        const result = this.applyRule(rule, value);
                        if (!result.isValid) {
                            errors.push(result.error);
                        }
                    }
                }

                // 应用智能规则
                if (rules.smartRules) {
                    for (const rule of rules.smartRules) {
                        const result = await this.applySmartRule(rule, value, fieldName);
                        if (!result.isValid) {
                            errors.push(result.error);
                        } else if (result.suggestion) {
                            suggestions.push(result.suggestion);
                        }
                    }
                }

                // 检查错误模式
                const patternResult = await this.checkErrorPatterns(value, fieldName);
                if (patternResult.correction) {
                    suggestions.push(patternResult.correction);
                }

                return {
                    isValid: errors.length === 0,
                    error: errors[0] || null,
                    suggestions: suggestions,
                    suggestion: suggestions[0] || null
                };

            } catch (error) {
                this.logger?.logError('执行验证失败', error);
                return { isValid: true, error: null };
            }
        }

        /**
         * 应用验证规则
         */
        applyRule(rule, value) {
            try {
                switch (rule.type) {
                    case 'required':
                        return { isValid: value.trim().length > 0, error: rule.message };
                    
                    case 'pattern':
                        return { isValid: rule.pattern.test(value), error: rule.message };
                    
                    case 'minLength':
                        return { isValid: value.length >= rule.min, error: rule.message };
                    
                    case 'range':
                        const num = parseFloat(value);
                        return { isValid: !isNaN(num) && num >= rule.min && num <= rule.max, error: rule.message };
                    
                    case 'futureDate':
                        const date = new Date(value);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return { isValid: date >= today, error: rule.message };
                    
                    default:
                        return { isValid: true, error: null };
                }
            } catch (error) {
                this.logger?.logError('应用验证规则失败', error);
                return { isValid: true, error: null };
            }
        }

        /**
         * 应用智能规则
         */
        async applySmartRule(rule, value, fieldName) {
            try {
                if (rule.type === 'patternMatching') {
                    const match = await this.patternMatcher.matchPattern(value, rule.pattern);
                    if (match.hasMatch && match.confidence >= this.validationConfig.confidenceThreshold) {
                        return { isValid: true, suggestion: rule.suggestion };
                    }
                }

                return { isValid: true, suggestion: null };

            } catch (error) {
                this.logger?.logError('应用智能规则失败', error);
                return { isValid: true, suggestion: null };
            }
        }

        /**
         * 检查错误模式
         */
        async checkErrorPatterns(value, fieldName) {
            try {
                const cacheKey = `${fieldName}_${value}`;
                
                // 检查缓存
                if (this.correctionCache.has(cacheKey)) {
                    return this.correctionCache.get(cacheKey);
                }

                const patterns = this.errorPatterns.get(fieldName) || [];
                for (const pattern of patterns) {
                    const match = await this.patternMatcher.matchPattern(value, pattern.pattern);
                    if (match.hasMatch) {
                        const result = {
                            correction: pattern.correction,
                            confidence: match.confidence
                        };
                        
                        // 缓存结果
                        this.correctionCache.set(cacheKey, result);
                        return result;
                    }
                }

                return { correction: null, confidence: 0 };

            } catch (error) {
                this.logger?.logError('检查错误模式失败', error);
                return { correction: null, confidence: 0 };
            }
        }

        /**
         * 显示错误信息
         */
        showError(inputElement, error, suggestions) {
            const fieldName = inputElement.dataset.fieldName;
            
            // 移除旧的错误显示
            this.clearFieldErrors(fieldName);

            // 创建错误显示元素
            const errorElement = document.createElement('div');
            errorElement.className = 'validation-error';
            errorElement.style.cssText = `
                color: #ff4444;
                font-size: 12px;
                margin-top: 4px;
                background: #ffebee;
                padding: 4px 8px;
                border-radius: 2px;
                border-left: 3px solid #ff4444;
            `;
            errorElement.textContent = error;

            // 添加建议
            if (suggestions && suggestions.length > 0) {
                const suggestionsElement = document.createElement('div');
                suggestionsElement.className = 'validation-suggestions';
                suggestionsElement.style.cssText = `
                    color: #666;
                    font-size: 11px;
                    margin-top: 2px;
                    background: #f5f5f5;
                    padding: 2px 6px;
                    border-radius: 2px;
                `;
                suggestionsElement.textContent = `建议: ${suggestions.join(', ')}`;
                errorElement.appendChild(suggestionsElement);
            }

            // 插入到输入框下方
            inputElement.parentNode.appendChild(errorElement);

            // 添加视觉反馈
            inputElement.style.borderColor = '#ff4444';
            inputElement.style.backgroundColor = '#ffebee';

            // 记录错误
            this.fieldErrors.set(fieldName, error);
        }

        /**
         * 显示成功信息
         */
        showSuccess(inputElement, suggestion) {
            const fieldName = inputElement.dataset.fieldName;
            
            // 移除错误样式
            inputElement.style.borderColor = '#4caf50';
            inputElement.style.backgroundColor = '#e8f5e8';

            // 如果有建议，显示提示
            if (suggestion) {
                // 短暂显示建议提示
                setTimeout(() => {
                    inputElement.style.borderColor = '';
                    inputElement.style.backgroundColor = '';
                }, 2000);
            }

            this.fieldErrors.delete(fieldName);
        }

        /**
         * 清除字段错误
         */
        clearFieldErrors(fieldName) {
            const inputElement = document.querySelector(`[data-field-name="${fieldName}"]`);
            if (inputElement) {
                const errorElement = inputElement.parentNode.querySelector('.validation-error');
                if (errorElement) {
                    errorElement.remove();
                }

                // 重置样式
                inputElement.style.borderColor = '';
                inputElement.style.backgroundColor = '';
            }

            this.fieldErrors.delete(fieldName);
        }

        /**
         * 显示字段提示
         */
        showFieldHints(inputElement) {
            const fieldName = inputElement.dataset.fieldName;
            const rules = this.getFieldRules(fieldName);
            
            if (rules.suggestions && rules.suggestions.length > 0) {
                // 显示提示信息
                const hintElement = document.createElement('div');
                hintElement.className = 'field-hints';
                hintElement.style.cssText = `
                    color: #666;
                    font-size: 11px;
                    margin-top: 2px;
                    background: #f0f0f0;
                    padding: 4px 8px;
                    border-radius: 2px;
                `;
                hintElement.textContent = `提示: ${rules.suggestions[0].suggestion}`;
                
                inputElement.parentNode.appendChild(hintElement);

                // 自动清除提示
                setTimeout(() => {
                    const hint = inputElement.parentNode.querySelector('.field-hints');
                    if (hint) hint.remove();
                }, 5000);
            }
        }

        /**
         * 从错误中学习
         */
        learnFromError(fieldName, value, error) {
            try {
                // 记录错误模式
                this.errorClassifier.recordError({
                    fieldName,
                    value,
                    error,
                    timestamp: Date.now()
                });

                // 生成新的验证规则
                this.ruleEngine.generateValidationRule({
                    fieldName,
                    errorPattern: { value, error },
                    confidence: 0.8
                });

            } catch (error) {
                this.logger?.logError('学习错误失败', error);
            }
        }

        /**
         * 提交前验证
         */
        async validateBeforeSubmission(formElement) {
            try {
                const inputs = formElement.querySelectorAll('[data-field-name]');
                const validationPromises = [];

                inputs.forEach(input => {
                    const fieldName = input.dataset.fieldName;
                    const value = input.value;
                    validationPromises.push(this.validateField(fieldName, value, input));
                });

                const results = await Promise.all(validationPromises);
                const hasErrors = results.some(result => !result.isValid);

                if (hasErrors) {
                    // 聚焦到第一个错误字段
                    const firstError = results.find(result => !result.isValid);
                    const firstErrorInput = Array.from(inputs).find(
                        input => input.dataset.fieldName === firstError.fieldName
                    );
                    
                    if (firstErrorInput) {
                        firstErrorInput.focus();
                    }
                    
                    return false;
                }

                return true;

            } catch (error) {
                this.logger?.logError('提交前验证失败', error);
                return true; // 避免阻止提交
            }
        }

        /**
         * 获取验证统计
         */
        getValidationStats() {
            return {
                ...this.validationStats,
                activeErrors: this.fieldErrors.size,
                cacheSize: this.correctionCache.size,
                ruleCount: this.validationRules.size
            };
        }

        /**
         * 清理缓存
         */
        cleanupCache() {
            this.correctionCache.clear();
            this.activeValidations.clear();
        }
    }

    // 创建全局实例
    const realTimeValidator = new RealTimeValidator();

    // 导出到全局命名空间
    window.OTA.realTimeValidator = realTimeValidator;
    window.realTimeValidator = realTimeValidator; // 向后兼容

    // 工厂函数
    window.getRealTimeValidator = function() {
        return window.OTA.realTimeValidator || window.realTimeValidator;
    };

    console.log('实时验证器加载完成', {
        version: realTimeValidator.version,
        debounceDelay: realTimeValidator.validationConfig.debounceDelay,
        confidenceThreshold: realTimeValidator.validationConfig.confidenceThreshold
    });

})();