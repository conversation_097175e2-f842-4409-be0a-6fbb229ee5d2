<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整API服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-columns {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 完整API服务测试</h1>
        
        <div class="test-section">
            <h3>🚀 API服务初始化</h3>
            <button class="test-button" onclick="testApiInitialization()">测试API初始化</button>
            <div id="initResults"></div>
        </div>

        <div class="test-section">
            <h3>🔐 认证测试</h3>
            <div class="form-columns">
                <div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" id="loginEmail" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" id="loginPassword" value="Gomyhire@123456">
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="rememberMe"> 记住我
                        </label>
                    </div>
                </div>
                <div>
                    <button class="test-button" onclick="testLogin()">测试登录</button>
                    <button class="test-button" onclick="testHealthCheck()">健康检查</button>
                    <button class="test-button" onclick="testApiStats()">API统计</button>
                </div>
            </div>
            <div id="authResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 数据管理测试</h3>
            <button class="test-button" onclick="testSystemData()">测试系统数据</button>
            <button class="test-button" onclick="testStaticData()">测试静态数据</button>
            <button class="test-button" onclick="testDataQueries()">测试数据查询</button>
            <div id="dataResults"></div>
        </div>

        <div class="test-section">
            <h3>🛠️ 智能功能测试</h3>
            <button class="test-button" onclick="testSmartRecommendations()">测试智能推荐</button>
            <button class="test-button" onclick="testDataValidation()">测试数据验证</button>
            <button class="test-button" onclick="testFieldMapping()">测试字段映射</button>
            <div id="smartResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 订单创建测试</h3>
            <div class="form-columns">
                <div>
                    <div class="form-group">
                        <label>客户姓名</label>
                        <input type="text" id="customerName" value="王小明">
                    </div>
                    <div class="form-group">
                        <label>客户电话</label>
                        <input type="text" id="customerContact" value="+60123456789">
                    </div>
                    <div class="form-group">
                        <label>客户邮箱</label>
                        <input type="email" id="customerEmail" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>OTA参考号</label>
                        <input type="text" id="otaReference" value="CD123456789">
                    </div>
                    <div class="form-group">
                        <label>接客地点</label>
                        <input type="text" id="pickup" value="KLIA2 Terminal">
                    </div>
                    <div class="form-group">
                        <label>目的地</label>
                        <input type="text" id="destination" value="Kuala Lumpur Sentral">
                    </div>
                </div>
                <div>
                    <div class="form-group">
                        <label>服务日期</label>
                        <input type="date" id="serviceDate" value="2025-07-20">
                    </div>
                    <div class="form-group">
                        <label>服务时间</label>
                        <input type="time" id="serviceTime" value="15:30">
                    </div>
                    <div class="form-group">
                        <label>乘客数量</label>
                        <input type="number" id="passengerCount" value="2" min="1" max="50">
                    </div>
                    <div class="form-group">
                        <label>行李数量</label>
                        <input type="number" id="luggageCount" value="3" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label>价格 (MYR)</label>
                        <input type="number" id="otaPrice" value="150.00" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>额外要求</label>
                        <textarea id="extraRequirement" placeholder="特殊要求或备注">需要中文司机</textarea>
                    </div>
                </div>
            </div>
            <div>
                <button class="test-button" onclick="testCreateOrder()">创建订单</button>
                <button class="test-button" onclick="testValidateOrder()">验证订单</button>
                <button class="test-button" onclick="testEnhanceOrder()">增强订单</button>
                <button class="test-button" onclick="testOrderSummary()">订单摘要</button>
            </div>
            <div id="orderResults"></div>
        </div>

        <div class="test-section">
            <h3>📦 批量处理测试</h3>
            <div class="form-group">
                <label>批量订单数据 (JSON格式)</label>
                <textarea id="batchOrderData" style="height: 200px;">[
    {
        "ota_reference_number": "CD001",
        "customer_name": "客户A",
        "pickup": "KLIA2",
        "destination": "KL Sentral",
        "passenger_number": 2
    },
    {
        "ota_reference_number": "CD002",
        "customer_name": "客户B",
        "pickup": "KL Sentral",
        "destination": "KLIA",
        "passenger_number": 3
    }
]</textarea>
            </div>
            <button class="test-button" onclick="testBatchProcessing()">批量处理</button>
            <div id="batchResults"></div>
        </div>

        <div class="test-section">
            <h3>🚨 错误处理测试</h3>
            <button class="test-button" onclick="testValidationErrors()">测试验证错误</button>
            <button class="test-button" onclick="testDuplicateError()">测试重复错误</button>
            <button class="test-button" onclick="testNetworkError()">测试网络错误</button>
            <div id="errorResults"></div>
        </div>

        <div class="test-section">
            <h3>📈 完整集成测试</h3>
            <button class="test-button" onclick="runFullTest()">运行完整测试</button>
            <div id="fullTestResults"></div>
        </div>
    </div>

    <!-- 加载依赖 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service-complete.js"></script>

    <script>
        // 测试工具函数
        function displayResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            element.appendChild(resultDiv);
        }

        function clearResults(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '';
        }

        // 等待系统初始化
        async function waitForSystem() {
            return new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    if (window.getCompleteApiService) {
                        clearInterval(checkInterval);
                        resolve(true);
                    }
                }, 100);
            });
        }

        // 测试API初始化
        async function testApiInitialization() {
            clearResults('initResults');
            displayResult('initResults', '🚀 开始API初始化测试...', 'info');
            
            try {
                await waitForSystem();
                
                const apiService = getCompleteApiService();
                
                const tests = [
                    { name: 'API服务实例', check: () => !!apiService },
                    { name: 'baseURL配置', check: () => apiService.baseURL === 'https://gomyhire.com.my/api' },
                    { name: '超时设置', check: () => apiService.timeout === 30000 },
                    { name: '端点配置', check: () => !!apiService.endpoints.createOrder },
                    { name: '静态数据', check: () => !!apiService.staticData.carTypes },
                    { name: '字段映射', check: () => !!apiService.fieldMapping },
                    { name: '验证规则', check: () => !!apiService.validationRules },
                    { name: '智能规则', check: () => !!apiService.intelligentRules }
                ];
                
                let passed = 0;
                tests.forEach(test => {
                    try {
                        if (test.check()) {
                            displayResult('initResults', `✅ ${test.name}: 正常`, 'success');
                            passed++;
                        } else {
                            displayResult('initResults', `❌ ${test.name}: 失败`, 'error');
                        }
                    } catch (error) {
                        displayResult('initResults', `❌ ${test.name}: ${error.message}`, 'error');
                    }
                });
                
                displayResult('initResults', `🎯 API初始化测试完成: ${passed}/${tests.length} 通过`, 
                    passed === tests.length ? 'success' : 'warning');
                
            } catch (error) {
                displayResult('initResults', `❌ API初始化测试失败: ${error.message}`, 'error');
            }
        }

        // 测试登录
        async function testLogin() {
            clearResults('authResults');
            displayResult('authResults', '🔐 开始登录测试...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const email = document.getElementById('loginEmail').value;
                const password = document.getElementById('loginPassword').value;
                const rememberMe = document.getElementById('rememberMe').checked;
                
                const result = await apiService.login(email, password, rememberMe);
                
                if (result.success) {
                    displayResult('authResults', `✅ 登录成功: ${result.user.email}`, 'success');
                    displayResult('authResults', `🔑 Token: ${result.token.substring(0, 20)}...`, 'info');
                } else {
                    displayResult('authResults', '❌ 登录失败', 'error');
                }
                
            } catch (error) {
                displayResult('authResults', `❌ 登录异常: ${error.message}`, 'error');
            }
        }

        // 测试健康检查
        async function testHealthCheck() {
            clearResults('authResults');
            displayResult('authResults', '🏥 开始健康检查...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const health = await apiService.healthCheck();
                
                if (health.healthy) {
                    displayResult('authResults', '✅ API服务健康', 'success');
                } else {
                    displayResult('authResults', `⚠️ API服务异常: ${health.error}`, 'warning');
                }
                
            } catch (error) {
                displayResult('authResults', `❌ 健康检查失败: ${error.message}`, 'error');
            }
        }

        // 测试API统计
        async function testApiStats() {
            clearResults('authResults');
            displayResult('authResults', '📊 获取API统计...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const stats = apiService.getApiStats();
                
                displayResult('authResults', `✅ API版本: ${stats.version}`, 'success');
                displayResult('authResults', `📋 字段总数: ${stats.fieldCount.total} (必填: ${stats.fieldCount.required})`, 'info');
                displayResult('authResults', `🔧 支持功能: ${stats.supportedFeatures.length}个`, 'info');
                
            } catch (error) {
                displayResult('authResults', `❌ 获取统计失败: ${error.message}`, 'error');
            }
        }

        // 测试系统数据
        async function testSystemData() {
            clearResults('dataResults');
            displayResult('dataResults', '📊 测试系统数据...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                
                const dataTypes = ['carTypes', 'backendUsers', 'subCategories', 'drivingRegions', 'languages'];
                
                dataTypes.forEach(dataType => {
                    const data = apiService.getSystemData(dataType);
                    displayResult('dataResults', `✅ ${dataType}: ${data.length}条记录`, 'success');
                });
                
            } catch (error) {
                displayResult('dataResults', `❌ 系统数据测试失败: ${error.message}`, 'error');
            }
        }

        // 测试智能推荐
        async function testSmartRecommendations() {
            clearResults('smartResults');
            displayResult('smartResults', '🛠️ 测试智能推荐...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                
                // 车型推荐
                const carType = apiService.recommendCarType(6);
                displayResult('smartResults', `🚗 6人推荐车型ID: ${carType}`, 'success');
                
                // 服务类型检测
                const serviceType = apiService.detectServiceType('KLIA2', 'KL Sentral');
                displayResult('smartResults', `🛫 服务类型: ${serviceType} (接机)`, 'success');
                
                // 语言检测
                const languages = apiService.getDefaultLanguagesArray('王小明');
                displayResult('smartResults', `🗣️ 语言推荐: ${JSON.stringify(languages)}`, 'success');
                
                // OTA平台检测
                const otaPlatform = apiService.detectOtaPlatform('CD123456789');
                displayResult('smartResults', `🏢 OTA平台: ${otaPlatform}`, 'success');
                
            } catch (error) {
                displayResult('smartResults', `❌ 智能推荐测试失败: ${error.message}`, 'error');
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                customer_name: document.getElementById('customerName').value,
                customer_contact: document.getElementById('customerContact').value,
                customer_email: document.getElementById('customerEmail').value,
                ota_reference_number: document.getElementById('otaReference').value,
                pickup: document.getElementById('pickup').value,
                destination: document.getElementById('destination').value,
                date: document.getElementById('serviceDate').value,
                time: document.getElementById('serviceTime').value,
                passenger_number: parseInt(document.getElementById('passengerCount').value),
                luggage_number: parseInt(document.getElementById('luggageCount').value),
                ota_price: parseFloat(document.getElementById('otaPrice').value),
                extra_requirement: document.getElementById('extraRequirement').value
            };
        }

        // 测试创建订单
        async function testCreateOrder() {
            clearResults('orderResults');
            displayResult('orderResults', '📝 测试创建订单...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const orderData = collectFormData();
                
                const result = await apiService.createOrder(orderData);
                
                if (result.success) {
                    displayResult('orderResults', `✅ 订单创建成功: ${result.orderNumber}`, 'success');
                    displayResult('orderResults', `🆔 订单ID: ${result.orderId}`, 'info');
                } else {
                    displayResult('orderResults', `❌ 订单创建失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                displayResult('orderResults', `❌ 创建订单异常: ${error.message}`, 'error');
            }
        }

        // 测试验证订单
        async function testValidateOrder() {
            clearResults('orderResults');
            displayResult('orderResults', '🔍 测试订单验证...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const orderData = collectFormData();
                const processedData = apiService.preprocessOrderData(orderData);
                
                const validation = apiService.validateOrderData(processedData);
                
                if (validation.isValid) {
                    displayResult('orderResults', '✅ 订单数据验证通过', 'success');
                } else {
                    displayResult('orderResults', `❌ 验证失败: ${validation.errors.join(', ')}`, 'error');
                }
                
            } catch (error) {
                displayResult('orderResults', `❌ 验证异常: ${error.message}`, 'error');
            }
        }

        // 测试增强订单
        async function testEnhanceOrder() {
            clearResults('orderResults');
            displayResult('orderResults', '🔧 测试订单增强...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const orderData = collectFormData();
                const processedData = apiService.preprocessOrderData(orderData);
                const enhancedData = apiService.enhanceOrderData(processedData);
                
                displayResult('orderResults', '✅ 订单增强完成', 'success');
                displayResult('orderResults', `增强后的数据:\n${JSON.stringify(enhancedData, null, 2)}`, 'info');
                
            } catch (error) {
                displayResult('orderResults', `❌ 增强异常: ${error.message}`, 'error');
            }
        }

        // 测试订单摘要
        async function testOrderSummary() {
            clearResults('orderResults');
            displayResult('orderResults', '📋 测试订单摘要...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const orderData = collectFormData();
                const processedData = apiService.preprocessOrderData(orderData);
                const enhancedData = apiService.enhanceOrderData(processedData);
                
                const summary = apiService.generateOrderSummary(enhancedData);
                
                displayResult('orderResults', '✅ 订单摘要生成成功', 'success');
                displayResult('orderResults', `摘要:\n${JSON.stringify(summary, null, 2)}`, 'info');
                
            } catch (error) {
                displayResult('orderResults', `❌ 摘要生成异常: ${error.message}`, 'error');
            }
        }

        // 测试批量处理
        async function testBatchProcessing() {
            clearResults('batchResults');
            displayResult('batchResults', '📦 测试批量处理...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                const batchData = JSON.parse(document.getElementById('batchOrderData').value);
                
                const result = await apiService.createMultipleOrders(batchData);
                
                displayResult('batchResults', `✅ 批量处理完成`, 'success');
                displayResult('batchResults', `📊 总数: ${result.total}, 成功: ${result.successful}, 失败: ${result.failed}`, 'info');
                
                if (result.errors.length > 0) {
                    displayResult('batchResults', `❌ 失败订单: ${result.errors.length}个`, 'warning');
                }
                
            } catch (error) {
                displayResult('batchResults', `❌ 批量处理异常: ${error.message}`, 'error');
            }
        }

        // 测试验证错误
        async function testValidationErrors() {
            clearResults('errorResults');
            displayResult('errorResults', '🚨 测试验证错误...', 'info');
            
            try {
                const apiService = getCompleteApiService();
                
                // 创建无效数据
                const invalidData = {
                    customer_email: 'invalid-email',
                    customer_contact: '123',
                    date: 'invalid-date',
                    passenger_number: -1
                };
                
                const validation = apiService.validateOrderData(invalidData);
                
                if (!validation.isValid) {
                    displayResult('errorResults', '✅ 验证错误检测正常', 'success');
                    validation.errors.forEach(error => {
                        displayResult('errorResults', `⚠️ ${error}`, 'warning');
                    });
                } else {
                    displayResult('errorResults', '❌ 验证错误检测失败', 'error');
                }
                
            } catch (error) {
                displayResult('errorResults', `❌ 验证错误测试异常: ${error.message}`, 'error');
            }
        }

        // 运行完整测试
        async function runFullTest() {
            clearResults('fullTestResults');
            displayResult('fullTestResults', '🔄 开始完整测试...', 'info');
            
            try {
                // 按顺序运行所有测试
                await testApiInitialization();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testSystemData();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testSmartRecommendations();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testValidateOrder();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                displayResult('fullTestResults', '🎉 完整测试完成', 'success');
                
            } catch (error) {
                displayResult('fullTestResults', `❌ 完整测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行初始化测试
        window.addEventListener('load', function() {
            setTimeout(testApiInitialization, 1000);
        });
    </script>
</body>
</html>