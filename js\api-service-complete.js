/**
 * GoMyHire API Service - Complete Implementation
 * 完整的API服务实现，确保后续开发完全符合API需求
 * 
 * 功能特性:
 * - 完整的字段验证和映射
 * - 智能默认值生成
 * - 错误处理和重试机制
 * - 数据预处理和格式化
 * - 批量处理支持
 * - 完整的API文档遵循
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器
    function getAppState() {
        return getService('appState');
    }

    function getLogger() {
        return getService('logger');
    }

    function getUtils() {
        return getService('utils');
    }

    /**
     * GoMyHire API Service - 完整实现
     * 基于 docs/GoMyHire-API-Field-Requirements.md 的完整规范
     */
    class CompleteApiService {
        constructor() {
            this.baseURL = 'https://gomyhire.com.my/api';
            this.timeout = 30000; // 30秒超时
            this.maxRetries = 2;   // 最大重试次数
            
            // API端点定义
            this.endpoints = {
                // 认证端点
                login: '/login',
                
                // 系统数据端点 (需要认证)
                backendUsers: '/backend_users',
                subCategories: '/sub_category',
                carTypes: '/car_types',
                drivingRegions: '/driving_regions',
                languages: '/languages',
                
                // 订单端点
                createOrder: '/create_order'  // 不需要认证
            };

            // 静态数据映射 (离线模式支持)
            this.staticData = {
                backendUsers: [
                    { id: 1, name: 'Super Admin', email: '', phone: '', role_id: 1 },
                    { id: 37, name: 'smw', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 310, name: 'Jcy', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 312, name: 'opVenus', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 421, name: 'josua', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 1047, name: 'OP QiJun', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 1181, name: 'Op Karen', email: '<EMAIL>', phone: '', role_id: 2 },
                    { id: 1201, name: 'KK Lucas', email: '<EMAIL>', phone: '', role_id: 2 }
                ],
                
                subCategories: [
                    { id: 2, name: 'Pickup', description: '接机服务' },
                    { id: 3, name: 'Dropoff', description: '送机服务' },
                    { id: 4, name: 'Charter', description: '包车服务' }
                ],
                
                carTypes: [
                    { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3, luggageLimit: 3 },
                    { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5, luggageLimit: 4 },
                    { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7, luggageLimit: 7 },
                    { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6, luggageLimit: 4 },
                    { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3, luggageLimit: 3 },
                    { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10, luggageLimit: 10 },
                    { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12, luggageLimit: 12 },
                    { id: 25, name: '30 Seat Mini Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29, luggageLimit: 29 },
                    { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43, luggageLimit: 43 }
                ],
                
                drivingRegions: [
                    { id: 1, name: 'Kl/selangor (KL)', description: '吉隆坡/雪兰莪' },
                    { id: 2, name: 'Penang (PNG)', description: '槟城' },
                    { id: 4, name: 'Sabah (SBH)', description: '沙巴' },
                    { id: 5, name: 'Singapore (SG)', description: '新加坡' }
                ],
                
                languages: [
                    { id: 2, name: 'English (EN)', code: 'en' },
                    { id: 3, name: 'Malay (MY)', code: 'ms' },
                    { id: 4, name: 'Chinese (CN)', code: 'zh' }
                ]
            };

            // 字段映射规则
            this.fieldMapping = {
                // 系统内部字段名 -> API字段名
                'pickup_location': 'pickup',
                'dropoff_location': 'destination',
                'pickup_date': 'date',
                'pickup_time': 'time',
                'luggage_count': 'luggage_number',
                'passenger_count': 'passenger_number',
                'customerName': 'customer_name',
                'customerContact': 'customer_contact',
                'customerEmail': 'customer_email'
            };

            // 字段验证规则
            this.validationRules = {
                // 必填字段
                required: ['sub_category_id', 'ota_reference_number', 'car_type_id', 'incharge_by_backend_user_id'],
                
                // 格式验证
                email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,
                phone: /^[\\+]?[0-9\\s\\-\\(\\)]{8,}$/,
                date: /^\\d{4}-\\d{2}-\\d{2}$/,
                time: /^([01]\\d|2[0-3]):([0-5]\\d)$/,
                
                // 数值范围
                ranges: {
                    passenger_number: { min: 1, max: 50 },
                    luggage_number: { min: 0, max: 100 },
                    ota_price: { min: 0, max: 99999 },
                    driver_fee: { min: 0, max: 99999 },
                    driver_collect: { min: 0, max: 99999 }
                }
            };

            // 智能识别规则
            this.intelligentRules = {
                // OTA参考号格式识别
                otaPatterns: {
                    'Chong Dealer': [/CD[A-Z0-9]{6,12}/i, /CHONG[A-Z0-9]{4,8}/i],
                    'Klook': [/KL[A-Z0-9]{8,12}/i, /KLOOK[A-Z0-9]{4,8}/i],
                    'KKday': [/KK[A-Z0-9]{8,12}/i, /KKDAY[A-Z0-9]{4,8}/i],
                    'Generic': [/[A-Z]{2,4}[0-9]{6,10}/i, /[A-Z0-9]{8,15}/i]
                },
                
                // 语言检测
                languageDetection: {
                    chinese: /[\\u4e00-\\u9fff\\u3400-\\u4dbf\\uf900-\\ufaff]/,
                    english: /[a-zA-Z]/
                },
                
                // 地点关键词检测
                locationKeywords: {
                    airports: ['KLIA', 'KLIA2', 'Airport', 'Terminal', 'Changi', 'Penang International'],
                    hotels: ['Hotel', 'Resort', 'Inn', 'Lodge', 'Motel'],
                    landmarks: ['Sentral', 'Twin Towers', 'Pavilion', 'KLCC', 'Bukit Bintang']
                }
            };

            // 错误分类
            this.errorTypes = {
                NETWORK_ERROR: 'network_error',
                VALIDATION_ERROR: 'validation_error',
                DUPLICATE_ERROR: 'duplicate_error',
                TIMEOUT_ERROR: 'timeout_error',
                SERVER_ERROR: 'server_error',
                AUTH_ERROR: 'auth_error'
            };

            // 初始化
            this.init();
        }

        /**
         * 初始化API服务
         */
        async init() {
            try {
                getLogger().info('API服务初始化开始');
                
                // 加载系统数据
                await this.loadSystemData();
                
                getLogger().info('API服务初始化完成');
            } catch (error) {
                getLogger().error('API服务初始化失败', error);
                // 使用静态数据作为后备
                this.loadStaticData();
            }
        }

        /**
         * 加载系统数据
         */
        async loadSystemData() {
            try {
                const token = getAppState().get('auth.token');
                if (!token) {
                    throw new Error('未找到认证令牌');
                }

                const systemData = {
                    backendUsers: await this.fetchSystemData('backendUsers', token),
                    subCategories: await this.fetchSystemData('subCategories', token),
                    carTypes: await this.fetchSystemData('carTypes', token),
                    drivingRegions: await this.fetchSystemData('drivingRegions', token),
                    languages: await this.fetchSystemData('languages', token)
                };

                getAppState().setSystemData(systemData);
                getLogger().info('系统数据加载完成');
            } catch (error) {
                getLogger().warn('系统数据加载失败，使用静态数据', error);
                this.loadStaticData();
            }
        }

        /**
         * 加载静态数据
         */
        loadStaticData() {
            getAppState().setSystemData(this.staticData);
            getLogger().info('静态数据加载完成');
        }

        /**
         * 获取系统数据
         */
        async fetchSystemData(dataType, token) {
            const endpoint = this.endpoints[dataType];
            if (!endpoint) {
                throw new Error(`未知的数据类型: ${dataType}`);
            }

            const response = await this.request(endpoint, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            return response.data || [];
        }

        /**
         * 用户认证
         */
        async login(email, password, rememberMe = false) {
            try {
                getLogger().info('用户登录开始', { email });

                const response = await this.request(this.endpoints.login, {
                    method: 'POST',
                    body: JSON.stringify({ email, password }),
                    skipAuth: true
                });

                if (!response.status) {
                    throw new Error(response.message || '登录失败');
                }

                // 处理Token格式 (提取 | 后的部分)
                let actualToken = response.token;
                if (response.token && response.token.includes('|')) {
                    actualToken = response.token.split('|')[1];
                }

                // 创建用户对象
                const user = {
                    email: email,
                    token: actualToken,
                    loginTime: new Date().toISOString()
                };

                // 保存认证状态
                getAppState().setAuth(actualToken, user, rememberMe);

                getLogger().info('用户登录成功', { email, rememberMe });
                return { success: true, user, token: actualToken };

            } catch (error) {
                getLogger().error('用户登录失败', error);
                throw this.handleError(error);
            }
        }

        /**
         * 创建订单 - 完整实现
         */
        async createOrder(orderData) {
            try {
                getLogger().info('开始创建订单', orderData);

                // 1. 数据预处理
                const processedData = this.preprocessOrderData(orderData);

                // 2. 数据验证
                const validation = this.validateOrderData(processedData);
                if (!validation.isValid) {
                    throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
                }

                // 3. 智能字段补全
                const enhancedData = this.enhanceOrderData(processedData);

                // 4. 发送API请求
                const response = await this.request(this.endpoints.createOrder, {
                    method: 'POST',
                    body: JSON.stringify(enhancedData),
                    skipAuth: true // 订单创建不需要认证
                });

                // 5. 处理响应
                if (response.status === true) {
                    const result = {
                        success: true,
                        orderId: response.data.id,
                        orderNumber: response.data.order_id,
                        data: response.data
                    };

                    getLogger().info('订单创建成功', result);
                    return result;
                } else {
                    // 处理不同类型的错误
                    return this.handleOrderCreationError(response);
                }

            } catch (error) {
                getLogger().error('订单创建失败', error);
                throw this.handleError(error);
            }
        }

        /**
         * 批量创建订单
         */
        async createMultipleOrders(ordersData) {
            try {
                getLogger().info('开始批量创建订单', { count: ordersData.length });

                const results = [];
                const errors = [];

                for (let i = 0; i < ordersData.length; i++) {
                    try {
                        const result = await this.createOrder(ordersData[i]);
                        results.push({
                            index: i,
                            success: true,
                            data: result
                        });
                    } catch (error) {
                        errors.push({
                            index: i,
                            success: false,
                            error: error.message,
                            data: ordersData[i]
                        });
                    }
                }

                const summary = {
                    total: ordersData.length,
                    successful: results.length,
                    failed: errors.length,
                    results: results,
                    errors: errors
                };

                getLogger().info('批量创建订单完成', summary);
                return summary;

            } catch (error) {
                getLogger().error('批量创建订单失败', error);
                throw this.handleError(error);
            }
        }

        /**
         * 数据预处理
         */
        preprocessOrderData(orderData) {
            const processed = { ...orderData };

            // 1. 字段名称映射
            Object.keys(this.fieldMapping).forEach(internalName => {
                const apiName = this.fieldMapping[internalName];
                if (processed[internalName] !== undefined) {
                    processed[apiName] = processed[internalName];
                    delete processed[internalName];
                }
            });

            // 2. 数据类型转换
            if (processed.passenger_number) {
                processed.passenger_number = parseInt(processed.passenger_number, 10);
            }
            if (processed.luggage_number) {
                processed.luggage_number = parseInt(processed.luggage_number, 10);
            }
            if (processed.ota_price) {
                processed.ota_price = parseFloat(processed.ota_price);
            }

            // 3. 语言数组转换为对象格式
            if (processed.languages_id_array && Array.isArray(processed.languages_id_array)) {
                const languageObject = {};
                processed.languages_id_array.forEach((id, index) => {
                    languageObject[index.toString()] = id.toString();
                });
                processed.languages_id_array = languageObject;
            }

            // 4. 日期时间格式化
            if (processed.date) {
                processed.date = this.formatDate(processed.date);
            }
            if (processed.time) {
                processed.time = this.formatTime(processed.time);
            }

            return processed;
        }

        /**
         * 数据验证
         */
        validateOrderData(orderData) {
            const errors = [];

            // 1. 必填字段检查
            this.validationRules.required.forEach(field => {
                if (!orderData[field]) {
                    errors.push(`${field} 是必填字段`);
                }
            });

            // 2. 格式验证
            if (orderData.customer_email && !this.validationRules.email.test(orderData.customer_email)) {
                errors.push('客户邮箱格式无效');
            }

            if (orderData.customer_contact && !this.validationRules.phone.test(orderData.customer_contact)) {
                errors.push('客户电话格式无效');
            }

            if (orderData.date && !this.validationRules.date.test(orderData.date)) {
                errors.push('日期格式无效 (应为 YYYY-MM-DD)');
            }

            if (orderData.time && !this.validationRules.time.test(orderData.time)) {
                errors.push('时间格式无效 (应为 HH:MM)');
            }

            // 3. 数值范围验证
            Object.keys(this.validationRules.ranges).forEach(field => {
                const value = orderData[field];
                if (value !== undefined) {
                    const range = this.validationRules.ranges[field];
                    if (value < range.min || value > range.max) {
                        errors.push(`${field} 必须在 ${range.min} 到 ${range.max} 之间`);
                    }
                }
            });

            // 4. 业务逻辑验证
            if (orderData.passenger_number && orderData.car_type_id) {
                const carType = this.staticData.carTypes.find(ct => ct.id === orderData.car_type_id);
                if (carType && orderData.passenger_number > carType.passengerLimit) {
                    errors.push(`乘客数量 (${orderData.passenger_number}) 超过车型限制 (${carType.passengerLimit})`);
                }
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        /**
         * 智能字段补全
         */
        enhanceOrderData(orderData) {
            const enhanced = { ...orderData };

            // 1. 智能默认值生成
            if (!enhanced.incharge_by_backend_user_id) {
                enhanced.incharge_by_backend_user_id = this.getDefaultBackendUserId();
            }

            if (!enhanced.languages_id_array && enhanced.customer_name) {
                enhanced.languages_id_array = this.getDefaultLanguagesArray(enhanced.customer_name);
            }

            if (!enhanced.car_type_id && enhanced.passenger_number) {
                enhanced.car_type_id = this.recommendCarType(enhanced.passenger_number);
            }

            if (!enhanced.sub_category_id && (enhanced.pickup || enhanced.destination)) {
                enhanced.sub_category_id = this.detectServiceType(enhanced.pickup, enhanced.destination);
            }

            // 2. OTA平台检测
            if (enhanced.ota_reference_number && !enhanced.ota) {
                enhanced.ota = this.detectOtaPlatform(enhanced.ota_reference_number);
            }

            // 3. 驾驶区域检测
            if (!enhanced.driving_region_id && enhanced.pickup) {
                enhanced.driving_region_id = this.detectDrivingRegion(enhanced.pickup);
            }

            return enhanced;
        }

        /**
         * 获取默认后台用户ID
         */
        getDefaultBackendUserId() {
            const currentUser = getAppState().get('auth.user');
            
            if (currentUser && currentUser.email) {
                const backendUsers = getAppState().get('systemData.backendUsers') || this.staticData.backendUsers;
                const matched = backendUsers.find(user => 
                    user.email && user.email.toLowerCase() === currentUser.email.toLowerCase()
                );
                if (matched) {
                    return matched.id;
                }
            }
            
            return 1; // 默认Super Admin
        }

        /**
         * 获取默认语言数组
         */
        getDefaultLanguagesArray(customerName = '') {
            const chineseRegex = this.intelligentRules.languageDetection.chinese;
            let languageId = 2; // 默认英语
            
            if (customerName && chineseRegex.test(customerName)) {
                languageId = 4; // 中文
            }
            
            return { "0": languageId.toString() };
        }

        /**
         * 推荐车型
         */
        recommendCarType(passengerCount) {
            const carTypes = this.staticData.carTypes;
            
            // 按乘客容量排序并找到最合适的
            const sortedCarTypes = carTypes
                .filter(ct => ct.passengerLimit >= passengerCount)
                .sort((a, b) => a.passengerLimit - b.passengerLimit);
            
            return sortedCarTypes.length > 0 ? sortedCarTypes[0].id : 5; // 默认5座车
        }

        /**
         * 检测服务类型
         */
        detectServiceType(pickup = '', destination = '') {
            const airportKeywords = this.intelligentRules.locationKeywords.airports;
            
            const pickupIsAirport = airportKeywords.some(keyword => 
                pickup.toLowerCase().includes(keyword.toLowerCase())
            );
            const destinationIsAirport = airportKeywords.some(keyword => 
                destination.toLowerCase().includes(keyword.toLowerCase())
            );
            
            if (pickupIsAirport && !destinationIsAirport) {
                return 2; // 接机
            } else if (!pickupIsAirport && destinationIsAirport) {
                return 3; // 送机
            } else {
                return 4; // 包车
            }
        }

        /**
         * 检测OTA平台
         */
        detectOtaPlatform(referenceNumber) {
            for (const [platform, patterns] of Object.entries(this.intelligentRules.otaPatterns)) {
                if (patterns.some(pattern => pattern.test(referenceNumber))) {
                    return platform;
                }
            }
            return 'Unknown';
        }

        /**
         * 检测驾驶区域
         */
        detectDrivingRegion(location) {
            const location_lower = location.toLowerCase();
            
            if (location_lower.includes('kl') || location_lower.includes('kuala lumpur') || 
                location_lower.includes('selangor') || location_lower.includes('klia')) {
                return 1; // KL/Selangor
            } else if (location_lower.includes('penang') || location_lower.includes('png')) {
                return 2; // Penang
            } else if (location_lower.includes('sabah') || location_lower.includes('sbh')) {
                return 4; // Sabah
            } else if (location_lower.includes('singapore') || location_lower.includes('sg')) {
                return 5; // Singapore
            }
            
            return 1; // 默认KL/Selangor
        }

        /**
         * 处理订单创建错误
         */
        handleOrderCreationError(response) {
            const errorMessage = response.message || '订单创建失败';
            
            // 重复订单检测
            if (errorMessage.toLowerCase().includes('duplicate') || 
                errorMessage.toLowerCase().includes('already exists')) {
                const error = new Error(errorMessage);
                error.type = this.errorTypes.DUPLICATE_ERROR;
                error.isDuplicate = true;
                throw error;
            }
            
            // 验证错误
            if (response.data && response.data.validation_error) {
                const validationErrors = response.data.validation_error;
                const errorMessages = [];
                
                Object.keys(validationErrors).forEach(field => {
                    const fieldErrors = validationErrors[field];
                    if (Array.isArray(fieldErrors)) {
                        errorMessages.push(...fieldErrors);
                    } else {
                        errorMessages.push(fieldErrors);
                    }
                });
                
                return {
                    success: false,
                    errors: validationErrors,
                    message: errorMessages.join('; ')
                };
            }
            
            throw new Error(errorMessage);
        }

        /**
         * 通用错误处理
         */
        handleError(error) {
            if (error.name === 'AbortError') {
                const timeoutError = new Error('请求超时，请稍后重试');
                timeoutError.type = this.errorTypes.TIMEOUT_ERROR;
                return timeoutError;
            }
            
            if (error.message.includes('Failed to fetch')) {
                const networkError = new Error('网络连接失败，请检查网络连接');
                networkError.type = this.errorTypes.NETWORK_ERROR;
                return networkError;
            }
            
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                const authError = new Error('认证失败，请重新登录');
                authError.type = this.errorTypes.AUTH_ERROR;
                return authError;
            }
            
            return error;
        }

        /**
         * 通用HTTP请求方法
         */
        async request(endpoint, options = {}) {
            const url = `${this.baseURL}${endpoint}`;
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);

            try {
                const defaultHeaders = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                };

                // 添加认证头
                if (!options.skipAuth) {
                    const token = getAppState().get('auth.token');
                    if (token) {
                        defaultHeaders['Authorization'] = `Bearer ${token}`;
                    }
                }

                const requestOptions = {
                    method: options.method || 'GET',
                    headers: { ...defaultHeaders, ...options.headers },
                    signal: controller.signal,
                    ...options
                };

                const response = await fetch(url, requestOptions);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data;

            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        }

        /**
         * 格式化日期
         */
        formatDate(date) {
            if (!date) return '';
            
            if (typeof date === 'string' && /^\\d{4}-\\d{2}-\\d{2}$/.test(date)) {
                return date;
            }
            
            const dateObj = new Date(date);
            if (isNaN(dateObj.getTime())) {
                return '';
            }
            
            return dateObj.toISOString().split('T')[0];
        }

        /**
         * 格式化时间
         */
        formatTime(time) {
            if (!time) return '';
            
            if (typeof time === 'string' && /^([01]\\d|2[0-3]):([0-5]\\d)$/.test(time)) {
                return time;
            }
            
            const timeObj = new Date(`2000-01-01T${time}`);
            if (isNaN(timeObj.getTime())) {
                return '';
            }
            
            return timeObj.toTimeString().slice(0, 5);
        }

        /**
         * 获取系统数据
         */
        getSystemData(type) {
            return getAppState().get(`systemData.${type}`) || this.staticData[type] || [];
        }

        /**
         * 获取车型信息
         */
        getCarTypeInfo(carTypeId) {
            const carTypes = this.getSystemData('carTypes');
            return carTypes.find(ct => ct.id === carTypeId);
        }

        /**
         * 获取后台用户信息
         */
        getBackendUserInfo(userId) {
            const backendUsers = this.getSystemData('backendUsers');
            return backendUsers.find(user => user.id === userId);
        }

        /**
         * 获取服务类型信息
         */
        getServiceTypeInfo(subCategoryId) {
            const subCategories = this.getSystemData('subCategories');
            return subCategories.find(sc => sc.id === subCategoryId);
        }

        /**
         * 获取驾驶区域信息
         */
        getDrivingRegionInfo(regionId) {
            const drivingRegions = this.getSystemData('drivingRegions');
            return drivingRegions.find(dr => dr.id === regionId);
        }

        /**
         * 获取语言信息
         */
        getLanguageInfo(languageId) {
            const languages = this.getSystemData('languages');
            return languages.find(lang => lang.id === languageId);
        }

        /**
         * 验证OTA参考号格式
         */
        validateOtaReference(referenceNumber) {
            if (!referenceNumber || typeof referenceNumber !== 'string') {
                return false;
            }
            
            if (referenceNumber.length < 6 || referenceNumber.length > 20) {
                return false;
            }
            
            // 检查是否匹配任何已知格式
            const allPatterns = Object.values(this.intelligentRules.otaPatterns).flat();
            return allPatterns.some(pattern => pattern.test(referenceNumber));
        }

        /**
         * 生成订单摘要
         */
        generateOrderSummary(orderData) {
            const summary = {
                service: this.getServiceTypeInfo(orderData.sub_category_id)?.name || 'Unknown',
                customer: orderData.customer_name || 'Unknown',
                route: `${orderData.pickup || 'Unknown'} → ${orderData.destination || 'Unknown'}`,
                datetime: `${orderData.date || 'Unknown'} ${orderData.time || 'Unknown'}`,
                passengers: orderData.passenger_number || 0,
                luggage: orderData.luggage_number || 0,
                carType: this.getCarTypeInfo(orderData.car_type_id)?.name || 'Unknown',
                price: orderData.ota_price || 0,
                reference: orderData.ota_reference_number || 'Unknown'
            };
            
            return summary;
        }

        /**
         * 健康检查
         */
        async healthCheck() {
            try {
                const response = await this.request('/health', { 
                    method: 'GET',
                    skipAuth: true 
                });
                return { healthy: true, response };
            } catch (error) {
                return { healthy: false, error: error.message };
            }
        }

        /**
         * 获取API使用统计
         */
        getApiStats() {
            return {
                version: '1.0.0',
                lastUpdate: '2025-07-18',
                supportedFeatures: [
                    'Order Creation',
                    'Batch Processing',
                    'Smart Field Detection',
                    'Data Validation',
                    'Error Handling',
                    'Offline Support'
                ],
                fieldCount: {
                    total: 28,
                    required: 4,
                    optional: 24
                },
                validationRules: Object.keys(this.validationRules).length,
                staticDataEntries: Object.keys(this.staticData).reduce((total, key) => {
                    return total + this.staticData[key].length;
                }, 0)
            };
        }
    }

    // 创建单例实例
    let apiServiceInstance = null;

    /**
     * 获取API服务实例 (工厂函数)
     */
    function getCompleteApiService() {
        if (!apiServiceInstance) {
            apiServiceInstance = new CompleteApiService();
        }
        return apiServiceInstance;
    }

    // 导出到全局命名空间
    window.OTA.CompleteApiService = CompleteApiService;
    window.OTA.getCompleteApiService = getCompleteApiService;
    window.getCompleteApiService = getCompleteApiService;

    // 注册到服务定位器
    if (window.OTA && window.OTA.container) {
        window.OTA.container.register('completeApiService', () => getCompleteApiService());
    }

    console.log('✅ Complete API Service loaded successfully');

})();